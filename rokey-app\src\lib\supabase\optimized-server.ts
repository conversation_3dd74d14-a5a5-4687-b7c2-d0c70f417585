// Phase 2A: Optimized Supabase server client with connection pooling and caching
import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { cookies } from 'next/headers';

// Connection pool configuration
const CONNECTION_POOL_CONFIG = {
  maxConnections: 20,
  idleTimeoutMs: 30000,
  connectionTimeoutMs: 10000,
};

// Simple in-memory cache for frequently accessed data
class SimpleCache {
  private cache = new Map<string, { data: any; expires: number }>();
  private readonly defaultTTL = 60000; // 1 minute default TTL

  set(key: string, data: any, ttlMs: number = this.defaultTTL): void {
    this.cache.set(key, {
      data,
      expires: Date.now() + ttlMs
    });
  }

  get(key: string): any | null {
    const item = this.cache.get(key);
    if (!item) return null;
    
    if (Date.now() > item.expires) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data;
  }

  delete(key: string): void {
    this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  // Clean up expired entries
  cleanup(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expires) {
        this.cache.delete(key);
      }
    }
  }
}

// Global cache instance
const globalCache = new SimpleCache();

// Cleanup expired cache entries every 5 minutes
setInterval(() => {
  globalCache.cleanup();
}, 5 * 60 * 1000);

// Enhanced Supabase client with optimizations
export async function createOptimizedSupabaseServerClient() {
  const cookieStore = await cookies();

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          try {
            cookieStore.set({ name, value, ...options });
          } catch (error) {
            // Ignore cookie setting errors in Server Components
          }
        },
        remove(name: string, options: CookieOptions) {
          try {
            cookieStore.set({ name, value: '', ...options });
          } catch (error) {
            // Ignore cookie removal errors in Server Components
          }
        },
      },
      // Phase 2A: Add connection pooling configuration
      db: {
        schema: 'public',
      },
      global: {
        headers: {
          'Connection': 'keep-alive',
          'Keep-Alive': 'timeout=30, max=100',
        },
      },
    }
  );
}

// Cached query wrapper for frequently accessed data
export async function cachedQuery<T>(
  cacheKey: string,
  queryFn: () => Promise<T>,
  ttlMs: number = 60000
): Promise<T> {
  // Try to get from cache first
  const cached = globalCache.get(cacheKey);
  if (cached !== null) {
    return cached;
  }

  // Execute query and cache result
  const result = await queryFn();
  globalCache.set(cacheKey, result, ttlMs);
  
  return result;
}

// Optimized query builders for common patterns
export class OptimizedQueries {
  private supabase: ReturnType<typeof createOptimizedSupabaseServerClient>;

  constructor() {
    this.supabase = createOptimizedSupabaseServerClient();
  }

  // Get custom configs with caching
  async getCustomConfigs(userId?: string) {
    const cacheKey = `custom_configs_${userId || 'all'}`;
    
    return cachedQuery(
      cacheKey,
      async () => {
        let query = this.supabase
          .from('custom_api_configs')
          .select('id, name, created_at, updated_at, routing_strategy')
          .order('created_at', { ascending: false })
          .limit(100);

        if (userId) {
          query = query.eq('user_id', userId);
        }

        const { data, error } = await query;
        if (error) throw error;
        return data || [];
      },
      120000 // 2 minutes cache
    );
  }

  // Get conversation list with optimized queries
  async getConversations(customApiConfigId: string) {
    const cacheKey = `conversations_${customApiConfigId}`;
    
    return cachedQuery(
      cacheKey,
      async () => {
        // Get conversations
        const { data: conversations, error } = await this.supabase
          .from('chat_conversations')
          .select('id, custom_api_config_id, title, created_at, updated_at')
          .eq('custom_api_config_id', customApiConfigId)
          .order('updated_at', { ascending: false })
          .limit(50);

        if (error) throw error;
        if (!conversations || conversations.length === 0) return [];

        // Get message stats efficiently
        const conversationIds = conversations.map(conv => conv.id);
        const { data: messageStats } = await this.supabase
          .from('chat_messages')
          .select('conversation_id, content, created_at, role')
          .in('conversation_id', conversationIds)
          .order('created_at', { ascending: false })
          .limit(conversationIds.length * 2);

        // Process and combine data
        const statsMap = new Map();
        if (messageStats) {
          for (const msg of messageStats) {
            if (!statsMap.has(msg.conversation_id)) {
              statsMap.set(msg.conversation_id, { count: 0, lastMessage: null });
            }
            const stats = statsMap.get(msg.conversation_id);
            stats.count++;
            if (!stats.lastMessage) {
              stats.lastMessage = msg;
            }
          }
        }

        return conversations.map(conv => {
          const stats = statsMap.get(conv.id) || { count: 0, lastMessage: null };
          let lastMessagePreview = 'No messages yet';
          
          if (stats.lastMessage?.content) {
            const content = Array.isArray(stats.lastMessage.content) 
              ? stats.lastMessage.content.find((p: any) => p.type === 'text')?.text || ''
              : stats.lastMessage.content;
            lastMessagePreview = typeof content === 'string' 
              ? content.substring(0, 100) + (content.length > 100 ? '...' : '')
              : 'Media message';
          }

          return {
            id: conv.id,
            custom_api_config_id: conv.custom_api_config_id,
            title: conv.title,
            created_at: conv.created_at,
            updated_at: conv.updated_at,
            message_count: stats.count,
            last_message_preview: lastMessagePreview
          };
        });
      },
      30000 // 30 seconds cache
    );
  }

  // Clear cache for specific keys
  clearCache(pattern?: string) {
    if (pattern) {
      // Clear specific pattern (simple implementation)
      globalCache.clear();
    } else {
      globalCache.clear();
    }
  }
}

// Export the cache for manual management if needed
export { globalCache };

// Backward compatibility - use optimized client by default
export const createSupabaseServerClientOnRequest = createOptimizedSupabaseServerClient;
