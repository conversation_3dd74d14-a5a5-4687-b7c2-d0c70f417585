"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n\nvar _s = $RefreshSig$();\nfunction DashboardPage() {\n    _s();\n    const router = useRouter();\n    const [analyticsData, setAnalyticsData] = useState(null);\n    const [loading, setLoading] = useState(false); // Start with false for progressive loading\n    const [initialLoad, setInitialLoad] = useState(true);\n    const [error, setError] = useState(null);\n    const [recentActivity, setRecentActivity] = useState([]);\n    const [systemStatus, setSystemStatus] = useState([\n        {\n            name: \"API Gateway\",\n            status: \"operational\"\n        },\n        {\n            name: \"Routing Engine\",\n            status: \"operational\"\n        },\n        {\n            name: \"Analytics\",\n            status: \"degraded\"\n        }\n    ]);\n    useEffect(()=>{\n        // Progressive loading: render UI first, then load data\n        const loadData = async ()=>{\n            // Small delay to allow UI to render first\n            await new Promise((resolve)=>setTimeout(resolve, 50));\n            // Load data in parallel for better performance\n            const promises = [\n                fetchAnalyticsData(),\n                fetchRecentActivity(),\n                checkSystemStatus()\n            ];\n            await Promise.allSettled(promises);\n            setInitialLoad(false);\n        };\n        loadData();\n        // Set up auto-refresh for activity feed every 30 seconds\n        const activityInterval = setInterval(fetchRecentActivity, 30000);\n        // Set up system status check every 60 seconds\n        const statusInterval = setInterval(checkSystemStatus, 60000);\n        return ()=>{\n            clearInterval(activityInterval);\n            clearInterval(statusInterval);\n        };\n    }, []);\n    const fetchAnalyticsData = async ()=>{\n        try {\n            // Only show loading on initial load, not on refreshes\n            if (initialLoad) {\n                setLoading(true);\n            }\n            // Get data for the last 30 days\n            const thirtyDaysAgo = new Date();\n            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\n            const response = await fetch(\"/api/analytics/summary?startDate=\".concat(thirtyDaysAgo.toISOString(), \"&groupBy=day\"));\n            if (!response.ok) {\n                throw new Error(\"Failed to fetch analytics data\");\n            }\n            const data = await response.json();\n            setAnalyticsData(data);\n        } catch (err) {\n            setError(err.message);\n            console.error(\"Error fetching analytics:\", err);\n        } finally{\n            if (initialLoad) {\n                setLoading(false);\n            }\n        }\n    };\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\",\n            minimumFractionDigits: 2,\n            maximumFractionDigits: 6\n        }).format(amount);\n    };\n    const formatNumber = (num)=>{\n        return new Intl.NumberFormat(\"en-US\").format(num);\n    };\n    const fetchRecentActivity = async ()=>{\n        try {\n            // Fetch recent activity from the new activity API\n            const response = await fetch(\"/api/activity?limit=10\");\n            if (!response.ok) {\n                throw new Error(\"Failed to fetch recent activity\");\n            }\n            const data = await response.json();\n            const activities = data.activities.map((activity)=>({\n                    id: activity.id,\n                    action: activity.action,\n                    model: activity.model,\n                    time: activity.time,\n                    status: activity.status,\n                    details: activity.details\n                }));\n            setRecentActivity(activities);\n        } catch (err) {\n            console.error(\"Error fetching recent activity:\", err);\n            // Set fallback activity data\n            setRecentActivity([\n                {\n                    id: \"1\",\n                    action: \"System initialized\",\n                    model: \"RoKey\",\n                    time: \"Just now\",\n                    status: \"info\"\n                }\n            ]);\n        }\n    };\n    const checkSystemStatus = async ()=>{\n        try {\n            const response = await fetch(\"/api/system-status\");\n            if (!response.ok) {\n                throw new Error(\"Failed to fetch system status\");\n            }\n            const data = await response.json();\n            const statusItems = data.checks.map((check)=>({\n                    name: check.name,\n                    status: check.status,\n                    lastChecked: new Date(check.lastChecked).toLocaleTimeString()\n                }));\n            setSystemStatus(statusItems);\n        } catch (err) {\n            console.error(\"Error checking system status:\", err);\n            // Set fallback status\n            setSystemStatus([\n                {\n                    name: \"API Gateway\",\n                    status: \"down\",\n                    lastChecked: new Date().toLocaleTimeString()\n                },\n                {\n                    name: \"Routing Engine\",\n                    status: \"down\",\n                    lastChecked: new Date().toLocaleTimeString()\n                },\n                {\n                    name: \"Analytics\",\n                    status: \"down\",\n                    lastChecked: new Date().toLocaleTimeString()\n                }\n            ]);\n        }\n    };\n    const getTimeAgo = (date)=>{\n        const now = new Date();\n        const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n        if (diffInSeconds < 60) return \"Just now\";\n        if (diffInSeconds < 3600) return \"\".concat(Math.floor(diffInSeconds / 60), \" minutes ago\");\n        if (diffInSeconds < 86400) return \"\".concat(Math.floor(diffInSeconds / 3600), \" hours ago\");\n        return \"\".concat(Math.floor(diffInSeconds / 86400), \" days ago\");\n    };\n    // Quick Actions handlers\n    const handleAddNewModel = ()=>{\n        router.push(\"/my-models\");\n    };\n    const handleTestPlayground = ()=>{\n        router.push(\"/playground\");\n    };\n    const handleViewLogs = ()=>{\n        router.push(\"/logs\");\n    };\n    const stats = analyticsData ? [\n        {\n            name: \"Total Requests\",\n            value: formatNumber(analyticsData.summary.total_requests),\n            change: \"Last 30 days\",\n            changeType: \"neutral\",\n            icon: ChartBarIcon\n        },\n        {\n            name: \"Total Cost\",\n            value: formatCurrency(analyticsData.summary.total_cost),\n            change: \"\".concat(formatCurrency(analyticsData.summary.average_cost_per_request), \" avg/request\"),\n            changeType: \"neutral\",\n            icon: CurrencyDollarIcon\n        },\n        {\n            name: \"Success Rate\",\n            value: \"\".concat(analyticsData.summary.success_rate.toFixed(1), \"%\"),\n            change: \"\".concat(formatNumber(analyticsData.summary.successful_requests), \" successful\"),\n            changeType: analyticsData.summary.success_rate >= 95 ? \"positive\" : \"negative\",\n            icon: CheckCircleIcon\n        },\n        {\n            name: \"Total Tokens\",\n            value: formatNumber(analyticsData.summary.total_tokens),\n            change: \"\".concat(formatNumber(analyticsData.summary.total_input_tokens), \" in, \").concat(formatNumber(analyticsData.summary.total_output_tokens), \" out\"),\n            changeType: \"neutral\",\n            icon: CpuChipIcon\n        }\n    ] : [];\n    // Show loading only on initial load, not on subsequent visits\n    if (loading && initialLoad) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 bg-gray-200 rounded w-1/3 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-gray-200 rounded w-1/2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 235,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                    children: [\n                        ...Array(4)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card p-6 animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 rounded w-1/2 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 bg-gray-200 rounded w-3/4 mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-3 bg-gray-200 rounded w-1/3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 234,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-slide-in\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-gray-900 mb-2\",\n                            children: \"Welcome back! \\uD83D\\uDC4B\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 text-lg\",\n                            children: \"Here's what's happening with your LLM infrastructure today.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card p-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-600 mb-4\",\n                            children: [\n                                \"Error loading analytics data: \",\n                                error\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: fetchAnalyticsData,\n                            className: \"btn-primary\",\n                            children: \"Retry\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 263,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 254,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-slide-in\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold text-gray-900 mb-2\",\n                        children: \"Welcome back! \\uD83D\\uDC4B\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 text-lg\",\n                        children: \"Here's what's happening with your LLM infrastructure today.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 279,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 animate-slide-in\",\n                children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card p-6 hover:shadow-lg transition-all duration-200\",\n                        style: {\n                            animationDelay: \"\".concat(index * 100, \"ms\")\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: stat.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-3xl font-bold text-gray-900 mt-2\",\n                                            children: stat.value\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm mt-2 flex items-center \".concat(stat.changeType === \"positive\" ? \"text-green-600\" : stat.changeType === \"negative\" ? \"text-red-600\" : \"text-gray-500\"),\n                                            children: [\n                                                stat.changeType !== \"neutral\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ArrowTrendingUpIcon, {\n                                                    className: \"h-4 w-4 mr-1 \".concat(stat.changeType === \"negative\" ? \"rotate-180\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 21\n                                                }, this),\n                                                stat.change\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 rounded-lg bg-orange-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                        className: \"h-6 w-6 text-orange-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 13\n                        }, this)\n                    }, stat.name, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 289,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-1 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card p-6 animate-slide-in\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                        children: \"Quick Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleAddNewModel,\n                                                className: \"btn-primary w-full justify-center hover:scale-105 transition-transform duration-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlusIcon, {\n                                                        className: \"h-5 w-5 mr-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Add New Model\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleTestPlayground,\n                                                className: \"btn-secondary w-full justify-center hover:scale-105 transition-transform duration-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BeakerIcon, {\n                                                        className: \"h-5 w-5 mr-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Test in Playground\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleViewLogs,\n                                                className: \"btn-outline w-full justify-center hover:scale-105 transition-transform duration-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DocumentTextIcon, {\n                                                        className: \"h-5 w-5 mr-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"View Logs\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card p-6 animate-slide-in\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                        children: \"System Status\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: systemStatus.map((system)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-3 h-3 rounded-full mr-3 \".concat(system.status === \"operational\" ? \"bg-green-500\" : system.status === \"degraded\" ? \"bg-yellow-500\" : \"bg-red-500\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-700\",\n                                                                children: system.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 362,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium \".concat(system.status === \"operational\" ? \"text-green-600\" : system.status === \"degraded\" ? \"text-yellow-600\" : \"text-red-600\"),\n                                                                children: system.status === \"operational\" ? \"Operational\" : system.status === \"degraded\" ? \"Degraded\" : \"Down\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 365,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            system.lastChecked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: system.lastChecked\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 373,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, system.name, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card p-6 animate-slide-in\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900\",\n                                            children: \"Recent Activity\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: fetchRecentActivity,\n                                            className: \"text-orange-600 hover:text-orange-700 text-sm font-medium\",\n                                            children: \"Refresh\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        recentActivity.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClockIcon, {\n                                                    className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500\",\n                                                    children: \"No recent activity\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"Activity will appear here as you use the API\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 17\n                                        }, this) : recentActivity.slice(-4).map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start p-4 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors duration-200 group overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 rounded-full mr-4 \".concat(activity.status === \"success\" ? \"bg-green-500\" : activity.status === \"warning\" ? \"bg-yellow-500\" : activity.status === \"error\" ? \"bg-red-500\" : \"bg-blue-500\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 min-w-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-900 font-medium break-words\",\n                                                                children: activity.action\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 410,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 text-sm break-words\",\n                                                                children: [\n                                                                    activity.model,\n                                                                    \" • \",\n                                                                    activity.time\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            activity.details && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-500 text-xs mt-1 line-clamp-2 leading-relaxed\",\n                                                                title: activity.details,\n                                                                children: activity.details\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500 group-hover:text-gray-700\",\n                                                        children: activity.status === \"error\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ExclamationTriangleIcon, {\n                                                            className: \"h-5 w-5 text-red-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 420,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CpuChipIcon, {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 422,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, activity.id, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 19\n                                            }, this)),\n                                        recentActivity.length > 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 pt-4 border-t border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    // Navigate to logs page to see all activity\n                                                    window.location.href = \"/logs\";\n                                                },\n                                                className: \"text-sm text-orange-600 hover:text-orange-700 font-medium flex items-center justify-center w-full py-2 hover:bg-orange-50 rounded-lg transition-colors\",\n                                                children: [\n                                                    \"View All Activity (\",\n                                                    recentActivity.length,\n                                                    \")\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 ml-1\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9 5l7 7-7 7\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 441,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 440,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 384,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 321,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 277,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"Rh7snW/qICzjtCa+0YqVUHIGty8=\", true);\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZGFzaGJvYXJkL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFxQ2UsU0FBU0E7O0lBQ3RCLE1BQU1DLFNBQVNDO0lBQ2YsTUFBTSxDQUFDQyxlQUFlQyxpQkFBaUIsR0FBR0MsU0FBK0I7SUFDekUsTUFBTSxDQUFDQyxTQUFTQyxXQUFXLEdBQUdGLFNBQVMsUUFBUSwyQ0FBMkM7SUFDMUYsTUFBTSxDQUFDRyxhQUFhQyxlQUFlLEdBQUdKLFNBQVM7SUFDL0MsTUFBTSxDQUFDSyxPQUFPQyxTQUFTLEdBQUdOLFNBQXdCO0lBQ2xELE1BQU0sQ0FBQ08sZ0JBQWdCQyxrQkFBa0IsR0FBR1IsU0FBK0IsRUFBRTtJQUM3RSxNQUFNLENBQUNTLGNBQWNDLGdCQUFnQixHQUFHVixTQUE2QjtRQUNuRTtZQUFFVyxNQUFNO1lBQWVDLFFBQVE7UUFBYztRQUM3QztZQUFFRCxNQUFNO1lBQWtCQyxRQUFRO1FBQWM7UUFDaEQ7WUFBRUQsTUFBTTtZQUFhQyxRQUFRO1FBQVc7S0FDekM7SUFFREMsVUFBVTtRQUNSLHVEQUF1RDtRQUN2RCxNQUFNQyxXQUFXO1lBQ2YsMENBQTBDO1lBQzFDLE1BQU0sSUFBSUMsUUFBUUMsQ0FBQUEsVUFBV0MsV0FBV0QsU0FBUztZQUVqRCwrQ0FBK0M7WUFDL0MsTUFBTUUsV0FBVztnQkFDZkM7Z0JBQ0FDO2dCQUNBQzthQUNEO1lBRUQsTUFBTU4sUUFBUU8sVUFBVSxDQUFDSjtZQUN6QmQsZUFBZTtRQUNqQjtRQUVBVTtRQUVBLHlEQUF5RDtRQUN6RCxNQUFNUyxtQkFBbUJDLFlBQVlKLHFCQUFxQjtRQUUxRCw4Q0FBOEM7UUFDOUMsTUFBTUssaUJBQWlCRCxZQUFZSCxtQkFBbUI7UUFFdEQsT0FBTztZQUNMSyxjQUFjSDtZQUNkRyxjQUFjRDtRQUNoQjtJQUNGLEdBQUcsRUFBRTtJQUVMLE1BQU1OLHFCQUFxQjtRQUN6QixJQUFJO1lBQ0Ysc0RBQXNEO1lBQ3RELElBQUloQixhQUFhO2dCQUNmRCxXQUFXO1lBQ2I7WUFFQSxnQ0FBZ0M7WUFDaEMsTUFBTXlCLGdCQUFnQixJQUFJQztZQUMxQkQsY0FBY0UsT0FBTyxDQUFDRixjQUFjRyxPQUFPLEtBQUs7WUFFaEQsTUFBTUMsV0FBVyxNQUFNQyxNQUFNLG9DQUFnRSxPQUE1QkwsY0FBY00sV0FBVyxJQUFHO1lBQzdGLElBQUksQ0FBQ0YsU0FBU0csRUFBRSxFQUFFO2dCQUNoQixNQUFNLElBQUlDLE1BQU07WUFDbEI7WUFFQSxNQUFNQyxPQUFPLE1BQU1MLFNBQVNNLElBQUk7WUFDaEN0QyxpQkFBaUJxQztRQUNuQixFQUFFLE9BQU9FLEtBQVU7WUFDakJoQyxTQUFTZ0MsSUFBSUMsT0FBTztZQUNwQkMsUUFBUW5DLEtBQUssQ0FBQyw2QkFBNkJpQztRQUM3QyxTQUFVO1lBQ1IsSUFBSW5DLGFBQWE7Z0JBQ2ZELFdBQVc7WUFDYjtRQUNGO0lBQ0Y7SUFFQSxNQUFNdUMsaUJBQWlCLENBQUNDO1FBQ3RCLE9BQU8sSUFBSUMsS0FBS0MsWUFBWSxDQUFDLFNBQVM7WUFDcENDLE9BQU87WUFDUEMsVUFBVTtZQUNWQyx1QkFBdUI7WUFDdkJDLHVCQUF1QjtRQUN6QixHQUFHQyxNQUFNLENBQUNQO0lBQ1o7SUFFQSxNQUFNUSxlQUFlLENBQUNDO1FBQ3BCLE9BQU8sSUFBSVIsS0FBS0MsWUFBWSxDQUFDLFNBQVNLLE1BQU0sQ0FBQ0U7SUFDL0M7SUFFQSxNQUFNL0Isc0JBQXNCO1FBQzFCLElBQUk7WUFDRixrREFBa0Q7WUFDbEQsTUFBTVcsV0FBVyxNQUFNQyxNQUFNO1lBQzdCLElBQUksQ0FBQ0QsU0FBU0csRUFBRSxFQUFFO2dCQUNoQixNQUFNLElBQUlDLE1BQU07WUFDbEI7WUFFQSxNQUFNQyxPQUFPLE1BQU1MLFNBQVNNLElBQUk7WUFDaEMsTUFBTWUsYUFBbUNoQixLQUFLZ0IsVUFBVSxDQUFDQyxHQUFHLENBQUMsQ0FBQ0MsV0FBbUI7b0JBQy9FQyxJQUFJRCxTQUFTQyxFQUFFO29CQUNmQyxRQUFRRixTQUFTRSxNQUFNO29CQUN2QkMsT0FBT0gsU0FBU0csS0FBSztvQkFDckJDLE1BQU1KLFNBQVNJLElBQUk7b0JBQ25COUMsUUFBUTBDLFNBQVMxQyxNQUFNO29CQUN2QitDLFNBQVNMLFNBQVNLLE9BQU87Z0JBQzNCO1lBRUFuRCxrQkFBa0I0QztRQUNwQixFQUFFLE9BQU9kLEtBQVU7WUFDakJFLFFBQVFuQyxLQUFLLENBQUMsbUNBQW1DaUM7WUFDakQsNkJBQTZCO1lBQzdCOUIsa0JBQWtCO2dCQUNoQjtvQkFBRStDLElBQUk7b0JBQUtDLFFBQVE7b0JBQXNCQyxPQUFPO29CQUFTQyxNQUFNO29CQUFZOUMsUUFBUTtnQkFBTzthQUMzRjtRQUNIO0lBQ0Y7SUFFQSxNQUFNUyxvQkFBb0I7UUFDeEIsSUFBSTtZQUNGLE1BQU1VLFdBQVcsTUFBTUMsTUFBTTtZQUM3QixJQUFJLENBQUNELFNBQVNHLEVBQUUsRUFBRTtnQkFDaEIsTUFBTSxJQUFJQyxNQUFNO1lBQ2xCO1lBRUEsTUFBTUMsT0FBTyxNQUFNTCxTQUFTTSxJQUFJO1lBQ2hDLE1BQU11QixjQUFrQ3hCLEtBQUt5QixNQUFNLENBQUNSLEdBQUcsQ0FBQyxDQUFDUyxRQUFnQjtvQkFDdkVuRCxNQUFNbUQsTUFBTW5ELElBQUk7b0JBQ2hCQyxRQUFRa0QsTUFBTWxELE1BQU07b0JBQ3BCbUQsYUFBYSxJQUFJbkMsS0FBS2tDLE1BQU1DLFdBQVcsRUFBRUMsa0JBQWtCO2dCQUM3RDtZQUVBdEQsZ0JBQWdCa0Q7UUFDbEIsRUFBRSxPQUFPdEIsS0FBSztZQUNaRSxRQUFRbkMsS0FBSyxDQUFDLGlDQUFpQ2lDO1lBQy9DLHNCQUFzQjtZQUN0QjVCLGdCQUFnQjtnQkFDZDtvQkFBRUMsTUFBTTtvQkFBZUMsUUFBUTtvQkFBUW1ELGFBQWEsSUFBSW5DLE9BQU9vQyxrQkFBa0I7Z0JBQUc7Z0JBQ3BGO29CQUFFckQsTUFBTTtvQkFBa0JDLFFBQVE7b0JBQVFtRCxhQUFhLElBQUluQyxPQUFPb0Msa0JBQWtCO2dCQUFHO2dCQUN2RjtvQkFBRXJELE1BQU07b0JBQWFDLFFBQVE7b0JBQVFtRCxhQUFhLElBQUluQyxPQUFPb0Msa0JBQWtCO2dCQUFHO2FBQ25GO1FBQ0g7SUFDRjtJQUVBLE1BQU1DLGFBQWEsQ0FBQ0M7UUFDbEIsTUFBTUMsTUFBTSxJQUFJdkM7UUFDaEIsTUFBTXdDLGdCQUFnQkMsS0FBS0MsS0FBSyxDQUFDLENBQUNILElBQUlJLE9BQU8sS0FBS0wsS0FBS0ssT0FBTyxFQUFDLElBQUs7UUFFcEUsSUFBSUgsZ0JBQWdCLElBQUksT0FBTztRQUMvQixJQUFJQSxnQkFBZ0IsTUFBTSxPQUFPLEdBQWtDLE9BQS9CQyxLQUFLQyxLQUFLLENBQUNGLGdCQUFnQixLQUFJO1FBQ25FLElBQUlBLGdCQUFnQixPQUFPLE9BQU8sR0FBb0MsT0FBakNDLEtBQUtDLEtBQUssQ0FBQ0YsZ0JBQWdCLE9BQU07UUFDdEUsT0FBTyxHQUFxQyxPQUFsQ0MsS0FBS0MsS0FBSyxDQUFDRixnQkFBZ0IsUUFBTztJQUM5QztJQUVBLHlCQUF5QjtJQUN6QixNQUFNSSxvQkFBb0I7UUFDeEI1RSxPQUFPNkUsSUFBSSxDQUFDO0lBQ2Q7SUFFQSxNQUFNQyx1QkFBdUI7UUFDM0I5RSxPQUFPNkUsSUFBSSxDQUFDO0lBQ2Q7SUFFQSxNQUFNRSxpQkFBaUI7UUFDckIvRSxPQUFPNkUsSUFBSSxDQUFDO0lBQ2Q7SUFFQSxNQUFNRyxRQUFROUUsZ0JBQWdCO1FBQzVCO1lBQ0VhLE1BQU07WUFDTmtFLE9BQU8zQixhQUFhcEQsY0FBY2dGLE9BQU8sQ0FBQ0MsY0FBYztZQUN4REMsUUFBUTtZQUNSQyxZQUFZO1lBQ1pDLE1BQU1DO1FBQ1I7UUFDQTtZQUNFeEUsTUFBTTtZQUNOa0UsT0FBT3BDLGVBQWUzQyxjQUFjZ0YsT0FBTyxDQUFDTSxVQUFVO1lBQ3RESixRQUFRLEdBQWtFLE9BQS9EdkMsZUFBZTNDLGNBQWNnRixPQUFPLENBQUNPLHdCQUF3QixHQUFFO1lBQzFFSixZQUFZO1lBQ1pDLE1BQU1JO1FBQ1I7UUFDQTtZQUNFM0UsTUFBTTtZQUNOa0UsT0FBTyxHQUFpRCxPQUE5Qy9FLGNBQWNnRixPQUFPLENBQUNTLFlBQVksQ0FBQ0MsT0FBTyxDQUFDLElBQUc7WUFDeERSLFFBQVEsR0FBMkQsT0FBeEQ5QixhQUFhcEQsY0FBY2dGLE9BQU8sQ0FBQ1csbUJBQW1CLEdBQUU7WUFDbkVSLFlBQVluRixjQUFjZ0YsT0FBTyxDQUFDUyxZQUFZLElBQUksS0FBSyxhQUFhO1lBQ3BFTCxNQUFNUTtRQUNSO1FBQ0E7WUFDRS9FLE1BQU07WUFDTmtFLE9BQU8zQixhQUFhcEQsY0FBY2dGLE9BQU8sQ0FBQ2EsWUFBWTtZQUN0RFgsUUFBUSxHQUFpRTlCLE9BQTlEQSxhQUFhcEQsY0FBY2dGLE9BQU8sQ0FBQ2Msa0JBQWtCLEdBQUUsU0FBK0QsT0FBeEQxQyxhQUFhcEQsY0FBY2dGLE9BQU8sQ0FBQ2UsbUJBQW1CLEdBQUU7WUFDaklaLFlBQVk7WUFDWkMsTUFBTVk7UUFDUjtLQUNELEdBQUcsRUFBRTtJQUVOLDhEQUE4RDtJQUM5RCxJQUFJN0YsV0FBV0UsYUFBYTtRQUMxQixxQkFDRSw4REFBQzRGO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVOzs7Ozs7c0NBQ2YsOERBQUNEOzRCQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7OEJBRWpCLDhEQUFDRDtvQkFBSUMsV0FBVTs4QkFDWjsyQkFBSUMsTUFBTTtxQkFBRyxDQUFDNUMsR0FBRyxDQUFDLENBQUM2QyxHQUFHQyxrQkFDckIsOERBQUNKOzRCQUFZQyxXQUFVOzs4Q0FDckIsOERBQUNEO29DQUFJQyxXQUFVOzs7Ozs7OENBQ2YsOERBQUNEO29DQUFJQyxXQUFVOzs7Ozs7OENBQ2YsOERBQUNEO29DQUFJQyxXQUFVOzs7Ozs7OzJCQUhQRzs7Ozs7Ozs7Ozs7Ozs7OztJQVNwQjtJQUVBLElBQUk5RixPQUFPO1FBQ1QscUJBQ0UsOERBQUMwRjtZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDSTs0QkFBR0osV0FBVTtzQ0FBd0M7Ozs7OztzQ0FHdEQsOERBQUNLOzRCQUFFTCxXQUFVO3NDQUF3Qjs7Ozs7Ozs7Ozs7OzhCQUl2Qyw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDSzs0QkFBRUwsV0FBVTs7Z0NBQW9CO2dDQUErQjNGOzs7Ozs7O3NDQUNoRSw4REFBQ2lHOzRCQUNDQyxTQUFTcEY7NEJBQ1Q2RSxXQUFVO3NDQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFNVDtJQUVBLHFCQUNFLDhEQUFDRDtRQUFJQyxXQUFVOzswQkFFYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDSTt3QkFBR0osV0FBVTtrQ0FBd0M7Ozs7OztrQ0FHdEQsOERBQUNLO3dCQUFFTCxXQUFVO2tDQUF3Qjs7Ozs7Ozs7Ozs7OzBCQU12Qyw4REFBQ0Q7Z0JBQUlDLFdBQVU7MEJBQ1pwQixNQUFNdkIsR0FBRyxDQUFDLENBQUNtRCxNQUFNQyxzQkFDaEIsOERBQUNWO3dCQUVDQyxXQUFVO3dCQUNWbkQsT0FBTzs0QkFBRTZELGdCQUFnQixHQUFlLE9BQVpELFFBQVEsS0FBSTt3QkFBSTtrQ0FFNUMsNEVBQUNWOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7O3NEQUNDLDhEQUFDTTs0Q0FBRUwsV0FBVTtzREFBcUNRLEtBQUs3RixJQUFJOzs7Ozs7c0RBQzNELDhEQUFDMEY7NENBQUVMLFdBQVU7c0RBQXlDUSxLQUFLM0IsS0FBSzs7Ozs7O3NEQUNoRSw4REFBQ3dCOzRDQUFFTCxXQUFXLGtDQUdiLE9BRkNRLEtBQUt2QixVQUFVLEtBQUssYUFBYSxtQkFDakN1QixLQUFLdkIsVUFBVSxLQUFLLGFBQWEsaUJBQWlCOztnREFFakR1QixLQUFLdkIsVUFBVSxLQUFLLDJCQUNuQiw4REFBQzBCO29EQUFvQlgsV0FBVyxnQkFFL0IsT0FEQ1EsS0FBS3ZCLFVBQVUsS0FBSyxhQUFhLGVBQWU7Ozs7OztnREFHbkR1QixLQUFLeEIsTUFBTTs7Ozs7Ozs7Ozs7Ozs4Q0FHaEIsOERBQUNlO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDUSxLQUFLdEIsSUFBSTt3Q0FBQ2MsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7dUJBckJwQlEsS0FBSzdGLElBQUk7Ozs7Ozs7Ozs7MEJBNkJwQiw4REFBQ29GO2dCQUFJQyxXQUFVOztrQ0FFYiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNZO3dDQUFHWixXQUFVO2tEQUEyQzs7Ozs7O2tEQUN6RCw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDTTtnREFDQ0MsU0FBUy9CO2dEQUNUd0IsV0FBVTs7a0VBRVYsOERBQUNhO3dEQUFTYixXQUFVOzs7Ozs7b0RBQWlCOzs7Ozs7OzBEQUd2Qyw4REFBQ007Z0RBQ0NDLFNBQVM3QjtnREFDVHNCLFdBQVU7O2tFQUVWLDhEQUFDYzt3REFBV2QsV0FBVTs7Ozs7O29EQUFpQjs7Ozs7OzswREFHekMsOERBQUNNO2dEQUNDQyxTQUFTNUI7Z0RBQ1RxQixXQUFVOztrRUFFViw4REFBQ2U7d0RBQWlCZixXQUFVOzs7Ozs7b0RBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU9uRCw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDWTt3Q0FBR1osV0FBVTtrREFBMkM7Ozs7OztrREFDekQsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUNadkYsYUFBYTRDLEdBQUcsQ0FBQyxDQUFDMkQsdUJBQ2pCLDhEQUFDakI7Z0RBQXNCQyxXQUFVOztrRUFDL0IsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVcsNkJBR2YsT0FGQ2dCLE9BQU9wRyxNQUFNLEtBQUssZ0JBQWdCLGlCQUNsQ29HLE9BQU9wRyxNQUFNLEtBQUssYUFBYSxrQkFBa0I7Ozs7OzswRUFFbkQsOERBQUNxRztnRUFBS2pCLFdBQVU7MEVBQWlCZ0IsT0FBT3JHLElBQUk7Ozs7Ozs7Ozs7OztrRUFFOUMsOERBQUNvRjt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNpQjtnRUFBS2pCLFdBQVcsdUJBR2hCLE9BRkNnQixPQUFPcEcsTUFBTSxLQUFLLGdCQUFnQixtQkFDbENvRyxPQUFPcEcsTUFBTSxLQUFLLGFBQWEsb0JBQW9COzBFQUVsRG9HLE9BQU9wRyxNQUFNLEtBQUssZ0JBQWdCLGdCQUNsQ29HLE9BQU9wRyxNQUFNLEtBQUssYUFBYSxhQUFhOzs7Ozs7NERBRTlDb0csT0FBT2pELFdBQVcsa0JBQ2pCLDhEQUFDc0M7Z0VBQUVMLFdBQVU7MEVBQXlCZ0IsT0FBT2pELFdBQVc7Ozs7Ozs7Ozs7Ozs7K0NBakJwRGlELE9BQU9yRyxJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQTJCN0IsOERBQUNvRjt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNZOzRDQUFHWixXQUFVO3NEQUFzQzs7Ozs7O3NEQUNwRCw4REFBQ007NENBQ0NDLFNBQVNuRjs0Q0FDVDRFLFdBQVU7c0RBQ1g7Ozs7Ozs7Ozs7Ozs4Q0FJSCw4REFBQ0Q7b0NBQUlDLFdBQVU7O3dDQUNaekYsZUFBZTJHLE1BQU0sS0FBSyxrQkFDekIsOERBQUNuQjs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNtQjtvREFBVW5CLFdBQVU7Ozs7Ozs4REFDckIsOERBQUNLO29EQUFFTCxXQUFVOzhEQUFnQjs7Ozs7OzhEQUM3Qiw4REFBQ0s7b0RBQUVMLFdBQVU7OERBQXdCOzs7Ozs7Ozs7OzttREFHdkN6RixlQUFlNkcsS0FBSyxDQUFDLENBQUMsR0FBRy9ELEdBQUcsQ0FBQyxDQUFDQyx5QkFDNUIsOERBQUN5QztnREFBc0JDLFdBQVU7O2tFQUMvQiw4REFBQ0Q7d0RBQUlDLFdBQVcsNkJBSWYsT0FIQzFDLFNBQVMxQyxNQUFNLEtBQUssWUFBWSxpQkFDaEMwQyxTQUFTMUMsTUFBTSxLQUFLLFlBQVksa0JBQ2hDMEMsU0FBUzFDLE1BQU0sS0FBSyxVQUFVLGVBQWU7Ozs7OztrRUFFL0MsOERBQUNtRjt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNLO2dFQUFFTCxXQUFVOzBFQUF5QzFDLFNBQVNFLE1BQU07Ozs7OzswRUFDckUsOERBQUM2QztnRUFBRUwsV0FBVTs7b0VBQXFDMUMsU0FBU0csS0FBSztvRUFBQztvRUFBSUgsU0FBU0ksSUFBSTs7Ozs7Ozs0REFDakZKLFNBQVNLLE9BQU8sa0JBQ2YsOERBQUMwQztnRUFBRUwsV0FBVTtnRUFBMERxQixPQUFPL0QsU0FBU0ssT0FBTzswRUFDM0ZMLFNBQVNLLE9BQU87Ozs7Ozs7Ozs7OztrRUFJdkIsOERBQUNvQzt3REFBSUMsV0FBVTtrRUFDWjFDLFNBQVMxQyxNQUFNLEtBQUssd0JBQ25CLDhEQUFDMEc7NERBQXdCdEIsV0FBVTs7Ozs7aUZBRW5DLDhEQUFDRjs0REFBWUUsV0FBVTs7Ozs7Ozs7Ozs7OytDQW5CbkIxQyxTQUFTQyxFQUFFOzs7Ozt3Q0EyQnhCaEQsZUFBZTJHLE1BQU0sR0FBRyxtQkFDdkIsOERBQUNuQjs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ007Z0RBQ0NDLFNBQVM7b0RBQ1AsNENBQTRDO29EQUM1Q2dCLE9BQU9DLFFBQVEsQ0FBQ0MsSUFBSSxHQUFHO2dEQUN6QjtnREFDQXpCLFdBQVU7O29EQUNYO29EQUNxQnpGLGVBQWUyRyxNQUFNO29EQUFDO2tFQUMxQyw4REFBQ1E7d0RBQUkxQixXQUFVO3dEQUFlMkIsTUFBSzt3REFBT0MsUUFBTzt3REFBZUMsU0FBUTtrRUFDdEUsNEVBQUNDOzREQUFLQyxlQUFjOzREQUFRQyxnQkFBZTs0REFBUUMsYUFBYTs0REFBR0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBVzNGO0dBOVp3QnZJO0tBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZGFzaGJvYXJkL3BhZ2UudHN4P2MxNWUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgU3VzcGVuc2UgfSBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCBNYWluQ29udGVudCBmcm9tICdAL2NvbXBvbmVudHMvTWFpbkNvbnRlbnQnO1xyXG5pbXBvcnQgUG9ydGtleURhc2hib2FyZCBmcm9tICdAL2NvbXBvbmVudHMvUG9ydGtleURhc2hib2FyZCc7XHJcbmltcG9ydCBMb2FkaW5nU3Bpbm5lciBmcm9tICdAL2NvbXBvbmVudHMvTG9hZGluZ1NwaW5uZXInO1xyXG5cclxuaW50ZXJmYWNlIEFuYWx5dGljc1N1bW1hcnkge1xyXG4gIHRvdGFsX3JlcXVlc3RzOiBudW1iZXI7XHJcbiAgc3VjY2Vzc2Z1bF9yZXF1ZXN0czogbnVtYmVyO1xyXG4gIHN1Y2Nlc3NfcmF0ZTogbnVtYmVyO1xyXG4gIHRvdGFsX2Nvc3Q6IG51bWJlcjtcclxuICB0b3RhbF9pbnB1dF90b2tlbnM6IG51bWJlcjtcclxuICB0b3RhbF9vdXRwdXRfdG9rZW5zOiBudW1iZXI7XHJcbiAgdG90YWxfdG9rZW5zOiBudW1iZXI7XHJcbiAgYXZlcmFnZV9jb3N0X3Blcl9yZXF1ZXN0OiBudW1iZXI7XHJcbn1cclxuXHJcbmludGVyZmFjZSBBbmFseXRpY3NEYXRhIHtcclxuICBzdW1tYXJ5OiBBbmFseXRpY3NTdW1tYXJ5O1xyXG4gIGdyb3VwZWRfZGF0YTogYW55W107XHJcbiAgZmlsdGVyczogYW55O1xyXG59XHJcblxyXG5pbnRlcmZhY2UgUmVjZW50QWN0aXZpdHlJdGVtIHtcclxuICBpZDogc3RyaW5nO1xyXG4gIGFjdGlvbjogc3RyaW5nO1xyXG4gIG1vZGVsOiBzdHJpbmc7XHJcbiAgdGltZTogc3RyaW5nO1xyXG4gIHN0YXR1czogJ3N1Y2Nlc3MnIHwgJ3dhcm5pbmcnIHwgJ2Vycm9yJyB8ICdpbmZvJztcclxuICBkZXRhaWxzPzogc3RyaW5nO1xyXG59XHJcblxyXG5pbnRlcmZhY2UgU3lzdGVtU3RhdHVzSXRlbSB7XHJcbiAgbmFtZTogc3RyaW5nO1xyXG4gIHN0YXR1czogJ29wZXJhdGlvbmFsJyB8ICdkZWdyYWRlZCcgfCAnZG93bic7XHJcbiAgbGFzdENoZWNrZWQ/OiBzdHJpbmc7XHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIERhc2hib2FyZFBhZ2UoKSB7XHJcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XHJcbiAgY29uc3QgW2FuYWx5dGljc0RhdGEsIHNldEFuYWx5dGljc0RhdGFdID0gdXNlU3RhdGU8QW5hbHl0aWNzRGF0YSB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTsgLy8gU3RhcnQgd2l0aCBmYWxzZSBmb3IgcHJvZ3Jlc3NpdmUgbG9hZGluZ1xyXG4gIGNvbnN0IFtpbml0aWFsTG9hZCwgc2V0SW5pdGlhbExvYWRdID0gdXNlU3RhdGUodHJ1ZSk7XHJcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBbcmVjZW50QWN0aXZpdHksIHNldFJlY2VudEFjdGl2aXR5XSA9IHVzZVN0YXRlPFJlY2VudEFjdGl2aXR5SXRlbVtdPihbXSk7XHJcbiAgY29uc3QgW3N5c3RlbVN0YXR1cywgc2V0U3lzdGVtU3RhdHVzXSA9IHVzZVN0YXRlPFN5c3RlbVN0YXR1c0l0ZW1bXT4oW1xyXG4gICAgeyBuYW1lOiAnQVBJIEdhdGV3YXknLCBzdGF0dXM6ICdvcGVyYXRpb25hbCcgfSxcclxuICAgIHsgbmFtZTogJ1JvdXRpbmcgRW5naW5lJywgc3RhdHVzOiAnb3BlcmF0aW9uYWwnIH0sXHJcbiAgICB7IG5hbWU6ICdBbmFseXRpY3MnLCBzdGF0dXM6ICdkZWdyYWRlZCcgfVxyXG4gIF0pO1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgLy8gUHJvZ3Jlc3NpdmUgbG9hZGluZzogcmVuZGVyIFVJIGZpcnN0LCB0aGVuIGxvYWQgZGF0YVxyXG4gICAgY29uc3QgbG9hZERhdGEgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgIC8vIFNtYWxsIGRlbGF5IHRvIGFsbG93IFVJIHRvIHJlbmRlciBmaXJzdFxyXG4gICAgICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgNTApKTtcclxuXHJcbiAgICAgIC8vIExvYWQgZGF0YSBpbiBwYXJhbGxlbCBmb3IgYmV0dGVyIHBlcmZvcm1hbmNlXHJcbiAgICAgIGNvbnN0IHByb21pc2VzID0gW1xyXG4gICAgICAgIGZldGNoQW5hbHl0aWNzRGF0YSgpLFxyXG4gICAgICAgIGZldGNoUmVjZW50QWN0aXZpdHkoKSxcclxuICAgICAgICBjaGVja1N5c3RlbVN0YXR1cygpXHJcbiAgICAgIF07XHJcblxyXG4gICAgICBhd2FpdCBQcm9taXNlLmFsbFNldHRsZWQocHJvbWlzZXMpO1xyXG4gICAgICBzZXRJbml0aWFsTG9hZChmYWxzZSk7XHJcbiAgICB9O1xyXG5cclxuICAgIGxvYWREYXRhKCk7XHJcblxyXG4gICAgLy8gU2V0IHVwIGF1dG8tcmVmcmVzaCBmb3IgYWN0aXZpdHkgZmVlZCBldmVyeSAzMCBzZWNvbmRzXHJcbiAgICBjb25zdCBhY3Rpdml0eUludGVydmFsID0gc2V0SW50ZXJ2YWwoZmV0Y2hSZWNlbnRBY3Rpdml0eSwgMzAwMDApO1xyXG5cclxuICAgIC8vIFNldCB1cCBzeXN0ZW0gc3RhdHVzIGNoZWNrIGV2ZXJ5IDYwIHNlY29uZHNcclxuICAgIGNvbnN0IHN0YXR1c0ludGVydmFsID0gc2V0SW50ZXJ2YWwoY2hlY2tTeXN0ZW1TdGF0dXMsIDYwMDAwKTtcclxuXHJcbiAgICByZXR1cm4gKCkgPT4ge1xyXG4gICAgICBjbGVhckludGVydmFsKGFjdGl2aXR5SW50ZXJ2YWwpO1xyXG4gICAgICBjbGVhckludGVydmFsKHN0YXR1c0ludGVydmFsKTtcclxuICAgIH07XHJcbiAgfSwgW10pO1xyXG5cclxuICBjb25zdCBmZXRjaEFuYWx5dGljc0RhdGEgPSBhc3luYyAoKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICAvLyBPbmx5IHNob3cgbG9hZGluZyBvbiBpbml0aWFsIGxvYWQsIG5vdCBvbiByZWZyZXNoZXNcclxuICAgICAgaWYgKGluaXRpYWxMb2FkKSB7XHJcbiAgICAgICAgc2V0TG9hZGluZyh0cnVlKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gR2V0IGRhdGEgZm9yIHRoZSBsYXN0IDMwIGRheXNcclxuICAgICAgY29uc3QgdGhpcnR5RGF5c0FnbyA9IG5ldyBEYXRlKCk7XHJcbiAgICAgIHRoaXJ0eURheXNBZ28uc2V0RGF0ZSh0aGlydHlEYXlzQWdvLmdldERhdGUoKSAtIDMwKTtcclxuXHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvYW5hbHl0aWNzL3N1bW1hcnk/c3RhcnREYXRlPSR7dGhpcnR5RGF5c0Fnby50b0lTT1N0cmluZygpfSZncm91cEJ5PWRheWApO1xyXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gZmV0Y2ggYW5hbHl0aWNzIGRhdGEnKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuICAgICAgc2V0QW5hbHl0aWNzRGF0YShkYXRhKTtcclxuICAgIH0gY2F0Y2ggKGVycjogYW55KSB7XHJcbiAgICAgIHNldEVycm9yKGVyci5tZXNzYWdlKTtcclxuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgYW5hbHl0aWNzOicsIGVycik7XHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICBpZiAoaW5pdGlhbExvYWQpIHtcclxuICAgICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IGZvcm1hdEN1cnJlbmN5ID0gKGFtb3VudDogbnVtYmVyKSA9PiB7XHJcbiAgICByZXR1cm4gbmV3IEludGwuTnVtYmVyRm9ybWF0KCdlbi1VUycsIHtcclxuICAgICAgc3R5bGU6ICdjdXJyZW5jeScsXHJcbiAgICAgIGN1cnJlbmN5OiAnVVNEJyxcclxuICAgICAgbWluaW11bUZyYWN0aW9uRGlnaXRzOiAyLFxyXG4gICAgICBtYXhpbXVtRnJhY3Rpb25EaWdpdHM6IDYsXHJcbiAgICB9KS5mb3JtYXQoYW1vdW50KTtcclxuICB9O1xyXG5cclxuICBjb25zdCBmb3JtYXROdW1iZXIgPSAobnVtOiBudW1iZXIpID0+IHtcclxuICAgIHJldHVybiBuZXcgSW50bC5OdW1iZXJGb3JtYXQoJ2VuLVVTJykuZm9ybWF0KG51bSk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgZmV0Y2hSZWNlbnRBY3Rpdml0eSA9IGFzeW5jICgpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIEZldGNoIHJlY2VudCBhY3Rpdml0eSBmcm9tIHRoZSBuZXcgYWN0aXZpdHkgQVBJXHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvYWN0aXZpdHk/bGltaXQ9MTAnKTtcclxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgICAgIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIGZldGNoIHJlY2VudCBhY3Rpdml0eScpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgICBjb25zdCBhY3Rpdml0aWVzOiBSZWNlbnRBY3Rpdml0eUl0ZW1bXSA9IGRhdGEuYWN0aXZpdGllcy5tYXAoKGFjdGl2aXR5OiBhbnkpID0+ICh7XHJcbiAgICAgICAgaWQ6IGFjdGl2aXR5LmlkLFxyXG4gICAgICAgIGFjdGlvbjogYWN0aXZpdHkuYWN0aW9uLFxyXG4gICAgICAgIG1vZGVsOiBhY3Rpdml0eS5tb2RlbCxcclxuICAgICAgICB0aW1lOiBhY3Rpdml0eS50aW1lLFxyXG4gICAgICAgIHN0YXR1czogYWN0aXZpdHkuc3RhdHVzLFxyXG4gICAgICAgIGRldGFpbHM6IGFjdGl2aXR5LmRldGFpbHNcclxuICAgICAgfSkpO1xyXG5cclxuICAgICAgc2V0UmVjZW50QWN0aXZpdHkoYWN0aXZpdGllcyk7XHJcbiAgICB9IGNhdGNoIChlcnI6IGFueSkge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyByZWNlbnQgYWN0aXZpdHk6JywgZXJyKTtcclxuICAgICAgLy8gU2V0IGZhbGxiYWNrIGFjdGl2aXR5IGRhdGFcclxuICAgICAgc2V0UmVjZW50QWN0aXZpdHkoW1xyXG4gICAgICAgIHsgaWQ6ICcxJywgYWN0aW9uOiAnU3lzdGVtIGluaXRpYWxpemVkJywgbW9kZWw6ICdSb0tleScsIHRpbWU6ICdKdXN0IG5vdycsIHN0YXR1czogJ2luZm8nIH1cclxuICAgICAgXSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgY2hlY2tTeXN0ZW1TdGF0dXMgPSBhc3luYyAoKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL3N5c3RlbS1zdGF0dXMnKTtcclxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgICAgIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIGZldGNoIHN5c3RlbSBzdGF0dXMnKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuICAgICAgY29uc3Qgc3RhdHVzSXRlbXM6IFN5c3RlbVN0YXR1c0l0ZW1bXSA9IGRhdGEuY2hlY2tzLm1hcCgoY2hlY2s6IGFueSkgPT4gKHtcclxuICAgICAgICBuYW1lOiBjaGVjay5uYW1lLFxyXG4gICAgICAgIHN0YXR1czogY2hlY2suc3RhdHVzLFxyXG4gICAgICAgIGxhc3RDaGVja2VkOiBuZXcgRGF0ZShjaGVjay5sYXN0Q2hlY2tlZCkudG9Mb2NhbGVUaW1lU3RyaW5nKClcclxuICAgICAgfSkpO1xyXG5cclxuICAgICAgc2V0U3lzdGVtU3RhdHVzKHN0YXR1c0l0ZW1zKTtcclxuICAgIH0gY2F0Y2ggKGVycikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjaGVja2luZyBzeXN0ZW0gc3RhdHVzOicsIGVycik7XHJcbiAgICAgIC8vIFNldCBmYWxsYmFjayBzdGF0dXNcclxuICAgICAgc2V0U3lzdGVtU3RhdHVzKFtcclxuICAgICAgICB7IG5hbWU6ICdBUEkgR2F0ZXdheScsIHN0YXR1czogJ2Rvd24nLCBsYXN0Q2hlY2tlZDogbmV3IERhdGUoKS50b0xvY2FsZVRpbWVTdHJpbmcoKSB9LFxyXG4gICAgICAgIHsgbmFtZTogJ1JvdXRpbmcgRW5naW5lJywgc3RhdHVzOiAnZG93bicsIGxhc3RDaGVja2VkOiBuZXcgRGF0ZSgpLnRvTG9jYWxlVGltZVN0cmluZygpIH0sXHJcbiAgICAgICAgeyBuYW1lOiAnQW5hbHl0aWNzJywgc3RhdHVzOiAnZG93bicsIGxhc3RDaGVja2VkOiBuZXcgRGF0ZSgpLnRvTG9jYWxlVGltZVN0cmluZygpIH1cclxuICAgICAgXSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgZ2V0VGltZUFnbyA9IChkYXRlOiBEYXRlKTogc3RyaW5nID0+IHtcclxuICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7XHJcbiAgICBjb25zdCBkaWZmSW5TZWNvbmRzID0gTWF0aC5mbG9vcigobm93LmdldFRpbWUoKSAtIGRhdGUuZ2V0VGltZSgpKSAvIDEwMDApO1xyXG5cclxuICAgIGlmIChkaWZmSW5TZWNvbmRzIDwgNjApIHJldHVybiAnSnVzdCBub3cnO1xyXG4gICAgaWYgKGRpZmZJblNlY29uZHMgPCAzNjAwKSByZXR1cm4gYCR7TWF0aC5mbG9vcihkaWZmSW5TZWNvbmRzIC8gNjApfSBtaW51dGVzIGFnb2A7XHJcbiAgICBpZiAoZGlmZkluU2Vjb25kcyA8IDg2NDAwKSByZXR1cm4gYCR7TWF0aC5mbG9vcihkaWZmSW5TZWNvbmRzIC8gMzYwMCl9IGhvdXJzIGFnb2A7XHJcbiAgICByZXR1cm4gYCR7TWF0aC5mbG9vcihkaWZmSW5TZWNvbmRzIC8gODY0MDApfSBkYXlzIGFnb2A7XHJcbiAgfTtcclxuXHJcbiAgLy8gUXVpY2sgQWN0aW9ucyBoYW5kbGVyc1xyXG4gIGNvbnN0IGhhbmRsZUFkZE5ld01vZGVsID0gKCkgPT4ge1xyXG4gICAgcm91dGVyLnB1c2goJy9teS1tb2RlbHMnKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVUZXN0UGxheWdyb3VuZCA9ICgpID0+IHtcclxuICAgIHJvdXRlci5wdXNoKCcvcGxheWdyb3VuZCcpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZVZpZXdMb2dzID0gKCkgPT4ge1xyXG4gICAgcm91dGVyLnB1c2goJy9sb2dzJyk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3Qgc3RhdHMgPSBhbmFseXRpY3NEYXRhID8gW1xyXG4gICAge1xyXG4gICAgICBuYW1lOiAnVG90YWwgUmVxdWVzdHMnLFxyXG4gICAgICB2YWx1ZTogZm9ybWF0TnVtYmVyKGFuYWx5dGljc0RhdGEuc3VtbWFyeS50b3RhbF9yZXF1ZXN0cyksXHJcbiAgICAgIGNoYW5nZTogJ0xhc3QgMzAgZGF5cycsXHJcbiAgICAgIGNoYW5nZVR5cGU6ICduZXV0cmFsJyxcclxuICAgICAgaWNvbjogQ2hhcnRCYXJJY29uLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgbmFtZTogJ1RvdGFsIENvc3QnLFxyXG4gICAgICB2YWx1ZTogZm9ybWF0Q3VycmVuY3koYW5hbHl0aWNzRGF0YS5zdW1tYXJ5LnRvdGFsX2Nvc3QpLFxyXG4gICAgICBjaGFuZ2U6IGAke2Zvcm1hdEN1cnJlbmN5KGFuYWx5dGljc0RhdGEuc3VtbWFyeS5hdmVyYWdlX2Nvc3RfcGVyX3JlcXVlc3QpfSBhdmcvcmVxdWVzdGAsXHJcbiAgICAgIGNoYW5nZVR5cGU6ICduZXV0cmFsJyxcclxuICAgICAgaWNvbjogQ3VycmVuY3lEb2xsYXJJY29uLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgbmFtZTogJ1N1Y2Nlc3MgUmF0ZScsXHJcbiAgICAgIHZhbHVlOiBgJHthbmFseXRpY3NEYXRhLnN1bW1hcnkuc3VjY2Vzc19yYXRlLnRvRml4ZWQoMSl9JWAsXHJcbiAgICAgIGNoYW5nZTogYCR7Zm9ybWF0TnVtYmVyKGFuYWx5dGljc0RhdGEuc3VtbWFyeS5zdWNjZXNzZnVsX3JlcXVlc3RzKX0gc3VjY2Vzc2Z1bGAsXHJcbiAgICAgIGNoYW5nZVR5cGU6IGFuYWx5dGljc0RhdGEuc3VtbWFyeS5zdWNjZXNzX3JhdGUgPj0gOTUgPyAncG9zaXRpdmUnIDogJ25lZ2F0aXZlJyxcclxuICAgICAgaWNvbjogQ2hlY2tDaXJjbGVJY29uLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgbmFtZTogJ1RvdGFsIFRva2VucycsXHJcbiAgICAgIHZhbHVlOiBmb3JtYXROdW1iZXIoYW5hbHl0aWNzRGF0YS5zdW1tYXJ5LnRvdGFsX3Rva2VucyksXHJcbiAgICAgIGNoYW5nZTogYCR7Zm9ybWF0TnVtYmVyKGFuYWx5dGljc0RhdGEuc3VtbWFyeS50b3RhbF9pbnB1dF90b2tlbnMpfSBpbiwgJHtmb3JtYXROdW1iZXIoYW5hbHl0aWNzRGF0YS5zdW1tYXJ5LnRvdGFsX291dHB1dF90b2tlbnMpfSBvdXRgLFxyXG4gICAgICBjaGFuZ2VUeXBlOiAnbmV1dHJhbCcsXHJcbiAgICAgIGljb246IENwdUNoaXBJY29uLFxyXG4gICAgfSxcclxuICBdIDogW107XHJcblxyXG4gIC8vIFNob3cgbG9hZGluZyBvbmx5IG9uIGluaXRpYWwgbG9hZCwgbm90IG9uIHN1YnNlcXVlbnQgdmlzaXRzXHJcbiAgaWYgKGxvYWRpbmcgJiYgaW5pdGlhbExvYWQpIHtcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS04XCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXB1bHNlXCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtOCBiZy1ncmF5LTIwMCByb3VuZGVkIHctMS8zIG1iLTRcIj48L2Rpdj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00IGJnLWdyYXktMjAwIHJvdW5kZWQgdy0xLzJcIj48L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTQgZ2FwLTZcIj5cclxuICAgICAgICAgIHtbLi4uQXJyYXkoNCldLm1hcCgoXywgaSkgPT4gKFxyXG4gICAgICAgICAgICA8ZGl2IGtleT17aX0gY2xhc3NOYW1lPVwiY2FyZCBwLTYgYW5pbWF0ZS1wdWxzZVwiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00IGJnLWdyYXktMjAwIHJvdW5kZWQgdy0xLzIgbWItNFwiPjwvZGl2PlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC04IGJnLWdyYXktMjAwIHJvdW5kZWQgdy0zLzQgbWItMlwiPjwvZGl2PlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC0zIGJnLWdyYXktMjAwIHJvdW5kZWQgdy0xLzNcIj48L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICApKX1cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgaWYgKGVycm9yKSB7XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktOFwiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zbGlkZS1pblwiPlxyXG4gICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtNHhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTJcIj5cclxuICAgICAgICAgICAgV2VsY29tZSBiYWNrISDwn5GLXHJcbiAgICAgICAgICA8L2gxPlxyXG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCB0ZXh0LWxnXCI+XHJcbiAgICAgICAgICAgIEhlcmUncyB3aGF0J3MgaGFwcGVuaW5nIHdpdGggeW91ciBMTE0gaW5mcmFzdHJ1Y3R1cmUgdG9kYXkuXHJcbiAgICAgICAgICA8L3A+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjYXJkIHAtNiB0ZXh0LWNlbnRlclwiPlxyXG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1yZWQtNjAwIG1iLTRcIj5FcnJvciBsb2FkaW5nIGFuYWx5dGljcyBkYXRhOiB7ZXJyb3J9PC9wPlxyXG4gICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICBvbkNsaWNrPXtmZXRjaEFuYWx5dGljc0RhdGF9XHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImJ0bi1wcmltYXJ5XCJcclxuICAgICAgICAgID5cclxuICAgICAgICAgICAgUmV0cnlcclxuICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgICk7XHJcbiAgfVxyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LThcIj5cclxuICAgICAgey8qIEhlYWRlciAqL31cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNsaWRlLWluXCI+XHJcbiAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtNHhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTJcIj5cclxuICAgICAgICAgIFdlbGNvbWUgYmFjayEg8J+Ri1xyXG4gICAgICAgIDwvaDE+XHJcbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCB0ZXh0LWxnXCI+XHJcbiAgICAgICAgICBIZXJlJ3Mgd2hhdCdzIGhhcHBlbmluZyB3aXRoIHlvdXIgTExNIGluZnJhc3RydWN0dXJlIHRvZGF5LlxyXG4gICAgICAgIDwvcD5cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICB7LyogU3RhdHMgR3JpZCAqL31cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy00IGdhcC02IGFuaW1hdGUtc2xpZGUtaW5cIj5cclxuICAgICAgICB7c3RhdHMubWFwKChzdGF0LCBpbmRleCkgPT4gKFxyXG4gICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICBrZXk9e3N0YXQubmFtZX1cclxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiY2FyZCBwLTYgaG92ZXI6c2hhZG93LWxnIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMFwiXHJcbiAgICAgICAgICAgIHN0eWxlPXt7IGFuaW1hdGlvbkRlbGF5OiBgJHtpbmRleCAqIDEwMH1tc2AgfX1cclxuICAgICAgICAgID5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cclxuICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNjAwXCI+e3N0YXQubmFtZX08L3A+XHJcbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBtdC0yXCI+e3N0YXQudmFsdWV9PC9wPlxyXG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPXtgdGV4dC1zbSBtdC0yIGZsZXggaXRlbXMtY2VudGVyICR7XHJcbiAgICAgICAgICAgICAgICAgIHN0YXQuY2hhbmdlVHlwZSA9PT0gJ3Bvc2l0aXZlJyA/ICd0ZXh0LWdyZWVuLTYwMCcgOlxyXG4gICAgICAgICAgICAgICAgICBzdGF0LmNoYW5nZVR5cGUgPT09ICduZWdhdGl2ZScgPyAndGV4dC1yZWQtNjAwJyA6ICd0ZXh0LWdyYXktNTAwJ1xyXG4gICAgICAgICAgICAgICAgfWB9PlxyXG4gICAgICAgICAgICAgICAgICB7c3RhdC5jaGFuZ2VUeXBlICE9PSAnbmV1dHJhbCcgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgIDxBcnJvd1RyZW5kaW5nVXBJY29uIGNsYXNzTmFtZT17YGgtNCB3LTQgbXItMSAke1xyXG4gICAgICAgICAgICAgICAgICAgICAgc3RhdC5jaGFuZ2VUeXBlID09PSAnbmVnYXRpdmUnID8gJ3JvdGF0ZS0xODAnIDogJydcclxuICAgICAgICAgICAgICAgICAgICB9YH0gLz5cclxuICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAge3N0YXQuY2hhbmdlfVxyXG4gICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0zIHJvdW5kZWQtbGcgYmctb3JhbmdlLTUwXCI+XHJcbiAgICAgICAgICAgICAgICA8c3RhdC5pY29uIGNsYXNzTmFtZT1cImgtNiB3LTYgdGV4dC1vcmFuZ2UtNjAwXCIgLz5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICApKX1cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICB7LyogTWFpbiBDb250ZW50IEdyaWQgKi99XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBsZzpncmlkLWNvbHMtMyBnYXAtOFwiPlxyXG4gICAgICAgIHsvKiBRdWljayBBY3Rpb25zICovfVxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibGc6Y29sLXNwYW4tMSBzcGFjZS15LTZcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZCBwLTYgYW5pbWF0ZS1zbGlkZS1pblwiPlxyXG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgbWItNFwiPlF1aWNrIEFjdGlvbnM8L2gzPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxyXG4gICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUFkZE5ld01vZGVsfVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYnRuLXByaW1hcnkgdy1mdWxsIGp1c3RpZnktY2VudGVyIGhvdmVyOnNjYWxlLTEwNSB0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi0yMDBcIlxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIDxQbHVzSWNvbiBjbGFzc05hbWU9XCJoLTUgdy01IG1yLTNcIiAvPlxyXG4gICAgICAgICAgICAgICAgQWRkIE5ldyBNb2RlbFxyXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVRlc3RQbGF5Z3JvdW5kfVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYnRuLXNlY29uZGFyeSB3LWZ1bGwganVzdGlmeS1jZW50ZXIgaG92ZXI6c2NhbGUtMTA1IHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTIwMFwiXHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPEJlYWtlckljb24gY2xhc3NOYW1lPVwiaC01IHctNSBtci0zXCIgLz5cclxuICAgICAgICAgICAgICAgIFRlc3QgaW4gUGxheWdyb3VuZFxyXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVZpZXdMb2dzfVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYnRuLW91dGxpbmUgdy1mdWxsIGp1c3RpZnktY2VudGVyIGhvdmVyOnNjYWxlLTEwNSB0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi0yMDBcIlxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIDxEb2N1bWVudFRleHRJY29uIGNsYXNzTmFtZT1cImgtNSB3LTUgbXItM1wiIC8+XHJcbiAgICAgICAgICAgICAgICBWaWV3IExvZ3NcclxuICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICB7LyogU3lzdGVtIFN0YXR1cyAqL31cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZCBwLTYgYW5pbWF0ZS1zbGlkZS1pblwiPlxyXG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgbWItNFwiPlN5c3RlbSBTdGF0dXM8L2gzPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxyXG4gICAgICAgICAgICAgIHtzeXN0ZW1TdGF0dXMubWFwKChzeXN0ZW0pID0+IChcclxuICAgICAgICAgICAgICAgIDxkaXYga2V5PXtzeXN0ZW0ubmFtZX0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHctMyBoLTMgcm91bmRlZC1mdWxsIG1yLTMgJHtcclxuICAgICAgICAgICAgICAgICAgICAgIHN5c3RlbS5zdGF0dXMgPT09ICdvcGVyYXRpb25hbCcgPyAnYmctZ3JlZW4tNTAwJyA6XHJcbiAgICAgICAgICAgICAgICAgICAgICBzeXN0ZW0uc3RhdHVzID09PSAnZGVncmFkZWQnID8gJ2JnLXllbGxvdy01MDAnIDogJ2JnLXJlZC01MDAnXHJcbiAgICAgICAgICAgICAgICAgICAgfWB9PjwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS03MDBcIj57c3lzdGVtLm5hbWV9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgdGV4dC1zbSBmb250LW1lZGl1bSAke1xyXG4gICAgICAgICAgICAgICAgICAgICAgc3lzdGVtLnN0YXR1cyA9PT0gJ29wZXJhdGlvbmFsJyA/ICd0ZXh0LWdyZWVuLTYwMCcgOlxyXG4gICAgICAgICAgICAgICAgICAgICAgc3lzdGVtLnN0YXR1cyA9PT0gJ2RlZ3JhZGVkJyA/ICd0ZXh0LXllbGxvdy02MDAnIDogJ3RleHQtcmVkLTYwMCdcclxuICAgICAgICAgICAgICAgICAgICB9YH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICB7c3lzdGVtLnN0YXR1cyA9PT0gJ29wZXJhdGlvbmFsJyA/ICdPcGVyYXRpb25hbCcgOlxyXG4gICAgICAgICAgICAgICAgICAgICAgIHN5c3RlbS5zdGF0dXMgPT09ICdkZWdyYWRlZCcgPyAnRGVncmFkZWQnIDogJ0Rvd24nfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICB7c3lzdGVtLmxhc3RDaGVja2VkICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPntzeXN0ZW0ubGFzdENoZWNrZWR9PC9wPlxyXG4gICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIHsvKiBSZWNlbnQgQWN0aXZpdHkgKi99XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJsZzpjb2wtc3Bhbi0yXCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmQgcC02IGFuaW1hdGUtc2xpZGUtaW5cIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNlwiPlxyXG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPlJlY2VudCBBY3Rpdml0eTwvaDM+XHJcbiAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17ZmV0Y2hSZWNlbnRBY3Rpdml0eX1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtb3JhbmdlLTYwMCBob3Zlcjp0ZXh0LW9yYW5nZS03MDAgdGV4dC1zbSBmb250LW1lZGl1bVwiXHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgUmVmcmVzaFxyXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cclxuICAgICAgICAgICAgICB7cmVjZW50QWN0aXZpdHkubGVuZ3RoID09PSAwID8gKFxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS04XCI+XHJcbiAgICAgICAgICAgICAgICAgIDxDbG9ja0ljb24gY2xhc3NOYW1lPVwiaC0xMiB3LTEyIHRleHQtZ3JheS00MDAgbXgtYXV0byBtYi00XCIgLz5cclxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMFwiPk5vIHJlY2VudCBhY3Rpdml0eTwvcD5cclxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCB0ZXh0LXNtXCI+QWN0aXZpdHkgd2lsbCBhcHBlYXIgaGVyZSBhcyB5b3UgdXNlIHRoZSBBUEk8L3A+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgcmVjZW50QWN0aXZpdHkuc2xpY2UoLTQpLm1hcCgoYWN0aXZpdHkpID0+IChcclxuICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2FjdGl2aXR5LmlkfSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IHAtNCByb3VuZGVkLWxnIGJnLWdyYXktNTAgaG92ZXI6YmctZ3JheS0xMDAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwIGdyb3VwIG92ZXJmbG93LWhpZGRlblwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgdy0zIGgtMyByb3VuZGVkLWZ1bGwgbXItNCAke1xyXG4gICAgICAgICAgICAgICAgICAgICAgYWN0aXZpdHkuc3RhdHVzID09PSAnc3VjY2VzcycgPyAnYmctZ3JlZW4tNTAwJyA6XHJcbiAgICAgICAgICAgICAgICAgICAgICBhY3Rpdml0eS5zdGF0dXMgPT09ICd3YXJuaW5nJyA/ICdiZy15ZWxsb3ctNTAwJyA6XHJcbiAgICAgICAgICAgICAgICAgICAgICBhY3Rpdml0eS5zdGF0dXMgPT09ICdlcnJvcicgPyAnYmctcmVkLTUwMCcgOiAnYmctYmx1ZS01MDAnXHJcbiAgICAgICAgICAgICAgICAgICAgfWB9PjwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG1pbi13LTBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS05MDAgZm9udC1tZWRpdW0gYnJlYWstd29yZHNcIj57YWN0aXZpdHkuYWN0aW9ufTwvcD5cclxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgdGV4dC1zbSBicmVhay13b3Jkc1wiPnthY3Rpdml0eS5tb2RlbH0g4oCiIHthY3Rpdml0eS50aW1lfTwvcD5cclxuICAgICAgICAgICAgICAgICAgICAgIHthY3Rpdml0eS5kZXRhaWxzICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMCB0ZXh0LXhzIG10LTEgbGluZS1jbGFtcC0yIGxlYWRpbmctcmVsYXhlZFwiIHRpdGxlPXthY3Rpdml0eS5kZXRhaWxzfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICB7YWN0aXZpdHkuZGV0YWlsc31cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgZ3JvdXAtaG92ZXI6dGV4dC1ncmF5LTcwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAge2FjdGl2aXR5LnN0YXR1cyA9PT0gJ2Vycm9yJyA/IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPEV4Y2xhbWF0aW9uVHJpYW5nbGVJY29uIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1yZWQtNTAwXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxDcHVDaGlwSWNvbiBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgKSlcclxuICAgICAgICAgICAgICApfVxyXG5cclxuICAgICAgICAgICAgICB7LyogVmlldyBBbGwgTGluayAtIG9ubHkgc2hvdyBpZiB0aGVyZSBhcmUgbW9yZSB0aGFuIDQgaXRlbXMgKi99XHJcbiAgICAgICAgICAgICAge3JlY2VudEFjdGl2aXR5Lmxlbmd0aCA+IDQgJiYgKFxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC00IHB0LTQgYm9yZGVyLXQgYm9yZGVyLWdyYXktMjAwXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAvLyBOYXZpZ2F0ZSB0byBsb2dzIHBhZ2UgdG8gc2VlIGFsbCBhY3Rpdml0eVxyXG4gICAgICAgICAgICAgICAgICAgICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSAnL2xvZ3MnO1xyXG4gICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW9yYW5nZS02MDAgaG92ZXI6dGV4dC1vcmFuZ2UtNzAwIGZvbnQtbWVkaXVtIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHctZnVsbCBweS0yIGhvdmVyOmJnLW9yYW5nZS01MCByb3VuZGVkLWxnIHRyYW5zaXRpb24tY29sb3JzXCJcclxuICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgIFZpZXcgQWxsIEFjdGl2aXR5ICh7cmVjZW50QWN0aXZpdHkubGVuZ3RofSlcclxuICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTQgbWwtMVwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTkgNWw3IDctNyA3XCIgLz5cclxuICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cclxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn0iXSwibmFtZXMiOlsiRGFzaGJvYXJkUGFnZSIsInJvdXRlciIsInVzZVJvdXRlciIsImFuYWx5dGljc0RhdGEiLCJzZXRBbmFseXRpY3NEYXRhIiwidXNlU3RhdGUiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImluaXRpYWxMb2FkIiwic2V0SW5pdGlhbExvYWQiLCJlcnJvciIsInNldEVycm9yIiwicmVjZW50QWN0aXZpdHkiLCJzZXRSZWNlbnRBY3Rpdml0eSIsInN5c3RlbVN0YXR1cyIsInNldFN5c3RlbVN0YXR1cyIsIm5hbWUiLCJzdGF0dXMiLCJ1c2VFZmZlY3QiLCJsb2FkRGF0YSIsIlByb21pc2UiLCJyZXNvbHZlIiwic2V0VGltZW91dCIsInByb21pc2VzIiwiZmV0Y2hBbmFseXRpY3NEYXRhIiwiZmV0Y2hSZWNlbnRBY3Rpdml0eSIsImNoZWNrU3lzdGVtU3RhdHVzIiwiYWxsU2V0dGxlZCIsImFjdGl2aXR5SW50ZXJ2YWwiLCJzZXRJbnRlcnZhbCIsInN0YXR1c0ludGVydmFsIiwiY2xlYXJJbnRlcnZhbCIsInRoaXJ0eURheXNBZ28iLCJEYXRlIiwic2V0RGF0ZSIsImdldERhdGUiLCJyZXNwb25zZSIsImZldGNoIiwidG9JU09TdHJpbmciLCJvayIsIkVycm9yIiwiZGF0YSIsImpzb24iLCJlcnIiLCJtZXNzYWdlIiwiY29uc29sZSIsImZvcm1hdEN1cnJlbmN5IiwiYW1vdW50IiwiSW50bCIsIk51bWJlckZvcm1hdCIsInN0eWxlIiwiY3VycmVuY3kiLCJtaW5pbXVtRnJhY3Rpb25EaWdpdHMiLCJtYXhpbXVtRnJhY3Rpb25EaWdpdHMiLCJmb3JtYXQiLCJmb3JtYXROdW1iZXIiLCJudW0iLCJhY3Rpdml0aWVzIiwibWFwIiwiYWN0aXZpdHkiLCJpZCIsImFjdGlvbiIsIm1vZGVsIiwidGltZSIsImRldGFpbHMiLCJzdGF0dXNJdGVtcyIsImNoZWNrcyIsImNoZWNrIiwibGFzdENoZWNrZWQiLCJ0b0xvY2FsZVRpbWVTdHJpbmciLCJnZXRUaW1lQWdvIiwiZGF0ZSIsIm5vdyIsImRpZmZJblNlY29uZHMiLCJNYXRoIiwiZmxvb3IiLCJnZXRUaW1lIiwiaGFuZGxlQWRkTmV3TW9kZWwiLCJwdXNoIiwiaGFuZGxlVGVzdFBsYXlncm91bmQiLCJoYW5kbGVWaWV3TG9ncyIsInN0YXRzIiwidmFsdWUiLCJzdW1tYXJ5IiwidG90YWxfcmVxdWVzdHMiLCJjaGFuZ2UiLCJjaGFuZ2VUeXBlIiwiaWNvbiIsIkNoYXJ0QmFySWNvbiIsInRvdGFsX2Nvc3QiLCJhdmVyYWdlX2Nvc3RfcGVyX3JlcXVlc3QiLCJDdXJyZW5jeURvbGxhckljb24iLCJzdWNjZXNzX3JhdGUiLCJ0b0ZpeGVkIiwic3VjY2Vzc2Z1bF9yZXF1ZXN0cyIsIkNoZWNrQ2lyY2xlSWNvbiIsInRvdGFsX3Rva2VucyIsInRvdGFsX2lucHV0X3Rva2VucyIsInRvdGFsX291dHB1dF90b2tlbnMiLCJDcHVDaGlwSWNvbiIsImRpdiIsImNsYXNzTmFtZSIsIkFycmF5IiwiXyIsImkiLCJoMSIsInAiLCJidXR0b24iLCJvbkNsaWNrIiwic3RhdCIsImluZGV4IiwiYW5pbWF0aW9uRGVsYXkiLCJBcnJvd1RyZW5kaW5nVXBJY29uIiwiaDMiLCJQbHVzSWNvbiIsIkJlYWtlckljb24iLCJEb2N1bWVudFRleHRJY29uIiwic3lzdGVtIiwic3BhbiIsImxlbmd0aCIsIkNsb2NrSWNvbiIsInNsaWNlIiwidGl0bGUiLCJFeGNsYW1hdGlvblRyaWFuZ2xlSWNvbiIsIndpbmRvdyIsImxvY2F0aW9uIiwiaHJlZiIsInN2ZyIsImZpbGwiLCJzdHJva2UiLCJ2aWV3Qm94IiwicGF0aCIsInN0cm9rZUxpbmVjYXAiLCJzdHJva2VMaW5lam9pbiIsInN0cm9rZVdpZHRoIiwiZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});