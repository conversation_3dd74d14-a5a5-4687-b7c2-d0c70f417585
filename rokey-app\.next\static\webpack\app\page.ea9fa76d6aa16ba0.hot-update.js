"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/FeaturesSection.tsx":
/*!****************************************************!*\
  !*** ./src/components/landing/FeaturesSection.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeaturesSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst features = [\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: \"Intelligent Role Routing\",\n        description: \"AI automatically classifies your prompts and routes them to the best model for each specific task - writing, coding, logic, or analysis.\",\n        color: \"text-blue-600\",\n        bgColor: \"bg-blue-50\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"Automatic Failover\",\n        description: \"Never worry about API limits or downtime. RouKey automatically retries failed requests with alternative models in your configuration.\",\n        color: \"text-green-600\",\n        bgColor: \"bg-green-50\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Enterprise Security\",\n        description: \"Military-grade AES-256-GCM encryption for all API keys. Your credentials are stored securely and never exposed.\",\n        color: \"text-purple-600\",\n        bgColor: \"bg-purple-50\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Comprehensive Analytics\",\n        description: \"Track costs, performance, token usage, and success rates across all your models with real-time dashboards and insights.\",\n        color: \"text-orange-600\",\n        bgColor: \"bg-orange-50\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"Performance Optimization\",\n        description: \"First-token tracking, latency monitoring, and intelligent caching ensure blazing-fast response times under 500ms.\",\n        color: \"text-indigo-600\",\n        bgColor: \"bg-indigo-50\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"Cost Optimization\",\n        description: \"Automatic free-tier detection, budget alerts, and cost tracking help you optimize spending across all AI providers.\",\n        color: \"text-emerald-600\",\n        bgColor: \"bg-emerald-50\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        title: \"300+ AI Models\",\n        description: \"Access the latest models from OpenAI, Google, Anthropic, DeepSeek, xAI, Meta, and hundreds more through one unified API.\",\n        color: \"text-red-600\",\n        bgColor: \"bg-red-50\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        title: \"Advanced Routing Strategies\",\n        description: \"Load balancing, complexity-based routing, strict fallback sequences, and custom rules for enterprise-grade control.\",\n        color: \"text-cyan-600\",\n        bgColor: \"bg-cyan-50\"\n    }\n];\nfunction FeaturesSection() {\n    _s();\n    const [isHovered, setIsHovered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"features\",\n        className: \"py-24 bg-gradient-to-br from-gray-900 to-black relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-10\",\n                        style: {\n                            backgroundImage: \"\\n              linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),\\n              linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)\\n            \",\n                            backgroundSize: '80px 80px'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 right-0 w-96 h-96 bg-[#ff6b35]/5 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 left-0 w-96 h-96 bg-[#f7931e]/5 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-left mb-20 max-w-4xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.h2, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"text-5xl sm:text-6xl font-bold text-white mb-6 leading-tight\",\n                                children: [\n                                    \"Enterprise-Grade\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]\",\n                                        children: [\n                                            ' ',\n                                            \"AI Infrastructure\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.p, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    delay: 0.2\n                                },\n                                className: \"text-xl text-gray-300 max-w-3xl leading-relaxed\",\n                                children: \"RouKey provides military-grade security, intelligent routing, and comprehensive analytics for the most demanding AI workloads. Built for scale, designed for performance.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-6xl mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 50\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"relative h-[700px] flex items-center justify-center\",\n                            style: {\n                                perspective: '1000px'\n                            },\n                            onMouseEnter: ()=>setIsHovered(true),\n                            onMouseLeave: ()=>setIsHovered(false),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-gradient-to-r from-[#ff6b35]/10 via-transparent to-[#f7931e]/10 rounded-3xl blur-3xl transition-all duration-700 \".concat(isHovered ? 'from-[#ff6b35]/20 to-[#f7931e]/20' : '')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, this),\n                                features.map((feature, index)=>{\n                                    // Calculate positions for stacked and spread states\n                                    const getTransform = (spread)=>{\n                                        if (spread) {\n                                            const angle = (index - 3.5) * 18; // Spread in a fan\n                                            const radius = 320;\n                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 120;\n                                            return {\n                                                x,\n                                                y,\n                                                rotateY: angle,\n                                                rotateX: Math.abs(angle) * 0.2,\n                                                scale: 1\n                                            };\n                                        } else {\n                                            return {\n                                                x: index * 8,\n                                                y: index * 4,\n                                                rotateY: index * 2,\n                                                rotateX: 0,\n                                                scale: 1\n                                            };\n                                        }\n                                    };\n                                    const transform = getTransform(isHovered);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                        className: \"absolute w-80 h-96 cursor-pointer\",\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.8\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: transform.x,\n                                            y: transform.y,\n                                            rotateY: transform.rotateY,\n                                            rotateX: transform.rotateX,\n                                            scale: transform.scale\n                                        },\n                                        whileInView: {\n                                            opacity: 1\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        transition: {\n                                            delay: isHovered ? 0 : index * 0.1,\n                                            duration: isHovered ? 0.8 : 0.6,\n                                            type: \"spring\",\n                                            stiffness: isHovered ? 80 : 100,\n                                            damping: isHovered ? 20 : 15\n                                        },\n                                        style: {\n                                            zIndex: isHovered ? index === Math.floor(features.length / 2) ? 50 : features.length - Math.abs(index - Math.floor(features.length / 2)) : features.length - index,\n                                            transformStyle: 'preserve-3d'\n                                        },\n                                        whileHover: {\n                                            scale: 1.05,\n                                            zIndex: 100,\n                                            transition: {\n                                                duration: 0.3\n                                            }\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-full bg-gradient-to-br from-gray-800/90 to-gray-900/90 backdrop-blur-xl rounded-3xl border border-gray-700 hover:border-[#ff6b35]/50 transition-all duration-500 shadow-2xl hover:shadow-orange-500/20 relative overflow-hidden group/card\",\n                                            style: {\n                                                transform: 'translateZ(0)',\n                                                backfaceVisibility: 'hidden'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 opacity-5\",\n                                                    style: {\n                                                        backgroundImage: \"\\n                          linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),\\n                          linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)\\n                        \",\n                                                        backgroundSize: '20px 20px'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10 p-8 h-full flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-20 h-20 \".concat(index % 3 === 0 ? 'bg-gradient-to-br from-[#ff6b35] to-[#f7931e]' : index % 3 === 1 ? 'bg-gradient-to-br from-gray-600 to-gray-700' : 'bg-gradient-to-br from-[#f7931e] to-[#ff6b35]', \" rounded-2xl flex items-center justify-center mb-6 group-hover/card:scale-110 transition-transform duration-300 shadow-lg \").concat(index % 3 === 0 || index % 3 === 2 ? 'shadow-orange-500/30' : 'shadow-gray-500/30'),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                                className: \"h-10 w-10 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                lineNumber: 221,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-2xl font-bold text-white mb-4 group-hover/card:text-[#ff6b35] transition-colors duration-300\",\n                                                            children: feature.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-300 leading-relaxed flex-1 text-lg\",\n                                                            children: feature.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-6 h-1 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-full transform scale-x-0 group-hover/card:scale-x-100 transition-transform duration-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                            lineNumber: 235,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 rounded-3xl bg-gradient-to-r from-[#ff6b35]/10 to-[#f7931e]/10 opacity-0 group-hover/card:opacity-100 transition-opacity duration-500 pointer-events-none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 rounded-3xl bg-gradient-to-r from-transparent via-white/5 to-transparent transform -skew-x-12 -translate-x-full group-hover/card:translate-x-full transition-transform duration-1000 pointer-events-none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, feature.title, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 17\n                                    }, this);\n                                }),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        delay: 1.5\n                                    },\n                                    className: \"absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-20 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-800/90 backdrop-blur-sm rounded-full px-8 py-4 border border-gray-700 shadow-xl\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.p, {\n                                            className: \"text-gray-300 text-sm font-medium\",\n                                            animate: {\n                                                color: isHovered ? '#ff6b35' : '#d1d5db'\n                                            },\n                                            transition: {\n                                                duration: 0.3\n                                            },\n                                            children: isHovered ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-[#ff6b35]\",\n                                                        children: \"✨ Exploring\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \" our features\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-[#ff6b35]\",\n                                                        children: \"Hover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \" to spread the cards\"\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: 50\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        className: \"mt-24 flex justify-end\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-2xl bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-3xl p-12 text-white relative overflow-hidden shadow-2xl shadow-orange-500/25\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 opacity-10\",\n                                    style: {\n                                        backgroundImage: \"\\n                  linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),\\n                  linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px)\\n                \",\n                                        backgroundSize: '20px 20px'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative z-10 text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-4xl font-bold mb-6 leading-tight\",\n                                            children: [\n                                                \"Ready to Transform\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Your AI Infrastructure?\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl opacity-90 mb-8 leading-relaxed\",\n                                            children: \"Join the next generation of developers building with RouKey's intelligent AI gateway. Start routing smarter today.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row gap-4 justify-end\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.button, {\n                                                    whileHover: {\n                                                        scale: 1.05,\n                                                        boxShadow: \"0 20px 40px rgba(0,0,0,0.3)\"\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.95\n                                                    },\n                                                    className: \"bg-white text-[#ff6b35] px-8 py-4 rounded-xl font-bold hover:bg-gray-50 transition-all duration-300 shadow-lg\",\n                                                    children: \"Start Building Now\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.button, {\n                                                    whileHover: {\n                                                        scale: 1.05\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.95\n                                                    },\n                                                    className: \"border-2 border-white text-white px-8 py-4 rounded-xl font-bold hover:bg-white hover:text-[#ff6b35] transition-all duration-300\",\n                                                    children: \"View Documentation\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full blur-xl\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -bottom-4 -left-4 w-32 h-32 bg-white/5 rounded-full blur-2xl\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, this);\n}\n_s(FeaturesSection, \"FPQn8a98tPjpohC7NUYORQR8GJE=\");\n_c = FeaturesSection;\nvar _c;\n$RefreshReg$(_c, \"FeaturesSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/FeaturesSection.tsx\n"));

/***/ })

});