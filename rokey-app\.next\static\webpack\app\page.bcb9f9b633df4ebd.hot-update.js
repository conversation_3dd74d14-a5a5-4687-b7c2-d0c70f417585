"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/RoutingVisualization.tsx":
/*!*********************************************************!*\
  !*** ./src/components/landing/RoutingVisualization.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RoutingVisualization)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,CpuChipIcon,PencilIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,CpuChipIcon,PencilIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,CpuChipIcon,PencilIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CodeBracketIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,CpuChipIcon,PencilIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,CpuChipIcon,PencilIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,CpuChipIcon,PencilIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,CpuChipIcon,PencilIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst routingExamples = [\n    {\n        id: 1,\n        prompt: \"Solve this complex math problem: 2x + 5 = 15\",\n        role: \"logic_reasoning\",\n        roleName: \"Logic & Reasoning\",\n        model: \"GPT-4o\",\n        provider: \"OpenAI\",\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        color: \"text-blue-400\",\n        bgColor: \"bg-blue-500/10\",\n        borderColor: \"border-blue-500/20\",\n        glowColor: \"shadow-blue-500/50\"\n    },\n    {\n        id: 2,\n        prompt: \"Write a blog post about AI trends\",\n        role: \"writing\",\n        roleName: \"Writing & Content Creation\",\n        model: \"Claude 3.5 Sonnet\",\n        provider: \"Anthropic\",\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        color: \"text-purple-400\",\n        bgColor: \"bg-purple-500/10\",\n        borderColor: \"border-purple-500/20\",\n        glowColor: \"shadow-purple-500/50\"\n    },\n    {\n        id: 3,\n        prompt: \"Build a React component with TypeScript\",\n        role: \"coding_frontend\",\n        roleName: \"Frontend Development\",\n        model: \"DeepSeek Coder V2\",\n        provider: \"DeepSeek\",\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        color: \"text-green-400\",\n        bgColor: \"bg-green-500/10\",\n        borderColor: \"border-green-500/20\",\n        glowColor: \"shadow-green-500/50\"\n    },\n    {\n        id: 4,\n        prompt: \"Summarize this research paper\",\n        role: \"research_synthesis\",\n        roleName: \"Research & Analysis\",\n        model: \"Gemini 2.0 Flash\",\n        provider: \"Google\",\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        color: \"text-cyan-400\",\n        bgColor: \"bg-cyan-500/10\",\n        borderColor: \"border-cyan-500/20\",\n        glowColor: \"shadow-cyan-500/50\"\n    }\n];\nfunction RoutingVisualization() {\n    _s();\n    const [activeExample, setActiveExample] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [isAnimating, setIsAnimating] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"RoutingVisualization.useEffect\": ()=>{\n            const interval = setInterval({\n                \"RoutingVisualization.useEffect.interval\": ()=>{\n                    setIsAnimating(true);\n                    setTimeout({\n                        \"RoutingVisualization.useEffect.interval\": ()=>{\n                            setActiveExample({\n                                \"RoutingVisualization.useEffect.interval\": (prev)=>(prev + 1) % routingExamples.length\n                            }[\"RoutingVisualization.useEffect.interval\"]);\n                            setIsAnimating(false);\n                        }\n                    }[\"RoutingVisualization.useEffect.interval\"], 500);\n                }\n            }[\"RoutingVisualization.useEffect.interval\"], 5000); // Slower transition for better viewing\n            return ({\n                \"RoutingVisualization.useEffect\": ()=>clearInterval(interval)\n            })[\"RoutingVisualization.useEffect\"];\n        }\n    }[\"RoutingVisualization.useEffect\"], []);\n    const currentExample = routingExamples[activeExample];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"jsx-78bab6ba4a187045\" + \" \" + \"py-20 bg-gradient-to-br from-slate-900 via-gray-900 to-slate-800 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"78bab6ba4a187045\",\n                children: \"@-webkit-keyframes flowRight{0%{-webkit-transform:translatex(-100%);transform:translatex(-100%);opacity:0}50%{opacity:1}100%{-webkit-transform:translatex(100%);transform:translatex(100%);opacity:0}}@-moz-keyframes flowRight{0%{-moz-transform:translatex(-100%);transform:translatex(-100%);opacity:0}50%{opacity:1}100%{-moz-transform:translatex(100%);transform:translatex(100%);opacity:0}}@-o-keyframes flowRight{0%{-o-transform:translatex(-100%);transform:translatex(-100%);opacity:0}50%{opacity:1}100%{-o-transform:translatex(100%);transform:translatex(100%);opacity:0}}@keyframes flowRight{0%{-webkit-transform:translatex(-100%);-moz-transform:translatex(-100%);-o-transform:translatex(-100%);transform:translatex(-100%);opacity:0}50%{opacity:1}100%{-webkit-transform:translatex(100%);-moz-transform:translatex(100%);-o-transform:translatex(100%);transform:translatex(100%);opacity:0}}@-webkit-keyframes flowLeft{0%{-webkit-transform:translatex(100%);transform:translatex(100%);opacity:0}50%{opacity:1}100%{-webkit-transform:translatex(-100%);transform:translatex(-100%);opacity:0}}@-moz-keyframes flowLeft{0%{-moz-transform:translatex(100%);transform:translatex(100%);opacity:0}50%{opacity:1}100%{-moz-transform:translatex(-100%);transform:translatex(-100%);opacity:0}}@-o-keyframes flowLeft{0%{-o-transform:translatex(100%);transform:translatex(100%);opacity:0}50%{opacity:1}100%{-o-transform:translatex(-100%);transform:translatex(-100%);opacity:0}}@keyframes flowLeft{0%{-webkit-transform:translatex(100%);-moz-transform:translatex(100%);-o-transform:translatex(100%);transform:translatex(100%);opacity:0}50%{opacity:1}100%{-webkit-transform:translatex(-100%);-moz-transform:translatex(-100%);-o-transform:translatex(-100%);transform:translatex(-100%);opacity:0}}.flow-right.jsx-78bab6ba4a187045{-webkit-animation:flowRight 2s ease-in-out infinite;-moz-animation:flowRight 2s ease-in-out infinite;-o-animation:flowRight 2s ease-in-out infinite;animation:flowRight 2s ease-in-out infinite}.flow-left.jsx-78bab6ba4a187045{-webkit-animation:flowLeft 2s ease-in-out infinite;-moz-animation:flowLeft 2s ease-in-out infinite;-o-animation:flowLeft 2s ease-in-out infinite;animation:flowLeft 2s ease-in-out infinite;-webkit-animation-delay:1s;-moz-animation-delay:1s;-o-animation-delay:1s;animation-delay:1s}.glow-pulse.jsx-78bab6ba4a187045{-webkit-animation:pulse 2s cubic-bezier(.4,0,.6,1)infinite;-moz-animation:pulse 2s cubic-bezier(.4,0,.6,1)infinite;-o-animation:pulse 2s cubic-bezier(.4,0,.6,1)infinite;animation:pulse 2s cubic-bezier(.4,0,.6,1)infinite}\"\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-78bab6ba4a187045\" + \" \" + \"absolute inset-0 opacity-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-78bab6ba4a187045\" + \" \" + \"absolute inset-0 bg-grid-white/[0.02] bg-[size:60px_60px]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-78bab6ba4a187045\" + \" \" + \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.h2, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"text-4xl sm:text-5xl font-bold text-white mb-6\",\n                                children: \"Introducing the AI Gateway Pattern\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.p, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    delay: 0.1\n                                },\n                                className: \"text-xl text-gray-300 max-w-3xl mx-auto\",\n                                children: \"Watch how RouKey intelligently routes your requests through our unified gateway to the perfect AI model\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"relative max-w-7xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-78bab6ba4a187045\" + \" \" + \"grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-16 items-start\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: -50\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6\n                                        },\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-xl font-semibold text-white mb-2\",\n                                                        children: \"User Prompt\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 159,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-sm text-gray-400\",\n                                                        children: \"Your request enters RouKey\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-78bab6ba4a187045\" + \" \" + \"bg-slate-800 border-2 \".concat(currentExample.borderColor, \" rounded-xl p-6 relative \").concat(currentExample.bgColor, \" shadow-lg \").concat(currentExample.glowColor),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"absolute inset-0 rounded-xl border-2 border-dashed border-gray-500 opacity-30 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"relative z-10\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-sm text-gray-400 mb-3\",\n                                                                children: \"Incoming Request\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 169,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-white font-medium mb-4 text-lg leading-relaxed\",\n                                                                children: [\n                                                                    '\"',\n                                                                    currentExample.prompt,\n                                                                    '\"'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 170,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-78bab6ba4a187045\" + \" \" + \"flex items-center text-sm text-gray-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"w-2 h-2 bg-[#ff6b35] rounded-full mr-2 animate-pulse\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 174,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    \"Analyzing prompt...\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 173,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-78bab6ba4a187045\" + \" \" + \"flex space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-78bab6ba4a187045\" + \" \" + \"w-1 h-8 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 182,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.2s'\n                                                                    },\n                                                                    className: \"jsx-78bab6ba4a187045\" + \" \" + \"w-1 h-6 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 183,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.4s'\n                                                                    },\n                                                                    className: \"jsx-78bab6ba4a187045\" + \" \" + \"w-1 h-4 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 184,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 181,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-78bab6ba4a187045\" + \" \" + \"absolute right-0 top-1/2 transform translate-x-full -translate-y-1/2 hidden lg:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-78bab6ba4a187045\" + \" \" + \"relative w-16 h-px\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-78bab6ba4a187045\" + \" \" + \"absolute inset-0 bg-gradient-to-r from-[#ff6b35] to-transparent opacity-60\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 192,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-78bab6ba4a187045\" + \" \" + \"absolute top-0 left-0 w-4 h-px bg-gradient-to-r from-white to-[#ff6b35] flow-right shadow-lg shadow-orange-500/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 193,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                animationDelay: '0.5s'\n                                                            },\n                                                            className: \"jsx-78bab6ba4a187045\" + \" \" + \"absolute top-0 left-0 w-2 h-px bg-white flow-right shadow-sm shadow-white/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, \"prompt-\".concat(activeExample), true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.9\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            scale: 1\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 0.3\n                                        },\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-xl font-semibold text-white mb-2\",\n                                                        children: \"Intelligent Role Classification\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-sm text-gray-400\",\n                                                        children: \"AI analyzes & routes to optimal model\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-78bab6ba4a187045\" + \" \" + \"bg-slate-800 border-2 border-slate-700 rounded-xl p-6 relative shadow-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"absolute inset-0 rounded-xl border-2 border-dashed border-[#ff6b35] opacity-40 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"relative z-10 space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-center mb-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"w-16 h-16 bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-lg shadow-orange-500/30\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                            className: \"w-8 h-8 text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                            lineNumber: 221,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 220,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-sm text-gray-400\",\n                                                                        children: \"Gemini 2.0 Flash Classifier\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 223,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 219,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-78bab6ba4a187045\" + \" \" + \"\".concat(currentExample.bgColor, \" \").concat(currentExample.borderColor, \" border-2 rounded-lg p-4 text-center shadow-lg \").concat(currentExample.glowColor),\n                                                                children: [\n                                                                    /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().createElement(currentExample.icon, {\n                                                                        className: \"h-6 w-6 \".concat(currentExample.color, \" mx-auto mb-2\")\n                                                                    }),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-xs text-gray-400 mb-1\",\n                                                                        children: \"Classified as\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 231,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"font-semibold text-sm \".concat(currentExample.color),\n                                                                        children: currentExample.roleName\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 232,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-xs text-gray-500 mt-1\",\n                                                                        children: [\n                                                                            \"Role ID: \",\n                                                                            currentExample.role\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 235,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 227,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-78bab6ba4a187045\" + \" \" + \"grid grid-cols-2 gap-2 text-xs\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"bg-slate-700 rounded p-2 text-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-green-400 mb-1\",\n                                                                                children: \"✓ Context Analysis\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 243,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-gray-500\",\n                                                                                children: \"Complete\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 244,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 242,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"bg-slate-700 rounded p-2 text-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-green-400 mb-1\",\n                                                                                children: \"✓ Role Matching\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 247,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-gray-500\",\n                                                                                children: \"Complete\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 248,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 246,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 241,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"absolute left-0 top-1/2 transform -translate-x-1/2 -translate-y-1/2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-78bab6ba4a187045\" + \" \" + \"flex space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.6s'\n                                                                    },\n                                                                    className: \"jsx-78bab6ba4a187045\" + \" \" + \"w-1 h-4 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 256,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.4s'\n                                                                    },\n                                                                    className: \"jsx-78bab6ba4a187045\" + \" \" + \"w-1 h-6 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 257,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.2s'\n                                                                    },\n                                                                    className: \"jsx-78bab6ba4a187045\" + \" \" + \"w-1 h-8 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 258,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 255,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-78bab6ba4a187045\" + \" \" + \"flex space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-78bab6ba4a187045\" + \" \" + \"w-1 h-8 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 263,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.2s'\n                                                                    },\n                                                                    className: \"jsx-78bab6ba4a187045\" + \" \" + \"w-1 h-6 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 264,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.4s'\n                                                                    },\n                                                                    className: \"jsx-78bab6ba4a187045\" + \" \" + \"w-1 h-4 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 265,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-78bab6ba4a187045\" + \" \" + \"absolute left-0 top-1/2 transform -translate-x-full -translate-y-1/2 hidden lg:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-78bab6ba4a187045\" + \" \" + \"relative w-16 h-px\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-78bab6ba4a187045\" + \" \" + \"absolute inset-0 bg-gradient-to-r from-transparent via-[#ff6b35] to-transparent opacity-60\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-78bab6ba4a187045\" + \" \" + \"absolute top-0 right-0 w-4 h-px bg-gradient-to-l from-white to-[#ff6b35] flow-left shadow-lg shadow-orange-500/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 274,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                animationDelay: '0.3s'\n                                                            },\n                                                            className: \"jsx-78bab6ba4a187045\" + \" \" + \"absolute top-0 right-0 w-2 h-px bg-white flow-left shadow-sm shadow-white/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 275,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-78bab6ba4a187045\" + \" \" + \"absolute right-0 top-1/2 transform translate-x-full -translate-y-1/2 hidden lg:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-78bab6ba4a187045\" + \" \" + \"relative w-16 h-px\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-78bab6ba4a187045\" + \" \" + \"absolute inset-0 bg-gradient-to-r from-[#ff6b35] to-transparent opacity-60\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-78bab6ba4a187045\" + \" \" + \"absolute top-0 left-0 w-4 h-px bg-gradient-to-r from-white to-[#ff6b35] flow-right shadow-lg shadow-orange-500/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                animationDelay: '0.7s'\n                                                            },\n                                                            className: \"jsx-78bab6ba4a187045\" + \" \" + \"absolute top-0 left-0 w-2 h-px bg-white flow-right shadow-sm shadow-white/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, \"classification-\".concat(activeExample), true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: 50\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 0.6\n                                        },\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-xl font-semibold text-white mb-2\",\n                                                        children: \"Optimal Model Selection\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-sm text-gray-400\",\n                                                        children: \"Routed to the perfect model\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-78bab6ba4a187045\" + \" \" + \"bg-gradient-to-br from-slate-800 to-slate-900 border-2 border-green-500/30 rounded-xl p-6 relative shadow-xl shadow-green-500/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"absolute inset-0 rounded-xl border-2 border-dashed border-green-400 opacity-50 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"relative z-10\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-center mb-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-lg shadow-green-500/30\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                            className: \"w-8 h-8 text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                            lineNumber: 308,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 307,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-lg font-bold text-white mb-1\",\n                                                                        children: currentExample.model\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 310,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-sm text-green-400 font-medium\",\n                                                                        children: currentExample.provider\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 311,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 306,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-78bab6ba4a187045\" + \" \" + \"\".concat(currentExample.bgColor, \" \").concat(currentExample.borderColor, \" border-2 rounded-lg p-4 text-center mb-4 shadow-lg \").concat(currentExample.glowColor),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-xs text-gray-400 mb-1\",\n                                                                        children: \"Assigned Role\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 316,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"font-semibold text-sm \".concat(currentExample.color),\n                                                                        children: currentExample.roleName\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 317,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-78bab6ba4a187045\" + \" \" + \"grid grid-cols-2 gap-2 text-xs\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"bg-slate-700 rounded p-2 text-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-green-400 mb-1\",\n                                                                                children: \"⚡ Speed\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 325,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-white font-medium\",\n                                                                                children: \"Optimal\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 326,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 324,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"bg-slate-700 rounded p-2 text-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-blue-400 mb-1\",\n                                                                                children: \"\\uD83C\\uDFAF Accuracy\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 329,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-white font-medium\",\n                                                                                children: \"High\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 330,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 328,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-78bab6ba4a187045\" + \" \" + \"mt-4 flex items-center justify-center text-green-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"w-4 h-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 336,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-sm font-medium\",\n                                                                        children: \"Route Established\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 337,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"absolute left-0 top-1/2 transform -translate-x-1/2 -translate-y-1/2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-78bab6ba4a187045\" + \" \" + \"flex space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.8s'\n                                                                    },\n                                                                    className: \"jsx-78bab6ba4a187045\" + \" \" + \"w-1 h-4 bg-green-400 rounded-full animate-pulse shadow-lg shadow-green-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 344,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.6s'\n                                                                    },\n                                                                    className: \"jsx-78bab6ba4a187045\" + \" \" + \"w-1 h-6 bg-green-400 rounded-full animate-pulse shadow-lg shadow-green-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 345,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.4s'\n                                                                    },\n                                                                    className: \"jsx-78bab6ba4a187045\" + \" \" + \"w-1 h-8 bg-green-400 rounded-full animate-pulse shadow-lg shadow-green-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 346,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-78bab6ba4a187045\" + \" \" + \"absolute left-0 top-1/2 transform -translate-x-full -translate-y-1/2 hidden lg:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-78bab6ba4a187045\" + \" \" + \"relative w-16 h-px\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-78bab6ba4a187045\" + \" \" + \"absolute inset-0 bg-gradient-to-r from-transparent via-green-400 to-green-400 opacity-60\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-78bab6ba4a187045\" + \" \" + \"absolute top-0 right-0 w-4 h-px bg-gradient-to-l from-white to-green-400 flow-left shadow-lg shadow-green-500/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                animationDelay: '1s'\n                                                            },\n                                                            className: \"jsx-78bab6ba4a187045\" + \" \" + \"absolute top-0 right-0 w-2 h-px bg-white flow-left shadow-sm shadow-white/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, \"model-\".concat(activeExample), true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-78bab6ba4a187045\" + \" \" + \"flex justify-center mt-12 space-x-2\",\n                                children: routingExamples.map((example, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveExample(index),\n                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"w-3 h-3 rounded-full transition-all duration-200 \".concat(index === activeExample ? 'bg-[#ff6b35] scale-125' : 'bg-gray-500 hover:bg-gray-400')\n                                    }, example.id, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-8 mt-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center mx-auto mb-4 shadow-lg shadow-blue-500/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-6 w-6 text-blue-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-lg font-semibold text-white mb-2\",\n                                        children: \"Role-Based Classification\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-gray-400\",\n                                        children: \"Gemini 2.0 Flash analyzes context and classifies requests into 15+ specialized roles\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center mx-auto mb-4 shadow-lg shadow-green-500/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-6 w-6 text-green-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-lg font-semibold text-white mb-2\",\n                                        children: \"Smart Model Matching\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-gray-400\",\n                                        children: \"Each role routes to your pre-configured optimal model for maximum performance\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center mx-auto mb-4 shadow-lg shadow-purple-500/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-6 w-6 text-purple-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-lg font-semibold text-white mb-2\",\n                                        children: \"Contextual Continuity\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-gray-400\",\n                                        children: \"Maintains conversation context and role consistency across multi-turn interactions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"w-12 h-12 bg-orange-500/20 rounded-lg flex items-center justify-center mx-auto mb-4 shadow-lg shadow-orange-500/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-6 w-6 text-orange-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-lg font-semibold text-white mb-2\",\n                                        children: \"Fallback Protection\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-gray-400\",\n                                        children: \"Automatic fallback to default general chat model when no role match is found\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            delay: 0.2\n                        },\n                        className: \"mt-16 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-xl font-semibold text-white mb-8\",\n                                children: \"Supported Role Types\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 426,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-78bab6ba4a187045\" + \" \" + \"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 max-w-4xl mx-auto\",\n                                children: [\n                                    {\n                                        name: 'General Chat',\n                                        color: 'text-gray-400'\n                                    },\n                                    {\n                                        name: 'Logic & Reasoning',\n                                        color: 'text-blue-400'\n                                    },\n                                    {\n                                        name: 'Writing & Content',\n                                        color: 'text-purple-400'\n                                    },\n                                    {\n                                        name: 'Frontend Coding',\n                                        color: 'text-green-400'\n                                    },\n                                    {\n                                        name: 'Backend Coding',\n                                        color: 'text-emerald-400'\n                                    },\n                                    {\n                                        name: 'Research & Analysis',\n                                        color: 'text-cyan-400'\n                                    },\n                                    {\n                                        name: 'Summarization',\n                                        color: 'text-yellow-400'\n                                    },\n                                    {\n                                        name: 'Translation',\n                                        color: 'text-pink-400'\n                                    },\n                                    {\n                                        name: 'Data Extraction',\n                                        color: 'text-indigo-400'\n                                    },\n                                    {\n                                        name: 'Brainstorming',\n                                        color: 'text-orange-400'\n                                    },\n                                    {\n                                        name: 'Education',\n                                        color: 'text-red-400'\n                                    },\n                                    {\n                                        name: 'Audio Transcription',\n                                        color: 'text-teal-400'\n                                    }\n                                ].map((role, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-78bab6ba4a187045\" + \" \" + \"bg-slate-800 rounded-lg p-3 border border-slate-700\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-78bab6ba4a187045\" + \" \" + \"text-sm font-medium \".concat(role.color),\n                                            children: role.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, role.name, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 427,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\n_s(RoutingVisualization, \"SlEVdab/r4hG20wt70ej7grkDC0=\");\n_c = RoutingVisualization;\nvar _c;\n$RefreshReg$(_c, \"RoutingVisualization\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2xhbmRpbmcvUm91dGluZ1Zpc3VhbGl6YXRpb24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTBCO0FBQ2E7QUFDSztBQWFQO0FBRXJDLE1BQU1XLGtCQUFrQjtJQUN0QjtRQUNFQyxJQUFJO1FBQ0pDLFFBQVE7UUFDUkMsTUFBTTtRQUNOQyxVQUFVO1FBQ1ZDLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxNQUFNZCxnTEFBUUE7UUFDZGUsT0FBTztRQUNQQyxTQUFTO1FBQ1RDLGFBQWE7UUFDYkMsV0FBVztJQUNiO0lBQ0E7UUFDRVYsSUFBSTtRQUNKQyxRQUFRO1FBQ1JDLE1BQU07UUFDTkMsVUFBVTtRQUNWQyxPQUFPO1FBQ1BDLFVBQVU7UUFDVkMsTUFBTVosZ0xBQVVBO1FBQ2hCYSxPQUFPO1FBQ1BDLFNBQVM7UUFDVEMsYUFBYTtRQUNiQyxXQUFXO0lBQ2I7SUFDQTtRQUNFVixJQUFJO1FBQ0pDLFFBQVE7UUFDUkMsTUFBTTtRQUNOQyxVQUFVO1FBQ1ZDLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxNQUFNYixnTEFBZUE7UUFDckJjLE9BQU87UUFDUEMsU0FBUztRQUNUQyxhQUFhO1FBQ2JDLFdBQVc7SUFDYjtJQUNBO1FBQ0VWLElBQUk7UUFDSkMsUUFBUTtRQUNSQyxNQUFNO1FBQ05DLFVBQVU7UUFDVkMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZDLE1BQU1YLGdMQUFZQTtRQUNsQlksT0FBTztRQUNQQyxTQUFTO1FBQ1RDLGFBQWE7UUFDYkMsV0FBVztJQUNiO0NBQ0Q7QUFFYyxTQUFTQzs7SUFDdEIsTUFBTSxDQUFDQyxlQUFlQyxpQkFBaUIsR0FBR3ZCLCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQ3dCLGFBQWFDLGVBQWUsR0FBR3pCLCtDQUFRQSxDQUFDO0lBRS9DQyxnREFBU0E7MENBQUM7WUFDUixNQUFNeUIsV0FBV0M7MkRBQVk7b0JBQzNCRixlQUFlO29CQUNmRzttRUFBVzs0QkFDVEw7MkVBQWlCLENBQUNNLE9BQVMsQ0FBQ0EsT0FBTyxLQUFLcEIsZ0JBQWdCcUIsTUFBTTs7NEJBQzlETCxlQUFlO3dCQUNqQjtrRUFBRztnQkFDTDswREFBRyxPQUFPLHVDQUF1QztZQUVqRDtrREFBTyxJQUFNTSxjQUFjTDs7UUFDN0I7eUNBQUcsRUFBRTtJQUVMLE1BQU1NLGlCQUFpQnZCLGVBQWUsQ0FBQ2EsY0FBYztJQUVyRCxxQkFDRSw4REFBQ1c7a0RBQWtCOzs7Ozs7MEJBMEJqQiw4REFBQ0M7MERBQWM7MEJBQ2IsNEVBQUNBOzhEQUFjOzs7Ozs7Ozs7OzswQkFHakIsOERBQUNBOzBEQUFjOztrQ0FFYiw4REFBQ0E7a0VBQWM7OzBDQUNiLDhEQUFDbkMsaURBQU1BLENBQUNvQyxFQUFFO2dDQUNSQyxTQUFTO29DQUFFQyxTQUFTO29DQUFHQyxHQUFHO2dDQUFHO2dDQUM3QkMsYUFBYTtvQ0FBRUYsU0FBUztvQ0FBR0MsR0FBRztnQ0FBRTtnQ0FDaENFLFVBQVU7b0NBQUVDLE1BQU07Z0NBQUs7Z0NBQ3ZCQyxXQUFVOzBDQUNYOzs7Ozs7MENBR0QsOERBQUMzQyxpREFBTUEsQ0FBQzRDLENBQUM7Z0NBQ1BQLFNBQVM7b0NBQUVDLFNBQVM7b0NBQUdDLEdBQUc7Z0NBQUc7Z0NBQzdCQyxhQUFhO29DQUFFRixTQUFTO29DQUFHQyxHQUFHO2dDQUFFO2dDQUNoQ0UsVUFBVTtvQ0FBRUMsTUFBTTtnQ0FBSztnQ0FDdkJHLFlBQVk7b0NBQUVDLE9BQU87Z0NBQUk7Z0NBQ3pCSCxXQUFVOzBDQUNYOzs7Ozs7Ozs7Ozs7a0NBTUgsOERBQUNSO2tFQUFjOzswQ0FDYiw4REFBQ0E7MEVBQWM7O2tEQUdiLDhEQUFDbkMsaURBQU1BLENBQUNtQyxHQUFHO3dDQUVURSxTQUFTOzRDQUFFQyxTQUFTOzRDQUFHUyxHQUFHLENBQUM7d0NBQUc7d0NBQzlCQyxTQUFTOzRDQUFFVixTQUFTOzRDQUFHUyxHQUFHO3dDQUFFO3dDQUM1QkYsWUFBWTs0Q0FBRUksVUFBVTt3Q0FBSTt3Q0FDNUJOLFdBQVU7OzBEQUVWLDhEQUFDUjswRkFBYzs7a0VBQ2IsOERBQUNlO2tHQUFhO2tFQUF3Qzs7Ozs7O2tFQUN0RCw4REFBQ047a0dBQVk7a0VBQXdCOzs7Ozs7Ozs7Ozs7MERBR3ZDLDhEQUFDVDswRkFBZSx5QkFBK0VGLE9BQXREQSxlQUFlYixXQUFXLEVBQUMsNkJBQStEYSxPQUFwQ0EsZUFBZWQsT0FBTyxFQUFDLGVBQXNDLE9BQXpCYyxlQUFlWixTQUFTOztrRUFFekosOERBQUNjO2tHQUFjOzs7Ozs7a0VBR2YsOERBQUNBO2tHQUFjOzswRUFDYiw4REFBQ0E7MEdBQWM7MEVBQTZCOzs7Ozs7MEVBQzVDLDhEQUFDQTswR0FBYzs7b0VBQXNEO29FQUNqRUYsZUFBZXJCLE1BQU07b0VBQUM7Ozs7Ozs7MEVBRTFCLDhEQUFDdUI7MEdBQWM7O2tGQUNiLDhEQUFDQTtrSEFBZ0I7Ozs7OztvRUFBNkQ7Ozs7Ozs7Ozs7Ozs7a0VBTWxGLDhEQUFDQTtrR0FBYztrRUFDYiw0RUFBQ0E7c0dBQWM7OzhFQUNiLDhEQUFDQTs4R0FBYzs7Ozs7OzhFQUNmLDhEQUFDQTtvRUFBK0ZnQixPQUFPO3dFQUFDQyxnQkFBZ0I7b0VBQU07OEdBQS9HOzs7Ozs7OEVBQ2YsOERBQUNqQjtvRUFBK0ZnQixPQUFPO3dFQUFDQyxnQkFBZ0I7b0VBQU07OEdBQS9HOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFNckIsOERBQUNqQjswRkFBYzswREFDYiw0RUFBQ0E7OEZBQWM7O3NFQUNiLDhEQUFDQTtzR0FBYzs7Ozs7O3NFQUNmLDhEQUFDQTtzR0FBYzs7Ozs7O3NFQUNmLDhEQUFDQTs0REFBNkZnQixPQUFPO2dFQUFDQyxnQkFBZ0I7NERBQU07c0dBQTdHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7dUNBMUNkLFVBQXdCLE9BQWQ3Qjs7Ozs7a0RBZ0RqQiw4REFBQ3ZCLGlEQUFNQSxDQUFDbUMsR0FBRzt3Q0FFVEUsU0FBUzs0Q0FBRUMsU0FBUzs0Q0FBR2UsT0FBTzt3Q0FBSTt3Q0FDbENMLFNBQVM7NENBQUVWLFNBQVM7NENBQUdlLE9BQU87d0NBQUU7d0NBQ2hDUixZQUFZOzRDQUFFSSxVQUFVOzRDQUFLSCxPQUFPO3dDQUFJO3dDQUN4Q0gsV0FBVTs7MERBRVYsOERBQUNSOzBGQUFjOztrRUFDYiw4REFBQ2U7a0dBQWE7a0VBQXdDOzs7Ozs7a0VBQ3RELDhEQUFDTjtrR0FBWTtrRUFBd0I7Ozs7Ozs7Ozs7OzswREFHdkMsOERBQUNUOzBGQUFjOztrRUFFYiw4REFBQ0E7a0dBQWM7Ozs7OztrRUFHZiw4REFBQ0E7a0dBQWM7OzBFQUViLDhEQUFDQTswR0FBYzs7a0ZBQ2IsOERBQUNBO2tIQUFjO2tGQUNiLDRFQUFDaEMsZ0xBQVFBOzRFQUFDd0MsV0FBVTs7Ozs7Ozs7Ozs7a0ZBRXRCLDhEQUFDUjtrSEFBYztrRkFBd0I7Ozs7Ozs7Ozs7OzswRUFJekMsOERBQUNBOzBHQUFlLEdBQTZCRixPQUExQkEsZUFBZWQsT0FBTyxFQUFDLEtBQStFYyxPQUE1RUEsZUFBZWIsV0FBVyxFQUFDLG1EQUEwRSxPQUF6QmEsZUFBZVosU0FBUzs7a0ZBQzlJdEIsMERBQW1CLENBQUNrQyxlQUFlaEIsSUFBSSxFQUFFO3dFQUN4QzBCLFdBQVcsV0FBZ0MsT0FBckJWLGVBQWVmLEtBQUssRUFBQztvRUFDN0M7a0ZBQ0EsOERBQUNpQjtrSEFBYztrRkFBNkI7Ozs7OztrRkFDNUMsOERBQUNBO2tIQUFlLHlCQUE4QyxPQUFyQkYsZUFBZWYsS0FBSztrRkFDMURlLGVBQWVuQixRQUFROzs7Ozs7a0ZBRTFCLDhEQUFDcUI7a0hBQWM7OzRFQUE2Qjs0RUFDaENGLGVBQWVwQixJQUFJOzs7Ozs7Ozs7Ozs7OzBFQUtqQyw4REFBQ3NCOzBHQUFjOztrRkFDYiw4REFBQ0E7a0hBQWM7OzBGQUNiLDhEQUFDQTswSEFBYzswRkFBc0I7Ozs7OzswRkFDckMsOERBQUNBOzBIQUFjOzBGQUFnQjs7Ozs7Ozs7Ozs7O2tGQUVqQyw4REFBQ0E7a0hBQWM7OzBGQUNiLDhEQUFDQTswSEFBYzswRkFBc0I7Ozs7OzswRkFDckMsOERBQUNBOzBIQUFjOzBGQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tFQU1yQyw4REFBQ0E7a0dBQWM7a0VBQ2IsNEVBQUNBO3NHQUFjOzs4RUFDYiw4REFBQ0E7b0VBQStGZ0IsT0FBTzt3RUFBQ0MsZ0JBQWdCO29FQUFNOzhHQUEvRzs7Ozs7OzhFQUNmLDhEQUFDakI7b0VBQStGZ0IsT0FBTzt3RUFBQ0MsZ0JBQWdCO29FQUFNOzhHQUEvRzs7Ozs7OzhFQUNmLDhEQUFDakI7b0VBQStGZ0IsT0FBTzt3RUFBQ0MsZ0JBQWdCO29FQUFNOzhHQUEvRzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBR25CLDhEQUFDakI7a0dBQWM7a0VBQ2IsNEVBQUNBO3NHQUFjOzs4RUFDYiw4REFBQ0E7OEdBQWM7Ozs7Ozs4RUFDZiw4REFBQ0E7b0VBQStGZ0IsT0FBTzt3RUFBQ0MsZ0JBQWdCO29FQUFNOzhHQUEvRzs7Ozs7OzhFQUNmLDhEQUFDakI7b0VBQStGZ0IsT0FBTzt3RUFBQ0MsZ0JBQWdCO29FQUFNOzhHQUEvRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBTXJCLDhEQUFDakI7MEZBQWM7MERBQ2IsNEVBQUNBOzhGQUFjOztzRUFDYiw4REFBQ0E7c0dBQWM7Ozs7OztzRUFDZiw4REFBQ0E7c0dBQWM7Ozs7OztzRUFDZiw4REFBQ0E7NERBQTZGZ0IsT0FBTztnRUFBQ0MsZ0JBQWdCOzREQUFNO3NHQUE3Rzs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBR25CLDhEQUFDakI7MEZBQWM7MERBQ2IsNEVBQUNBOzhGQUFjOztzRUFDYiw4REFBQ0E7c0dBQWM7Ozs7OztzRUFDZiw4REFBQ0E7c0dBQWM7Ozs7OztzRUFDZiw4REFBQ0E7NERBQTZGZ0IsT0FBTztnRUFBQ0MsZ0JBQWdCOzREQUFNO3NHQUE3Rzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3VDQWpGZCxrQkFBZ0MsT0FBZDdCOzs7OztrREF1RnpCLDhEQUFDdkIsaURBQU1BLENBQUNtQyxHQUFHO3dDQUVURSxTQUFTOzRDQUFFQyxTQUFTOzRDQUFHUyxHQUFHO3dDQUFHO3dDQUM3QkMsU0FBUzs0Q0FBRVYsU0FBUzs0Q0FBR1MsR0FBRzt3Q0FBRTt3Q0FDNUJGLFlBQVk7NENBQUVJLFVBQVU7NENBQUtILE9BQU87d0NBQUk7d0NBQ3hDSCxXQUFVOzswREFFViw4REFBQ1I7MEZBQWM7O2tFQUNiLDhEQUFDZTtrR0FBYTtrRUFBd0M7Ozs7OztrRUFDdEQsOERBQUNOO2tHQUFZO2tFQUF3Qjs7Ozs7Ozs7Ozs7OzBEQUd2Qyw4REFBQ1Q7MEZBQWM7O2tFQUViLDhEQUFDQTtrR0FBYzs7Ozs7O2tFQUVmLDhEQUFDQTtrR0FBYzs7MEVBRWIsOERBQUNBOzBHQUFjOztrRkFDYiw4REFBQ0E7a0hBQWM7a0ZBQ2IsNEVBQUM1QixnTEFBV0E7NEVBQUNvQyxXQUFVOzs7Ozs7Ozs7OztrRkFFekIsOERBQUNSO2tIQUFjO2tGQUFxQ0YsZUFBZWxCLEtBQUs7Ozs7OztrRkFDeEUsOERBQUNvQjtrSEFBYztrRkFBc0NGLGVBQWVqQixRQUFROzs7Ozs7Ozs7Ozs7MEVBSTlFLDhEQUFDbUI7MEdBQWUsR0FBNkJGLE9BQTFCQSxlQUFlZCxPQUFPLEVBQUMsS0FBb0ZjLE9BQWpGQSxlQUFlYixXQUFXLEVBQUMsd0RBQStFLE9BQXpCYSxlQUFlWixTQUFTOztrRkFDcEosOERBQUNjO2tIQUFjO2tGQUE2Qjs7Ozs7O2tGQUM1Qyw4REFBQ0E7a0hBQWUseUJBQThDLE9BQXJCRixlQUFlZixLQUFLO2tGQUMxRGUsZUFBZW5CLFFBQVE7Ozs7Ozs7Ozs7OzswRUFLNUIsOERBQUNxQjswR0FBYzs7a0ZBQ2IsOERBQUNBO2tIQUFjOzswRkFDYiw4REFBQ0E7MEhBQWM7MEZBQXNCOzs7Ozs7MEZBQ3JDLDhEQUFDQTswSEFBYzswRkFBeUI7Ozs7Ozs7Ozs7OztrRkFFMUMsOERBQUNBO2tIQUFjOzswRkFDYiw4REFBQ0E7MEhBQWM7MEZBQXFCOzs7Ozs7MEZBQ3BDLDhEQUFDQTswSEFBYzswRkFBeUI7Ozs7Ozs7Ozs7Ozs7Ozs7OzswRUFLNUMsOERBQUNBOzBHQUFjOztrRkFDYiw4REFBQzFCLGdMQUFTQTt3RUFBQ2tDLFdBQVU7Ozs7OztrRkFDckIsOERBQUNZO2tIQUFlO2tGQUFzQjs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tFQUsxQyw4REFBQ3BCO2tHQUFjO2tFQUNiLDRFQUFDQTtzR0FBYzs7OEVBQ2IsOERBQUNBO29FQUE4RmdCLE9BQU87d0VBQUNDLGdCQUFnQjtvRUFBTTs4R0FBOUc7Ozs7Ozs4RUFDZiw4REFBQ2pCO29FQUE4RmdCLE9BQU87d0VBQUNDLGdCQUFnQjtvRUFBTTs4R0FBOUc7Ozs7Ozs4RUFDZiw4REFBQ2pCO29FQUE4RmdCLE9BQU87d0VBQUNDLGdCQUFnQjtvRUFBTTs4R0FBOUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQU1yQiw4REFBQ2pCOzBGQUFjOzBEQUNiLDRFQUFDQTs4RkFBYzs7c0VBQ2IsOERBQUNBO3NHQUFjOzs7Ozs7c0VBQ2YsOERBQUNBO3NHQUFjOzs7Ozs7c0VBQ2YsOERBQUNBOzREQUE2RmdCLE9BQU87Z0VBQUNDLGdCQUFnQjs0REFBSTtzR0FBM0c7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt1Q0FuRWQsU0FBdUIsT0FBZDdCOzs7Ozs7Ozs7OzswQ0EwRWxCLDhEQUFDWTswRUFBYzswQ0FDWnpCLGdCQUFnQjhDLEdBQUcsQ0FBQyxDQUFDQyxTQUFTQyxzQkFDN0IsOERBQUNDO3dDQUVDQyxTQUFTLElBQU1wQyxpQkFBaUJrQztrRkFDckIsb0RBSVYsT0FIQ0EsVUFBVW5DLGdCQUNOLDJCQUNBO3VDQUxEa0MsUUFBUTlDLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBYXZCLDhEQUFDWCxpREFBTUEsQ0FBQ21DLEdBQUc7d0JBQ1RFLFNBQVM7NEJBQUVDLFNBQVM7NEJBQUdDLEdBQUc7d0JBQUc7d0JBQzdCQyxhQUFhOzRCQUFFRixTQUFTOzRCQUFHQyxHQUFHO3dCQUFFO3dCQUNoQ0UsVUFBVTs0QkFBRUMsTUFBTTt3QkFBSzt3QkFDdkJDLFdBQVU7OzBDQUVWLDhEQUFDUjswRUFBYzs7a0RBQ2IsOERBQUNBO2tGQUFjO2tEQUNiLDRFQUFDaEMsZ0xBQVFBOzRDQUFDd0MsV0FBVTs7Ozs7Ozs7Ozs7a0RBRXRCLDhEQUFDTztrRkFBYTtrREFBd0M7Ozs7OztrREFDdEQsOERBQUNOO2tGQUFZO2tEQUFnQjs7Ozs7Ozs7Ozs7OzBDQUcvQiw4REFBQ1Q7MEVBQWM7O2tEQUNiLDhEQUFDQTtrRkFBYztrREFDYiw0RUFBQzVCLGdMQUFXQTs0Q0FBQ29DLFdBQVU7Ozs7Ozs7Ozs7O2tEQUV6Qiw4REFBQ087a0ZBQWE7a0RBQXdDOzs7Ozs7a0RBQ3RELDhEQUFDTjtrRkFBWTtrREFBZ0I7Ozs7Ozs7Ozs7OzswQ0FHL0IsOERBQUNUOzBFQUFjOztrREFDYiw4REFBQ0E7a0ZBQWM7a0RBQ2IsNEVBQUM3QixnTEFBWUE7NENBQUNxQyxXQUFVOzs7Ozs7Ozs7OztrREFFMUIsOERBQUNPO2tGQUFhO2tEQUF3Qzs7Ozs7O2tEQUN0RCw4REFBQ047a0ZBQVk7a0RBQWdCOzs7Ozs7Ozs7Ozs7MENBRy9CLDhEQUFDVDswRUFBYzs7a0RBQ2IsOERBQUNBO2tGQUFjO2tEQUNiLDRFQUFDM0IsaUxBQWVBOzRDQUFDbUMsV0FBVTs7Ozs7Ozs7Ozs7a0RBRTdCLDhEQUFDTztrRkFBYTtrREFBd0M7Ozs7OztrREFDdEQsOERBQUNOO2tGQUFZO2tEQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQUtqQyw4REFBQzVDLGlEQUFNQSxDQUFDbUMsR0FBRzt3QkFDVEUsU0FBUzs0QkFBRUMsU0FBUzs0QkFBR0MsR0FBRzt3QkFBRzt3QkFDN0JDLGFBQWE7NEJBQUVGLFNBQVM7NEJBQUdDLEdBQUc7d0JBQUU7d0JBQ2hDRSxVQUFVOzRCQUFFQyxNQUFNO3dCQUFLO3dCQUN2QkcsWUFBWTs0QkFBRUMsT0FBTzt3QkFBSTt3QkFDekJILFdBQVU7OzBDQUVWLDhEQUFDa0I7MEVBQWE7MENBQXdDOzs7Ozs7MENBQ3RELDhEQUFDMUI7MEVBQWM7MENBQ1o7b0NBQ0M7d0NBQUUyQixNQUFNO3dDQUFnQjVDLE9BQU87b0NBQWdCO29DQUMvQzt3Q0FBRTRDLE1BQU07d0NBQXFCNUMsT0FBTztvQ0FBZ0I7b0NBQ3BEO3dDQUFFNEMsTUFBTTt3Q0FBcUI1QyxPQUFPO29DQUFrQjtvQ0FDdEQ7d0NBQUU0QyxNQUFNO3dDQUFtQjVDLE9BQU87b0NBQWlCO29DQUNuRDt3Q0FBRTRDLE1BQU07d0NBQWtCNUMsT0FBTztvQ0FBbUI7b0NBQ3BEO3dDQUFFNEMsTUFBTTt3Q0FBdUI1QyxPQUFPO29DQUFnQjtvQ0FDdEQ7d0NBQUU0QyxNQUFNO3dDQUFpQjVDLE9BQU87b0NBQWtCO29DQUNsRDt3Q0FBRTRDLE1BQU07d0NBQWU1QyxPQUFPO29DQUFnQjtvQ0FDOUM7d0NBQUU0QyxNQUFNO3dDQUFtQjVDLE9BQU87b0NBQWtCO29DQUNwRDt3Q0FBRTRDLE1BQU07d0NBQWlCNUMsT0FBTztvQ0FBa0I7b0NBQ2xEO3dDQUFFNEMsTUFBTTt3Q0FBYTVDLE9BQU87b0NBQWU7b0NBQzNDO3dDQUFFNEMsTUFBTTt3Q0FBdUI1QyxPQUFPO29DQUFnQjtpQ0FDdkQsQ0FBQ3NDLEdBQUcsQ0FBQyxDQUFDM0MsTUFBTTZDLHNCQUNYLDhEQUFDdkI7a0ZBQThCO2tEQUM3Qiw0RUFBQ0E7c0ZBQWUsdUJBQWtDLE9BQVh0QixLQUFLSyxLQUFLO3NEQUFLTCxLQUFLaUQsSUFBSTs7Ozs7O3VDQUR2RGpELEtBQUtpRCxJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBU2pDO0dBeFh3QnhDO0tBQUFBIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcY29tcG9uZW50c1xcbGFuZGluZ1xcUm91dGluZ1Zpc3VhbGl6YXRpb24udHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IG1vdGlvbiB9IGZyb20gJ2ZyYW1lci1tb3Rpb24nO1xuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7XG4gIEJvbHRJY29uLFxuICBDb2RlQnJhY2tldEljb24sXG4gIFBlbmNpbEljb24sXG4gIENoYXJ0QmFySWNvbixcbiAgQ3B1Q2hpcEljb24sXG4gIEFycm93UmlnaHRJY29uLFxuICBTaGllbGRDaGVja0ljb24sXG4gIEV5ZUljb24sXG4gIENvZ0ljb24sXG4gIENsb3VkSWNvbixcbiAgQ2hlY2tJY29uXG59IGZyb20gJ0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZSc7XG5cbmNvbnN0IHJvdXRpbmdFeGFtcGxlcyA9IFtcbiAge1xuICAgIGlkOiAxLFxuICAgIHByb21wdDogXCJTb2x2ZSB0aGlzIGNvbXBsZXggbWF0aCBwcm9ibGVtOiAyeCArIDUgPSAxNVwiLFxuICAgIHJvbGU6IFwibG9naWNfcmVhc29uaW5nXCIsXG4gICAgcm9sZU5hbWU6IFwiTG9naWMgJiBSZWFzb25pbmdcIixcbiAgICBtb2RlbDogXCJHUFQtNG9cIixcbiAgICBwcm92aWRlcjogXCJPcGVuQUlcIixcbiAgICBpY29uOiBCb2x0SWNvbixcbiAgICBjb2xvcjogXCJ0ZXh0LWJsdWUtNDAwXCIsXG4gICAgYmdDb2xvcjogXCJiZy1ibHVlLTUwMC8xMFwiLFxuICAgIGJvcmRlckNvbG9yOiBcImJvcmRlci1ibHVlLTUwMC8yMFwiLFxuICAgIGdsb3dDb2xvcjogXCJzaGFkb3ctYmx1ZS01MDAvNTBcIlxuICB9LFxuICB7XG4gICAgaWQ6IDIsXG4gICAgcHJvbXB0OiBcIldyaXRlIGEgYmxvZyBwb3N0IGFib3V0IEFJIHRyZW5kc1wiLFxuICAgIHJvbGU6IFwid3JpdGluZ1wiLFxuICAgIHJvbGVOYW1lOiBcIldyaXRpbmcgJiBDb250ZW50IENyZWF0aW9uXCIsXG4gICAgbW9kZWw6IFwiQ2xhdWRlIDMuNSBTb25uZXRcIixcbiAgICBwcm92aWRlcjogXCJBbnRocm9waWNcIixcbiAgICBpY29uOiBQZW5jaWxJY29uLFxuICAgIGNvbG9yOiBcInRleHQtcHVycGxlLTQwMFwiLFxuICAgIGJnQ29sb3I6IFwiYmctcHVycGxlLTUwMC8xMFwiLFxuICAgIGJvcmRlckNvbG9yOiBcImJvcmRlci1wdXJwbGUtNTAwLzIwXCIsXG4gICAgZ2xvd0NvbG9yOiBcInNoYWRvdy1wdXJwbGUtNTAwLzUwXCJcbiAgfSxcbiAge1xuICAgIGlkOiAzLFxuICAgIHByb21wdDogXCJCdWlsZCBhIFJlYWN0IGNvbXBvbmVudCB3aXRoIFR5cGVTY3JpcHRcIixcbiAgICByb2xlOiBcImNvZGluZ19mcm9udGVuZFwiLFxuICAgIHJvbGVOYW1lOiBcIkZyb250ZW5kIERldmVsb3BtZW50XCIsXG4gICAgbW9kZWw6IFwiRGVlcFNlZWsgQ29kZXIgVjJcIixcbiAgICBwcm92aWRlcjogXCJEZWVwU2Vla1wiLFxuICAgIGljb246IENvZGVCcmFja2V0SWNvbixcbiAgICBjb2xvcjogXCJ0ZXh0LWdyZWVuLTQwMFwiLFxuICAgIGJnQ29sb3I6IFwiYmctZ3JlZW4tNTAwLzEwXCIsXG4gICAgYm9yZGVyQ29sb3I6IFwiYm9yZGVyLWdyZWVuLTUwMC8yMFwiLFxuICAgIGdsb3dDb2xvcjogXCJzaGFkb3ctZ3JlZW4tNTAwLzUwXCJcbiAgfSxcbiAge1xuICAgIGlkOiA0LFxuICAgIHByb21wdDogXCJTdW1tYXJpemUgdGhpcyByZXNlYXJjaCBwYXBlclwiLFxuICAgIHJvbGU6IFwicmVzZWFyY2hfc3ludGhlc2lzXCIsXG4gICAgcm9sZU5hbWU6IFwiUmVzZWFyY2ggJiBBbmFseXNpc1wiLFxuICAgIG1vZGVsOiBcIkdlbWluaSAyLjAgRmxhc2hcIixcbiAgICBwcm92aWRlcjogXCJHb29nbGVcIixcbiAgICBpY29uOiBDaGFydEJhckljb24sXG4gICAgY29sb3I6IFwidGV4dC1jeWFuLTQwMFwiLFxuICAgIGJnQ29sb3I6IFwiYmctY3lhbi01MDAvMTBcIixcbiAgICBib3JkZXJDb2xvcjogXCJib3JkZXItY3lhbi01MDAvMjBcIixcbiAgICBnbG93Q29sb3I6IFwic2hhZG93LWN5YW4tNTAwLzUwXCJcbiAgfVxuXTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm91dGluZ1Zpc3VhbGl6YXRpb24oKSB7XG4gIGNvbnN0IFthY3RpdmVFeGFtcGxlLCBzZXRBY3RpdmVFeGFtcGxlXSA9IHVzZVN0YXRlKDApO1xuICBjb25zdCBbaXNBbmltYXRpbmcsIHNldElzQW5pbWF0aW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGludGVydmFsID0gc2V0SW50ZXJ2YWwoKCkgPT4ge1xuICAgICAgc2V0SXNBbmltYXRpbmcodHJ1ZSk7XG4gICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgc2V0QWN0aXZlRXhhbXBsZSgocHJldikgPT4gKHByZXYgKyAxKSAlIHJvdXRpbmdFeGFtcGxlcy5sZW5ndGgpO1xuICAgICAgICBzZXRJc0FuaW1hdGluZyhmYWxzZSk7XG4gICAgICB9LCA1MDApO1xuICAgIH0sIDUwMDApOyAvLyBTbG93ZXIgdHJhbnNpdGlvbiBmb3IgYmV0dGVyIHZpZXdpbmdcblxuICAgIHJldHVybiAoKSA9PiBjbGVhckludGVydmFsKGludGVydmFsKTtcbiAgfSwgW10pO1xuXG4gIGNvbnN0IGN1cnJlbnRFeGFtcGxlID0gcm91dGluZ0V4YW1wbGVzW2FjdGl2ZUV4YW1wbGVdO1xuXG4gIHJldHVybiAoXG4gICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwicHktMjAgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1zbGF0ZS05MDAgdmlhLWdyYXktOTAwIHRvLXNsYXRlLTgwMCByZWxhdGl2ZSBvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgIHsvKiBDdXN0b20gQ1NTIGZvciBmbG93aW5nIGFuaW1hdGlvbnMgKi99XG4gICAgICA8c3R5bGUganN4PntgXG4gICAgICAgIEBrZXlmcmFtZXMgZmxvd1JpZ2h0IHtcbiAgICAgICAgICAwJSB7IHRyYW5zZm9ybTogdHJhbnNsYXRlWCgtMTAwJSk7IG9wYWNpdHk6IDA7IH1cbiAgICAgICAgICA1MCUgeyBvcGFjaXR5OiAxOyB9XG4gICAgICAgICAgMTAwJSB7IHRyYW5zZm9ybTogdHJhbnNsYXRlWCgxMDAlKTsgb3BhY2l0eTogMDsgfVxuICAgICAgICB9XG4gICAgICAgIEBrZXlmcmFtZXMgZmxvd0xlZnQge1xuICAgICAgICAgIDAlIHsgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKDEwMCUpOyBvcGFjaXR5OiAwOyB9XG4gICAgICAgICAgNTAlIHsgb3BhY2l0eTogMTsgfVxuICAgICAgICAgIDEwMCUgeyB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoLTEwMCUpOyBvcGFjaXR5OiAwOyB9XG4gICAgICAgIH1cbiAgICAgICAgLmZsb3ctcmlnaHQge1xuICAgICAgICAgIGFuaW1hdGlvbjogZmxvd1JpZ2h0IDJzIGVhc2UtaW4tb3V0IGluZmluaXRlO1xuICAgICAgICB9XG4gICAgICAgIC5mbG93LWxlZnQge1xuICAgICAgICAgIGFuaW1hdGlvbjogZmxvd0xlZnQgMnMgZWFzZS1pbi1vdXQgaW5maW5pdGU7XG4gICAgICAgICAgYW5pbWF0aW9uLWRlbGF5OiAxcztcbiAgICAgICAgfVxuICAgICAgICAuZ2xvdy1wdWxzZSB7XG4gICAgICAgICAgYW5pbWF0aW9uOiBwdWxzZSAycyBjdWJpYy1iZXppZXIoMC40LCAwLCAwLjYsIDEpIGluZmluaXRlO1xuICAgICAgICB9XG4gICAgICBgfTwvc3R5bGU+XG5cbiAgICAgIHsvKiBCYWNrZ3JvdW5kIFBhdHRlcm4gKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgb3BhY2l0eS0xMFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctZ3JpZC13aGl0ZS9bMC4wMl0gYmctW3NpemU6NjBweF82MHB4XVwiIC8+XG4gICAgICA8L2Rpdj5cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC00IHNtOnB4LTYgbGc6cHgtOCByZWxhdGl2ZVwiPlxuICAgICAgICB7LyogU2VjdGlvbiBIZWFkZXIgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWItMTZcIj5cbiAgICAgICAgICA8bW90aW9uLmgyXG4gICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgICB3aGlsZUluVmlldz17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgICB2aWV3cG9ydD17eyBvbmNlOiB0cnVlIH19XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LTR4bCBzbTp0ZXh0LTV4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi02XCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICBJbnRyb2R1Y2luZyB0aGUgQUkgR2F0ZXdheSBQYXR0ZXJuXG4gICAgICAgICAgPC9tb3Rpb24uaDI+XG4gICAgICAgICAgPG1vdGlvbi5wXG4gICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgICB3aGlsZUluVmlldz17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgICB2aWV3cG9ydD17eyBvbmNlOiB0cnVlIH19XG4gICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGRlbGF5OiAwLjEgfX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQteGwgdGV4dC1ncmF5LTMwMCBtYXgtdy0zeGwgbXgtYXV0b1wiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgV2F0Y2ggaG93IFJvdUtleSBpbnRlbGxpZ2VudGx5IHJvdXRlcyB5b3VyIHJlcXVlc3RzIHRocm91Z2ggb3VyIHVuaWZpZWQgZ2F0ZXdheSB0byB0aGUgcGVyZmVjdCBBSSBtb2RlbFxuICAgICAgICAgIDwvbW90aW9uLnA+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBJbnRlbGxpZ2VudCBSb2xlIFJvdXRpbmcgVmlzdWFsaXphdGlvbiAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBtYXgtdy03eGwgbXgtYXV0b1wiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBsZzpncmlkLWNvbHMtMyBnYXAtOCBsZzpnYXAtMTYgaXRlbXMtc3RhcnRcIj5cblxuICAgICAgICAgICAgey8qIENvbHVtbiAxOiBVc2VyIFByb21wdCAqL31cbiAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgIGtleT17YHByb21wdC0ke2FjdGl2ZUV4YW1wbGV9YH1cbiAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB4OiAtNTAgfX1cbiAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB4OiAwIH19XG4gICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuNiB9fVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWItNlwiPlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBtYi0yXCI+VXNlciBQcm9tcHQ8L2gzPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTQwMFwiPllvdXIgcmVxdWVzdCBlbnRlcnMgUm91S2V5PC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGJnLXNsYXRlLTgwMCBib3JkZXItMiAke2N1cnJlbnRFeGFtcGxlLmJvcmRlckNvbG9yfSByb3VuZGVkLXhsIHAtNiByZWxhdGl2ZSAke2N1cnJlbnRFeGFtcGxlLmJnQ29sb3J9IHNoYWRvdy1sZyAke2N1cnJlbnRFeGFtcGxlLmdsb3dDb2xvcn1gfT5cbiAgICAgICAgICAgICAgICB7LyogQW5pbWF0ZWQgYm9yZGVyIGdsb3cgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIHJvdW5kZWQteGwgYm9yZGVyLTIgYm9yZGVyLWRhc2hlZCBib3JkZXItZ3JheS01MDAgb3BhY2l0eS0zMCBhbmltYXRlLXB1bHNlXCI+PC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7LyogUHJvbXB0IGNvbnRlbnQgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB6LTEwXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTQwMCBtYi0zXCI+SW5jb21pbmcgUmVxdWVzdDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtbWVkaXVtIG1iLTQgdGV4dC1sZyBsZWFkaW5nLXJlbGF4ZWRcIj5cbiAgICAgICAgICAgICAgICAgICAgXCJ7Y3VycmVudEV4YW1wbGUucHJvbXB0fVwiXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgdGV4dC1zbSB0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgdy0yIGgtMiBiZy1bI2ZmNmIzNV0gcm91bmRlZC1mdWxsIG1yLTIgYW5pbWF0ZS1wdWxzZWB9PjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICBBbmFseXppbmcgcHJvbXB0Li4uXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIHsvKiBDb25uZWN0aW9uIGRvdHMgd2l0aCBnbG93ICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgcmlnaHQtMCB0b3AtMS8yIHRyYW5zZm9ybSB0cmFuc2xhdGUteC0xLzIgLXRyYW5zbGF0ZS15LTEvMlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMSBoLTggYmctWyNmZjZiMzVdIHJvdW5kZWQtZnVsbCBhbmltYXRlLXB1bHNlIHNoYWRvdy1sZyBzaGFkb3ctb3JhbmdlLTUwMC81MFwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMSBoLTYgYmctWyNmZjZiMzVdIHJvdW5kZWQtZnVsbCBhbmltYXRlLXB1bHNlIHNoYWRvdy1sZyBzaGFkb3ctb3JhbmdlLTUwMC81MFwiIHN0eWxlPXt7YW5pbWF0aW9uRGVsYXk6ICcwLjJzJ319PjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMSBoLTQgYmctWyNmZjZiMzVdIHJvdW5kZWQtZnVsbCBhbmltYXRlLXB1bHNlIHNoYWRvdy1sZyBzaGFkb3ctb3JhbmdlLTUwMC81MFwiIHN0eWxlPXt7YW5pbWF0aW9uRGVsYXk6ICcwLjRzJ319PjwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBBbmltYXRlZCBjb25uZWN0aW9uIGxpbmUgd2l0aCBmbG93aW5nIGN1cnJlbnQgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgcmlnaHQtMCB0b3AtMS8yIHRyYW5zZm9ybSB0cmFuc2xhdGUteC1mdWxsIC10cmFuc2xhdGUteS0xLzIgaGlkZGVuIGxnOmJsb2NrXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB3LTE2IGgtcHhcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ncmFkaWVudC10by1yIGZyb20tWyNmZjZiMzVdIHRvLXRyYW5zcGFyZW50IG9wYWNpdHktNjBcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTAgbGVmdC0wIHctNCBoLXB4IGJnLWdyYWRpZW50LXRvLXIgZnJvbS13aGl0ZSB0by1bI2ZmNmIzNV0gZmxvdy1yaWdodCBzaGFkb3ctbGcgc2hhZG93LW9yYW5nZS01MDAvNTBcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTAgbGVmdC0wIHctMiBoLXB4IGJnLXdoaXRlIGZsb3ctcmlnaHQgc2hhZG93LXNtIHNoYWRvdy13aGl0ZS81MFwiIHN0eWxlPXt7YW5pbWF0aW9uRGVsYXk6ICcwLjVzJ319PjwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cblxuICAgICAgICAgICAgey8qIENvbHVtbiAyOiBJbnRlbGxpZ2VudCBSb2xlIENsYXNzaWZpY2F0aW9uICovfVxuICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAga2V5PXtgY2xhc3NpZmljYXRpb24tJHthY3RpdmVFeGFtcGxlfWB9XG4gICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgc2NhbGU6IDAuOSB9fVxuICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHNjYWxlOiAxIH19XG4gICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuNiwgZGVsYXk6IDAuMyB9fVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWItNlwiPlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBtYi0yXCI+SW50ZWxsaWdlbnQgUm9sZSBDbGFzc2lmaWNhdGlvbjwvaDM+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNDAwXCI+QUkgYW5hbHl6ZXMgJiByb3V0ZXMgdG8gb3B0aW1hbCBtb2RlbDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1zbGF0ZS04MDAgYm9yZGVyLTIgYm9yZGVyLXNsYXRlLTcwMCByb3VuZGVkLXhsIHAtNiByZWxhdGl2ZSBzaGFkb3cteGxcIj5cbiAgICAgICAgICAgICAgICB7LyogQW5pbWF0ZWQgc2Nhbm5pbmcgYm9yZGVyICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCByb3VuZGVkLXhsIGJvcmRlci0yIGJvcmRlci1kYXNoZWQgYm9yZGVyLVsjZmY2YjM1XSBvcGFjaXR5LTQwIGFuaW1hdGUtcHVsc2VcIj48L2Rpdj5cblxuICAgICAgICAgICAgICAgIHsvKiBDbGFzc2lmaWNhdGlvbiBQcm9jZXNzICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgei0xMCBzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgIHsvKiBBSSBCcmFpbiBJY29uICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi02XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xNiBoLTE2IGJnLWdyYWRpZW50LXRvLWJyIGZyb20tWyNmZjZiMzVdIHRvLVsjZjc5MzFlXSByb3VuZGVkLTJ4bCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBteC1hdXRvIG1iLTMgc2hhZG93LWxnIHNoYWRvdy1vcmFuZ2UtNTAwLzMwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPEJvbHRJY29uIGNsYXNzTmFtZT1cInctOCBoLTggdGV4dC13aGl0ZVwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTQwMFwiPkdlbWluaSAyLjAgRmxhc2ggQ2xhc3NpZmllcjwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIHsvKiBSb2xlIERldGVjdGlvbiAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgJHtjdXJyZW50RXhhbXBsZS5iZ0NvbG9yfSAke2N1cnJlbnRFeGFtcGxlLmJvcmRlckNvbG9yfSBib3JkZXItMiByb3VuZGVkLWxnIHAtNCB0ZXh0LWNlbnRlciBzaGFkb3ctbGcgJHtjdXJyZW50RXhhbXBsZS5nbG93Q29sb3J9YH0+XG4gICAgICAgICAgICAgICAgICAgIHtSZWFjdC5jcmVhdGVFbGVtZW50KGN1cnJlbnRFeGFtcGxlLmljb24sIHtcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU6IGBoLTYgdy02ICR7Y3VycmVudEV4YW1wbGUuY29sb3J9IG14LWF1dG8gbWItMmBcbiAgICAgICAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNDAwIG1iLTFcIj5DbGFzc2lmaWVkIGFzPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgZm9udC1zZW1pYm9sZCB0ZXh0LXNtICR7Y3VycmVudEV4YW1wbGUuY29sb3J9YH0+XG4gICAgICAgICAgICAgICAgICAgICAge2N1cnJlbnRFeGFtcGxlLnJvbGVOYW1lfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgIFJvbGUgSUQ6IHtjdXJyZW50RXhhbXBsZS5yb2xlfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICB7LyogUHJvY2Vzc2luZyBJbmRpY2F0b3JzICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIGdhcC0yIHRleHQteHNcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1zbGF0ZS03MDAgcm91bmRlZCBwLTIgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNDAwIG1iLTFcIj7inJMgQ29udGV4dCBBbmFseXNpczwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMFwiPkNvbXBsZXRlPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXNsYXRlLTcwMCByb3VuZGVkIHAtMiB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmVlbi00MDAgbWItMVwiPuKckyBSb2xlIE1hdGNoaW5nPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwXCI+Q29tcGxldGU8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIHsvKiBDb25uZWN0aW9uIGRvdHMgd2l0aCBhbmltYXRlZCBnbG93ICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgbGVmdC0wIHRvcC0xLzIgdHJhbnNmb3JtIC10cmFuc2xhdGUteC0xLzIgLXRyYW5zbGF0ZS15LTEvMlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMSBoLTQgYmctWyNmZjZiMzVdIHJvdW5kZWQtZnVsbCBhbmltYXRlLXB1bHNlIHNoYWRvdy1sZyBzaGFkb3ctb3JhbmdlLTUwMC81MFwiIHN0eWxlPXt7YW5pbWF0aW9uRGVsYXk6ICcwLjZzJ319PjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMSBoLTYgYmctWyNmZjZiMzVdIHJvdW5kZWQtZnVsbCBhbmltYXRlLXB1bHNlIHNoYWRvdy1sZyBzaGFkb3ctb3JhbmdlLTUwMC81MFwiIHN0eWxlPXt7YW5pbWF0aW9uRGVsYXk6ICcwLjRzJ319PjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMSBoLTggYmctWyNmZjZiMzVdIHJvdW5kZWQtZnVsbCBhbmltYXRlLXB1bHNlIHNoYWRvdy1sZyBzaGFkb3ctb3JhbmdlLTUwMC81MFwiIHN0eWxlPXt7YW5pbWF0aW9uRGVsYXk6ICcwLjJzJ319PjwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSByaWdodC0wIHRvcC0xLzIgdHJhbnNmb3JtIHRyYW5zbGF0ZS14LTEvMiAtdHJhbnNsYXRlLXktMS8yXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0xXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xIGgtOCBiZy1bI2ZmNmIzNV0gcm91bmRlZC1mdWxsIGFuaW1hdGUtcHVsc2Ugc2hhZG93LWxnIHNoYWRvdy1vcmFuZ2UtNTAwLzUwXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xIGgtNiBiZy1bI2ZmNmIzNV0gcm91bmRlZC1mdWxsIGFuaW1hdGUtcHVsc2Ugc2hhZG93LWxnIHNoYWRvdy1vcmFuZ2UtNTAwLzUwXCIgc3R5bGU9e3thbmltYXRpb25EZWxheTogJzAuMnMnfX0+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xIGgtNCBiZy1bI2ZmNmIzNV0gcm91bmRlZC1mdWxsIGFuaW1hdGUtcHVsc2Ugc2hhZG93LWxnIHNoYWRvdy1vcmFuZ2UtNTAwLzUwXCIgc3R5bGU9e3thbmltYXRpb25EZWxheTogJzAuNHMnfX0+PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIEFuaW1hdGVkIGNvbm5lY3Rpb24gbGluZXMgd2l0aCBmbG93aW5nIGN1cnJlbnQgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgbGVmdC0wIHRvcC0xLzIgdHJhbnNmb3JtIC10cmFuc2xhdGUteC1mdWxsIC10cmFuc2xhdGUteS0xLzIgaGlkZGVuIGxnOmJsb2NrXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB3LTE2IGgtcHhcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ncmFkaWVudC10by1yIGZyb20tdHJhbnNwYXJlbnQgdmlhLVsjZmY2YjM1XSB0by10cmFuc3BhcmVudCBvcGFjaXR5LTYwXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0wIHJpZ2h0LTAgdy00IGgtcHggYmctZ3JhZGllbnQtdG8tbCBmcm9tLXdoaXRlIHRvLVsjZmY2YjM1XSBmbG93LWxlZnQgc2hhZG93LWxnIHNoYWRvdy1vcmFuZ2UtNTAwLzUwXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0wIHJpZ2h0LTAgdy0yIGgtcHggYmctd2hpdGUgZmxvdy1sZWZ0IHNoYWRvdy1zbSBzaGFkb3ctd2hpdGUvNTBcIiBzdHlsZT17e2FuaW1hdGlvbkRlbGF5OiAnMC4zcyd9fT48L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgcmlnaHQtMCB0b3AtMS8yIHRyYW5zZm9ybSB0cmFuc2xhdGUteC1mdWxsIC10cmFuc2xhdGUteS0xLzIgaGlkZGVuIGxnOmJsb2NrXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB3LTE2IGgtcHhcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ncmFkaWVudC10by1yIGZyb20tWyNmZjZiMzVdIHRvLXRyYW5zcGFyZW50IG9wYWNpdHktNjBcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTAgbGVmdC0wIHctNCBoLXB4IGJnLWdyYWRpZW50LXRvLXIgZnJvbS13aGl0ZSB0by1bI2ZmNmIzNV0gZmxvdy1yaWdodCBzaGFkb3ctbGcgc2hhZG93LW9yYW5nZS01MDAvNTBcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTAgbGVmdC0wIHctMiBoLXB4IGJnLXdoaXRlIGZsb3ctcmlnaHQgc2hhZG93LXNtIHNoYWRvdy13aGl0ZS81MFwiIHN0eWxlPXt7YW5pbWF0aW9uRGVsYXk6ICcwLjdzJ319PjwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cblxuICAgICAgICAgICAgey8qIENvbHVtbiAzOiBPcHRpbWFsIE1vZGVsIFNlbGVjdGlvbiAqL31cbiAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgIGtleT17YG1vZGVsLSR7YWN0aXZlRXhhbXBsZX1gfVxuICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHg6IDUwIH19XG4gICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeDogMCB9fVxuICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjYsIGRlbGF5OiAwLjYgfX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicmVsYXRpdmVcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTZcIj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtd2hpdGUgbWItMlwiPk9wdGltYWwgTW9kZWwgU2VsZWN0aW9uPC9oMz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS00MDBcIj5Sb3V0ZWQgdG8gdGhlIHBlcmZlY3QgbW9kZWw8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1zbGF0ZS04MDAgdG8tc2xhdGUtOTAwIGJvcmRlci0yIGJvcmRlci1ncmVlbi01MDAvMzAgcm91bmRlZC14bCBwLTYgcmVsYXRpdmUgc2hhZG93LXhsIHNoYWRvdy1ncmVlbi01MDAvMjBcIj5cbiAgICAgICAgICAgICAgICB7LyogU3VjY2VzcyBnbG93IGJvcmRlciAqL31cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgcm91bmRlZC14bCBib3JkZXItMiBib3JkZXItZGFzaGVkIGJvcmRlci1ncmVlbi00MDAgb3BhY2l0eS01MCBhbmltYXRlLXB1bHNlXCI+PC9kaXY+XG5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHotMTBcIj5cbiAgICAgICAgICAgICAgICAgIHsvKiBTZWxlY3RlZCBNb2RlbCAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWItNlwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTYgaC0xNiBiZy1ncmFkaWVudC10by1iciBmcm9tLWdyZWVuLTUwMCB0by1lbWVyYWxkLTYwMCByb3VuZGVkLTJ4bCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBteC1hdXRvIG1iLTMgc2hhZG93LWxnIHNoYWRvdy1ncmVlbi01MDAvMzBcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8Q3B1Q2hpcEljb24gY2xhc3NOYW1lPVwidy04IGgtOCB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi0xXCI+e2N1cnJlbnRFeGFtcGxlLm1vZGVsfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmVlbi00MDAgZm9udC1tZWRpdW1cIj57Y3VycmVudEV4YW1wbGUucHJvdmlkZXJ9PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIFJvbGUgQXNzaWdubWVudCAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgJHtjdXJyZW50RXhhbXBsZS5iZ0NvbG9yfSAke2N1cnJlbnRFeGFtcGxlLmJvcmRlckNvbG9yfSBib3JkZXItMiByb3VuZGVkLWxnIHAtNCB0ZXh0LWNlbnRlciBtYi00IHNoYWRvdy1sZyAke2N1cnJlbnRFeGFtcGxlLmdsb3dDb2xvcn1gfT5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDAgbWItMVwiPkFzc2lnbmVkIFJvbGU8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2Bmb250LXNlbWlib2xkIHRleHQtc20gJHtjdXJyZW50RXhhbXBsZS5jb2xvcn1gfT5cbiAgICAgICAgICAgICAgICAgICAgICB7Y3VycmVudEV4YW1wbGUucm9sZU5hbWV9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIHsvKiBQZXJmb3JtYW5jZSBNZXRyaWNzICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIGdhcC0yIHRleHQteHNcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1zbGF0ZS03MDAgcm91bmRlZCBwLTIgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNDAwIG1iLTFcIj7imqEgU3BlZWQ8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1tZWRpdW1cIj5PcHRpbWFsPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXNsYXRlLTcwMCByb3VuZGVkIHAtMiB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTQwMCBtYi0xXCI+8J+OryBBY2N1cmFjeTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LW1lZGl1bVwiPkhpZ2g8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIFN1Y2Nlc3MgaW5kaWNhdG9yICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC00IGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHRleHQtZ3JlZW4tNDAwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxDaGVja0ljb24gY2xhc3NOYW1lPVwidy00IGgtNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bVwiPlJvdXRlIEVzdGFibGlzaGVkPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7LyogQ29ubmVjdGlvbiBkb3RzIHdpdGggc3VjY2VzcyBnbG93ICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgbGVmdC0wIHRvcC0xLzIgdHJhbnNmb3JtIC10cmFuc2xhdGUteC0xLzIgLXRyYW5zbGF0ZS15LTEvMlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMSBoLTQgYmctZ3JlZW4tNDAwIHJvdW5kZWQtZnVsbCBhbmltYXRlLXB1bHNlIHNoYWRvdy1sZyBzaGFkb3ctZ3JlZW4tNTAwLzUwXCIgc3R5bGU9e3thbmltYXRpb25EZWxheTogJzAuOHMnfX0+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xIGgtNiBiZy1ncmVlbi00MDAgcm91bmRlZC1mdWxsIGFuaW1hdGUtcHVsc2Ugc2hhZG93LWxnIHNoYWRvdy1ncmVlbi01MDAvNTBcIiBzdHlsZT17e2FuaW1hdGlvbkRlbGF5OiAnMC42cyd9fT48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEgaC04IGJnLWdyZWVuLTQwMCByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1wdWxzZSBzaGFkb3ctbGcgc2hhZG93LWdyZWVuLTUwMC81MFwiIHN0eWxlPXt7YW5pbWF0aW9uRGVsYXk6ICcwLjRzJ319PjwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBGaW5hbCBjb25uZWN0aW9uIGxpbmUgd2l0aCBzdWNjZXNzIGZsb3cgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgbGVmdC0wIHRvcC0xLzIgdHJhbnNmb3JtIC10cmFuc2xhdGUteC1mdWxsIC10cmFuc2xhdGUteS0xLzIgaGlkZGVuIGxnOmJsb2NrXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB3LTE2IGgtcHhcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ncmFkaWVudC10by1yIGZyb20tdHJhbnNwYXJlbnQgdmlhLWdyZWVuLTQwMCB0by1ncmVlbi00MDAgb3BhY2l0eS02MFwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMCByaWdodC0wIHctNCBoLXB4IGJnLWdyYWRpZW50LXRvLWwgZnJvbS13aGl0ZSB0by1ncmVlbi00MDAgZmxvdy1sZWZ0IHNoYWRvdy1sZyBzaGFkb3ctZ3JlZW4tNTAwLzUwXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0wIHJpZ2h0LTAgdy0yIGgtcHggYmctd2hpdGUgZmxvdy1sZWZ0IHNoYWRvdy1zbSBzaGFkb3ctd2hpdGUvNTBcIiBzdHlsZT17e2FuaW1hdGlvbkRlbGF5OiAnMXMnfX0+PC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIEV4YW1wbGUgU2VsZWN0b3IgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktY2VudGVyIG10LTEyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAge3JvdXRpbmdFeGFtcGxlcy5tYXAoKGV4YW1wbGUsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBrZXk9e2V4YW1wbGUuaWR9XG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0QWN0aXZlRXhhbXBsZShpbmRleCl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy0zIGgtMyByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwICR7XG4gICAgICAgICAgICAgICAgICBpbmRleCA9PT0gYWN0aXZlRXhhbXBsZVxuICAgICAgICAgICAgICAgICAgICA/ICdiZy1bI2ZmNmIzNV0gc2NhbGUtMTI1J1xuICAgICAgICAgICAgICAgICAgICA6ICdiZy1ncmF5LTUwMCBob3ZlcjpiZy1ncmF5LTQwMCdcbiAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogUm91S2V5J3MgSW50ZWxsaWdlbnQgUm91dGluZyBCZW5lZml0cyAqL31cbiAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgd2hpbGVJblZpZXc9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgIHZpZXdwb3J0PXt7IG9uY2U6IHRydWUgfX1cbiAgICAgICAgICBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy00IGdhcC04IG10LTIwXCJcbiAgICAgICAgPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMiBoLTEyIGJnLWJsdWUtNTAwLzIwIHJvdW5kZWQtbGcgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbXgtYXV0byBtYi00IHNoYWRvdy1sZyBzaGFkb3ctYmx1ZS01MDAvMjBcIj5cbiAgICAgICAgICAgICAgPEJvbHRJY29uIGNsYXNzTmFtZT1cImgtNiB3LTYgdGV4dC1ibHVlLTQwMFwiIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBtYi0yXCI+Um9sZS1CYXNlZCBDbGFzc2lmaWNhdGlvbjwvaDM+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwXCI+R2VtaW5pIDIuMCBGbGFzaCBhbmFseXplcyBjb250ZXh0IGFuZCBjbGFzc2lmaWVzIHJlcXVlc3RzIGludG8gMTUrIHNwZWNpYWxpemVkIHJvbGVzPC9wPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgYmctZ3JlZW4tNTAwLzIwIHJvdW5kZWQtbGcgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbXgtYXV0byBtYi00IHNoYWRvdy1sZyBzaGFkb3ctZ3JlZW4tNTAwLzIwXCI+XG4gICAgICAgICAgICAgIDxDcHVDaGlwSWNvbiBjbGFzc05hbWU9XCJoLTYgdy02IHRleHQtZ3JlZW4tNDAwXCIgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIG1iLTJcIj5TbWFydCBNb2RlbCBNYXRjaGluZzwvaDM+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwXCI+RWFjaCByb2xlIHJvdXRlcyB0byB5b3VyIHByZS1jb25maWd1cmVkIG9wdGltYWwgbW9kZWwgZm9yIG1heGltdW0gcGVyZm9ybWFuY2U8L3A+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTIgaC0xMiBiZy1wdXJwbGUtNTAwLzIwIHJvdW5kZWQtbGcgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbXgtYXV0byBtYi00IHNoYWRvdy1sZyBzaGFkb3ctcHVycGxlLTUwMC8yMFwiPlxuICAgICAgICAgICAgICA8Q2hhcnRCYXJJY29uIGNsYXNzTmFtZT1cImgtNiB3LTYgdGV4dC1wdXJwbGUtNDAwXCIgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIG1iLTJcIj5Db250ZXh0dWFsIENvbnRpbnVpdHk8L2gzPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMFwiPk1haW50YWlucyBjb252ZXJzYXRpb24gY29udGV4dCBhbmQgcm9sZSBjb25zaXN0ZW5jeSBhY3Jvc3MgbXVsdGktdHVybiBpbnRlcmFjdGlvbnM8L3A+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTIgaC0xMiBiZy1vcmFuZ2UtNTAwLzIwIHJvdW5kZWQtbGcgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbXgtYXV0byBtYi00IHNoYWRvdy1sZyBzaGFkb3ctb3JhbmdlLTUwMC8yMFwiPlxuICAgICAgICAgICAgICA8U2hpZWxkQ2hlY2tJY29uIGNsYXNzTmFtZT1cImgtNiB3LTYgdGV4dC1vcmFuZ2UtNDAwXCIgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIG1iLTJcIj5GYWxsYmFjayBQcm90ZWN0aW9uPC9oMz5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDBcIj5BdXRvbWF0aWMgZmFsbGJhY2sgdG8gZGVmYXVsdCBnZW5lcmFsIGNoYXQgbW9kZWwgd2hlbiBubyByb2xlIG1hdGNoIGlzIGZvdW5kPC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgey8qIFJvbGUgRXhhbXBsZXMgKi99XG4gICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgICAgIHdoaWxlSW5WaWV3PXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICB2aWV3cG9ydD17eyBvbmNlOiB0cnVlIH19XG4gICAgICAgICAgdHJhbnNpdGlvbj17eyBkZWxheTogMC4yIH19XG4gICAgICAgICAgY2xhc3NOYW1lPVwibXQtMTYgdGV4dC1jZW50ZXJcIlxuICAgICAgICA+XG4gICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIG1iLThcIj5TdXBwb3J0ZWQgUm9sZSBUeXBlczwvaDQ+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIG1kOmdyaWQtY29scy00IGxnOmdyaWQtY29scy02IGdhcC00IG1heC13LTR4bCBteC1hdXRvXCI+XG4gICAgICAgICAgICB7W1xuICAgICAgICAgICAgICB7IG5hbWU6ICdHZW5lcmFsIENoYXQnLCBjb2xvcjogJ3RleHQtZ3JheS00MDAnIH0sXG4gICAgICAgICAgICAgIHsgbmFtZTogJ0xvZ2ljICYgUmVhc29uaW5nJywgY29sb3I6ICd0ZXh0LWJsdWUtNDAwJyB9LFxuICAgICAgICAgICAgICB7IG5hbWU6ICdXcml0aW5nICYgQ29udGVudCcsIGNvbG9yOiAndGV4dC1wdXJwbGUtNDAwJyB9LFxuICAgICAgICAgICAgICB7IG5hbWU6ICdGcm9udGVuZCBDb2RpbmcnLCBjb2xvcjogJ3RleHQtZ3JlZW4tNDAwJyB9LFxuICAgICAgICAgICAgICB7IG5hbWU6ICdCYWNrZW5kIENvZGluZycsIGNvbG9yOiAndGV4dC1lbWVyYWxkLTQwMCcgfSxcbiAgICAgICAgICAgICAgeyBuYW1lOiAnUmVzZWFyY2ggJiBBbmFseXNpcycsIGNvbG9yOiAndGV4dC1jeWFuLTQwMCcgfSxcbiAgICAgICAgICAgICAgeyBuYW1lOiAnU3VtbWFyaXphdGlvbicsIGNvbG9yOiAndGV4dC15ZWxsb3ctNDAwJyB9LFxuICAgICAgICAgICAgICB7IG5hbWU6ICdUcmFuc2xhdGlvbicsIGNvbG9yOiAndGV4dC1waW5rLTQwMCcgfSxcbiAgICAgICAgICAgICAgeyBuYW1lOiAnRGF0YSBFeHRyYWN0aW9uJywgY29sb3I6ICd0ZXh0LWluZGlnby00MDAnIH0sXG4gICAgICAgICAgICAgIHsgbmFtZTogJ0JyYWluc3Rvcm1pbmcnLCBjb2xvcjogJ3RleHQtb3JhbmdlLTQwMCcgfSxcbiAgICAgICAgICAgICAgeyBuYW1lOiAnRWR1Y2F0aW9uJywgY29sb3I6ICd0ZXh0LXJlZC00MDAnIH0sXG4gICAgICAgICAgICAgIHsgbmFtZTogJ0F1ZGlvIFRyYW5zY3JpcHRpb24nLCBjb2xvcjogJ3RleHQtdGVhbC00MDAnIH1cbiAgICAgICAgICAgIF0ubWFwKChyb2xlLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICA8ZGl2IGtleT17cm9sZS5uYW1lfSBjbGFzc05hbWU9XCJiZy1zbGF0ZS04MDAgcm91bmRlZC1sZyBwLTMgYm9yZGVyIGJvcmRlci1zbGF0ZS03MDBcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHRleHQtc20gZm9udC1tZWRpdW0gJHtyb2xlLmNvbG9yfWB9Pntyb2xlLm5hbWV9PC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKSl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvc2VjdGlvbj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIm1vdGlvbiIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiQm9sdEljb24iLCJDb2RlQnJhY2tldEljb24iLCJQZW5jaWxJY29uIiwiQ2hhcnRCYXJJY29uIiwiQ3B1Q2hpcEljb24iLCJTaGllbGRDaGVja0ljb24iLCJDaGVja0ljb24iLCJyb3V0aW5nRXhhbXBsZXMiLCJpZCIsInByb21wdCIsInJvbGUiLCJyb2xlTmFtZSIsIm1vZGVsIiwicHJvdmlkZXIiLCJpY29uIiwiY29sb3IiLCJiZ0NvbG9yIiwiYm9yZGVyQ29sb3IiLCJnbG93Q29sb3IiLCJSb3V0aW5nVmlzdWFsaXphdGlvbiIsImFjdGl2ZUV4YW1wbGUiLCJzZXRBY3RpdmVFeGFtcGxlIiwiaXNBbmltYXRpbmciLCJzZXRJc0FuaW1hdGluZyIsImludGVydmFsIiwic2V0SW50ZXJ2YWwiLCJzZXRUaW1lb3V0IiwicHJldiIsImxlbmd0aCIsImNsZWFySW50ZXJ2YWwiLCJjdXJyZW50RXhhbXBsZSIsInNlY3Rpb24iLCJkaXYiLCJoMiIsImluaXRpYWwiLCJvcGFjaXR5IiwieSIsIndoaWxlSW5WaWV3Iiwidmlld3BvcnQiLCJvbmNlIiwiY2xhc3NOYW1lIiwicCIsInRyYW5zaXRpb24iLCJkZWxheSIsIngiLCJhbmltYXRlIiwiZHVyYXRpb24iLCJoMyIsInN0eWxlIiwiYW5pbWF0aW9uRGVsYXkiLCJzY2FsZSIsImNyZWF0ZUVsZW1lbnQiLCJzcGFuIiwibWFwIiwiZXhhbXBsZSIsImluZGV4IiwiYnV0dG9uIiwib25DbGljayIsImg0IiwibmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/RoutingVisualization.tsx\n"));

/***/ })

});