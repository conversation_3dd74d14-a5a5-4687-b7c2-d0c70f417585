import { type NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';

// POST /api/custom-configs
// Creates a new custom API configuration
export async function POST(request: NextRequest) {
  const supabase = await createSupabaseServerClientOnRequest();
  // TODO: Add user authentication check in Milestone 13
  // const { data: { user } } = await supabase.auth.getUser();
  // if (!user) {
  //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  // }

  try {
    const { name } = await request.json();

    if (!name || typeof name !== 'string' || name.trim().length === 0) {
      return NextResponse.json({ error: 'Configuration name is required and must be a non-empty string' }, { status: 400 });
    }
    if (name.length > 255) {
      return NextResponse.json({ error: 'Configuration name must be 255 characters or less' }, { status: 400 });
    }

    const placeholderUserId = '00000000-0000-0000-0000-000000000000'; // Placeholder for testing

    const { data, error } = await supabase
      .from('custom_api_configs')
      .insert([
        // { name, user_id: user.id }, // Uncomment and use user_id in Milestone 13
        { name, user_id: placeholderUserId }, // Use placeholder user_id for now
      ])
      .select()
      .single();

    if (error) {
      console.error('Supabase error creating custom config:', error);
      return NextResponse.json({ error: 'Failed to create custom API configuration', details: error.message }, { status: 500 });
    }

    return NextResponse.json(data, { status: 201 });
  } catch (e: any) {
    console.error('Error in POST /api/custom-configs:', e);
    if (e.name === 'SyntaxError') { // Likely JSON parsing error
        return NextResponse.json({ error: 'Invalid request body: Malformed JSON.' }, { status: 400 });
    }
    return NextResponse.json({ error: 'An unexpected error occurred', details: e.message }, { status: 500 });
  }
}

// GET /api/custom-configs
// Lists all custom API configurations (for the authenticated user in M13)
export async function GET(request: NextRequest) {
  const supabase = await createSupabaseServerClientOnRequest();
  // TODO: Add user authentication check in Milestone 13
  // const { data: { user } } = await supabase.auth.getUser();
  // if (!user) {
  //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  // }

  try {
    // Phase 2A Optimization: Optimized query with only needed fields and caching
    const { data, error } = await supabase
      .from('custom_api_configs')
      .select('id, name, created_at, updated_at, routing_strategy') // Only select needed fields
      // .eq('user_id', user.id) // Uncomment and use user_id in Milestone 13
      .order('created_at', { ascending: false })
      .limit(100); // Reasonable limit to prevent large responses

    if (error) {
      console.error('Supabase error fetching custom configs:', error);
      return NextResponse.json({ error: 'Failed to fetch custom API configurations', details: error.message }, { status: 500 });
    }

    const response = NextResponse.json(data || [], { status: 200 });

    // Phase 2A Optimization: Add aggressive caching headers
    const isPrefetch = request.headers.get('X-Prefetch') === 'true';
    const cacheMaxAge = isPrefetch ? 600 : 120; // 10 minutes for prefetch, 2 minutes for regular

    response.headers.set('Cache-Control', `private, max-age=${cacheMaxAge}, stale-while-revalidate=300`);
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('Vary', 'X-Prefetch');

    return response;
  } catch (e: any) {
    console.error('Error in GET /api/custom-configs:', e);
    return NextResponse.json({ error: 'An unexpected error occurred', details: e.message }, { status: 500 });
  }
} 