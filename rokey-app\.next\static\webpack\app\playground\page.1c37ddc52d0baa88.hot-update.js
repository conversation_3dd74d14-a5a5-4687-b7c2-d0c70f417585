"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/OrchestrationCanvas.tsx":
/*!************************************************!*\
  !*** ./src/components/OrchestrationCanvas.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OrchestrationCanvas: function() { return /* binding */ OrchestrationCanvas; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useOrchestrationStream__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useOrchestrationStream */ \"(app-pages-browser)/./src/hooks/useOrchestrationStream.ts\");\n/* harmony import */ var _OrchestrationChatroom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./OrchestrationChatroom */ \"(app-pages-browser)/./src/components/OrchestrationChatroom.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,MinusIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,MinusIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,MinusIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MinusIcon.js\");\n/* __next_internal_client_entry_do_not_use__ OrchestrationCanvas auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst OrchestrationCanvas = (param)=>{\n    let { executionId, onComplete, onError, onCanvasStateChange, forceMaximize = false } = param;\n    _s();\n    const [isCanvasOpen, setIsCanvasOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isMinimized, setIsMinimized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [orchestrationComplete, setOrchestrationComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [finalResult, setFinalResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const { events, isConnected, error } = (0,_hooks_useOrchestrationStream__WEBPACK_IMPORTED_MODULE_2__.useOrchestrationStream)(executionId);\n    // Handle orchestration completion\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const synthesisCompleteEvent = events.find((event)=>event.type === \"synthesis_complete\");\n        if (synthesisCompleteEvent && !orchestrationComplete) {\n            var _synthesisCompleteEvent_data;\n            setOrchestrationComplete(true);\n            const result = ((_synthesisCompleteEvent_data = synthesisCompleteEvent.data) === null || _synthesisCompleteEvent_data === void 0 ? void 0 : _synthesisCompleteEvent_data.result) || \"Orchestration completed successfully\";\n            setFinalResult(result);\n            // Notify parent component\n            if (onComplete) {\n                onComplete(result);\n            }\n        }\n    }, [\n        events,\n        orchestrationComplete,\n        onComplete\n    ]);\n    // Handle errors\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (error && onError) {\n            onError(error);\n        }\n    }, [\n        error,\n        onError\n    ]);\n    const handleMinimize = ()=>{\n        setIsMinimized(true);\n        // Keep isCanvasOpen as true so component doesn't disappear completely\n        // We'll hide it via CSS transform instead\n        onCanvasStateChange === null || onCanvasStateChange === void 0 ? void 0 : onCanvasStateChange(false, true);\n    };\n    const handleMaximize = ()=>{\n        setIsMinimized(false);\n        // isCanvasOpen should already be true, but ensure it\n        setIsCanvasOpen(true);\n        onCanvasStateChange === null || onCanvasStateChange === void 0 ? void 0 : onCanvasStateChange(true, false);\n    };\n    // Notify parent of initial canvas state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        onCanvasStateChange === null || onCanvasStateChange === void 0 ? void 0 : onCanvasStateChange(isCanvasOpen, isMinimized);\n    }, [\n        isCanvasOpen,\n        isMinimized,\n        onCanvasStateChange\n    ]);\n    // Handle external maximize trigger\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (forceMaximize && isMinimized) {\n            console.log(\"\\uD83C\\uDFAD [DEBUG] External maximize trigger received!\");\n            handleMaximize();\n        }\n    }, [\n        forceMaximize,\n        isMinimized\n    ]);\n    // Minimized card state - now returns null, will be rendered inline in chat\n    if (isMinimized) {\n        return null;\n    }\n    // Canvas is closed\n    if (!isCanvasOpen) {\n        return null;\n    }\n    // Debug log when rendering\n    console.log(\"\\uD83C\\uDFAD [DEBUG] OrchestrationCanvas is rendering!\", {\n        isCanvasOpen,\n        isMinimized,\n        executionId,\n        shouldBeVisible: isCanvasOpen && !isMinimized,\n        transformClass: isCanvasOpen && !isMinimized ? \"translate-x-0\" : \"translate-x-full\"\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed top-0 right-0 h-full w-1/2 shadow-2xl z-[9999] transform transition-transform duration-300 ease-out border-l border-gray-200 \".concat(isCanvasOpen && !isMinimized ? \"translate-x-0\" : \"translate-x-full\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-gradient-to-br from-slate-50 via-white to-blue-50/30\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 opacity-[0.02]\",\n                            style: {\n                                backgroundImage: \"\\n              radial-gradient(circle at 25% 25%, #3b82f6 2px, transparent 2px),\\n              radial-gradient(circle at 75% 75%, #8b5cf6 2px, transparent 2px)\\n            \",\n                                backgroundSize: \"60px 60px\",\n                                backgroundPosition: \"0 0, 30px 30px\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 opacity-[0.015]\",\n                            style: {\n                                backgroundImage: \"url(\\\"data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E\\\")\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative flex items-center justify-between p-4 border-b border-gray-200/60 bg-gradient-to-r from-blue-50/80 to-purple-50/80 backdrop-blur-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center shadow-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-5 h-5 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"font-semibold text-gray-900\",\n                                            children: \"AI Team Collaboration\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-600\",\n                                            children: orchestrationComplete ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-3 h-3 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Orchestration Complete\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 19\n                                            }, undefined) : \"Multi-Role Orchestration in Progress\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleMinimize,\n                                className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-white/60 rounded-lg transition-all duration-200 backdrop-blur-sm\",\n                                \"aria-label\": \"Minimize canvas\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative flex-1 h-full overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_OrchestrationChatroom__WEBPACK_IMPORTED_MODULE_3__.OrchestrationChatroom, {\n                        executionId: executionId,\n                        events: events,\n                        isConnected: isConnected,\n                        error: error,\n                        isComplete: orchestrationComplete\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n            lineNumber: 107,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(OrchestrationCanvas, \"KsTXJg8wJUpwnTOsKVHiLc1u04Q=\", false, function() {\n    return [\n        _hooks_useOrchestrationStream__WEBPACK_IMPORTED_MODULE_2__.useOrchestrationStream\n    ];\n});\n_c = OrchestrationCanvas;\nvar _c;\n$RefreshReg$(_c, \"OrchestrationCanvas\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OrchestrationCanvas.tsx\n"));

/***/ })

});