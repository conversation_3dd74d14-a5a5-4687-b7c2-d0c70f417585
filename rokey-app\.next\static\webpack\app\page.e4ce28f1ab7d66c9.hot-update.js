"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/FeaturesSection.tsx":
/*!****************************************************!*\
  !*** ./src/components/landing/FeaturesSection.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeaturesSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst features = [\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: \"Intelligent Role Routing\",\n        description: \"AI automatically classifies your prompts and routes them to the best model for each specific task - writing, coding, logic, or analysis.\",\n        color: \"text-blue-600\",\n        bgColor: \"bg-blue-50\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"Automatic Failover\",\n        description: \"Never worry about API limits or downtime. RouKey automatically retries failed requests with alternative models in your configuration.\",\n        color: \"text-green-600\",\n        bgColor: \"bg-green-50\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Enterprise Security\",\n        description: \"Military-grade AES-256-GCM encryption for all API keys. Your credentials are stored securely and never exposed.\",\n        color: \"text-purple-600\",\n        bgColor: \"bg-purple-50\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Comprehensive Analytics\",\n        description: \"Track costs, performance, token usage, and success rates across all your models with real-time dashboards and insights.\",\n        color: \"text-orange-600\",\n        bgColor: \"bg-orange-50\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"Performance Optimization\",\n        description: \"First-token tracking, latency monitoring, and intelligent caching ensure blazing-fast response times under 500ms.\",\n        color: \"text-indigo-600\",\n        bgColor: \"bg-indigo-50\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"Cost Optimization\",\n        description: \"Automatic free-tier detection, budget alerts, and cost tracking help you optimize spending across all AI providers.\",\n        color: \"text-emerald-600\",\n        bgColor: \"bg-emerald-50\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        title: \"300+ AI Models\",\n        description: \"Access the latest models from OpenAI, Google, Anthropic, DeepSeek, xAI, Meta, and hundreds more through one unified API.\",\n        color: \"text-red-600\",\n        bgColor: \"bg-red-50\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        title: \"Advanced Routing Strategies\",\n        description: \"Load balancing, complexity-based routing, strict fallback sequences, and custom rules for enterprise-grade control.\",\n        color: \"text-cyan-600\",\n        bgColor: \"bg-cyan-50\"\n    }\n];\nfunction FeaturesSection() {\n    _s();\n    const [isHovered, setIsHovered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"features\",\n        className: \"py-24 bg-gradient-to-br from-gray-900 to-black relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-10\",\n                        style: {\n                            backgroundImage: \"\\n              linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),\\n              linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)\\n            \",\n                            backgroundSize: '80px 80px'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 right-0 w-96 h-96 bg-[#ff6b35]/5 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 left-0 w-96 h-96 bg-[#f7931e]/5 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-left mb-20 max-w-4xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.h2, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"text-5xl sm:text-6xl font-bold text-white mb-6 leading-tight\",\n                                children: [\n                                    \"Enterprise-Grade\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]\",\n                                        children: [\n                                            ' ',\n                                            \"AI Infrastructure\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.p, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    delay: 0.2\n                                },\n                                className: \"text-xl text-gray-300 max-w-3xl leading-relaxed\",\n                                children: \"RouKey provides military-grade security, intelligent routing, and comprehensive analytics for the most demanding AI workloads. Built for scale, designed for performance.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-6xl mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 50\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"relative h-[700px] flex items-center justify-center\",\n                            style: {\n                                perspective: '1000px'\n                            },\n                            onMouseEnter: ()=>setIsHovered(true),\n                            onMouseLeave: ()=>setIsHovered(false),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-gradient-to-r from-[#ff6b35]/10 via-transparent to-[#f7931e]/10 rounded-3xl blur-3xl transition-all duration-700 \".concat(isHovered ? 'from-[#ff6b35]/20 to-[#f7931e]/20' : '')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, this),\n                                features.map((feature, index)=>{\n                                    // Calculate positions for stacked and spread states\n                                    const getTransform = (spread)=>{\n                                        if (spread) {\n                                            const angle = (index - 3.5) * 18; // Spread in a fan\n                                            const radius = 320;\n                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 120;\n                                            return {\n                                                x,\n                                                y,\n                                                rotateY: angle,\n                                                rotateX: Math.abs(angle) * 0.2,\n                                                scale: 1\n                                            };\n                                        } else {\n                                            return {\n                                                x: index * 8,\n                                                y: index * 4,\n                                                rotateY: index * 2,\n                                                rotateX: 0,\n                                                scale: 1\n                                            };\n                                        }\n                                    };\n                                    const transform = getTransform(isHovered);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                        className: \"absolute w-80 h-96 cursor-pointer\",\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.8\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: transform.x,\n                                            y: transform.y,\n                                            rotateY: transform.rotateY,\n                                            rotateX: transform.rotateX,\n                                            scale: transform.scale\n                                        },\n                                        whileInView: {\n                                            opacity: 1\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        transition: {\n                                            delay: isHovered ? 0 : index * 0.1,\n                                            duration: isHovered ? 0.8 : 0.6,\n                                            type: \"spring\",\n                                            stiffness: isHovered ? 80 : 100,\n                                            damping: isHovered ? 20 : 15\n                                        },\n                                        style: {\n                                            zIndex: isHovered ? index === Math.floor(features.length / 2) ? 50 : features.length - Math.abs(index - Math.floor(features.length / 2)) : features.length - index,\n                                            transformStyle: 'preserve-3d'\n                                        },\n                                        whileHover: {\n                                            scale: 1.15,\n                                            rotateY: 0,\n                                            rotateX: 0,\n                                            x: 0,\n                                            y: -20,\n                                            zIndex: 100,\n                                            transition: {\n                                                duration: 0.4,\n                                                type: \"spring\",\n                                                stiffness: 200,\n                                                damping: 20\n                                            }\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-full bg-gradient-to-br from-gray-800/90 to-gray-900/90 backdrop-blur-xl rounded-3xl border border-gray-700 hover:border-[#ff6b35]/50 transition-all duration-500 shadow-2xl hover:shadow-orange-500/20 relative overflow-hidden group/card\",\n                                            style: {\n                                                transform: 'translateZ(0)',\n                                                backfaceVisibility: 'hidden'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 opacity-5\",\n                                                    style: {\n                                                        backgroundImage: \"\\n                          linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),\\n                          linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)\\n                        \",\n                                                        backgroundSize: '20px 20px'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10 p-8 h-full flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-20 h-20 \".concat(index % 3 === 0 ? 'bg-gradient-to-br from-[#ff6b35] to-[#f7931e]' : index % 3 === 1 ? 'bg-gradient-to-br from-gray-600 to-gray-700' : 'bg-gradient-to-br from-[#f7931e] to-[#ff6b35]', \" rounded-2xl flex items-center justify-center mb-6 group-hover/card:scale-110 transition-transform duration-300 shadow-lg \").concat(index % 3 === 0 || index % 3 === 2 ? 'shadow-orange-500/30' : 'shadow-gray-500/30'),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                                className: \"h-10 w-10 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                lineNumber: 230,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                            lineNumber: 229,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-2xl font-bold text-white mb-4 group-hover/card:text-[#ff6b35] transition-colors duration-300\",\n                                                            children: feature.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-300 leading-relaxed flex-1 text-lg\",\n                                                            children: feature.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-6 h-1 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-full transform scale-x-0 group-hover/card:scale-x-100 transition-transform duration-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 rounded-3xl bg-gradient-to-r from-[#ff6b35]/10 to-[#f7931e]/10 opacity-0 group-hover/card:opacity-100 transition-opacity duration-500 pointer-events-none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 rounded-3xl bg-gradient-to-r from-transparent via-white/5 to-transparent transform -skew-x-12 -translate-x-full group-hover/card:translate-x-full transition-transform duration-1000 pointer-events-none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, feature.title, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 17\n                                    }, this);\n                                }),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        delay: 1.5\n                                    },\n                                    className: \"absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-20 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-800/90 backdrop-blur-sm rounded-full px-8 py-4 border border-gray-700 shadow-xl\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.p, {\n                                            className: \"text-gray-300 text-sm font-medium\",\n                                            animate: {\n                                                color: isHovered ? '#ff6b35' : '#d1d5db'\n                                            },\n                                            transition: {\n                                                duration: 0.3\n                                            },\n                                            children: isHovered ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-[#ff6b35]\",\n                                                        children: \"✨ Exploring\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \" our features\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-[#ff6b35]\",\n                                                        children: \"Hover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \" to spread the cards\"\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 50\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        className: \"mt-32 flex justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl w-full bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-3xl p-16 text-white relative overflow-hidden shadow-2xl shadow-orange-500/25\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 opacity-10\",\n                                    style: {\n                                        backgroundImage: \"\\n                  linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),\\n                  linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px)\\n                \",\n                                        backgroundSize: '30px 30px'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-10 left-10 w-32 h-32 bg-white/5 rounded-full blur-2xl animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-10 right-10 w-40 h-40 bg-white/5 rounded-full blur-3xl animate-pulse\",\n                                            style: {\n                                                animationDelay: '1s'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-1/2 left-1/4 w-20 h-20 bg-white/10 rounded-full blur-xl animate-pulse\",\n                                            style: {\n                                                animationDelay: '2s'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative z-10 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.h3, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                delay: 0.2\n                                            },\n                                            className: \"text-5xl sm:text-6xl font-bold mb-8 leading-tight\",\n                                            children: [\n                                                \"Ready to Transform\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white/90\",\n                                                    children: \"Your AI Infrastructure?\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.p, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                delay: 0.4\n                                            },\n                                            className: \"text-2xl opacity-90 mb-12 leading-relaxed max-w-3xl mx-auto\",\n                                            children: \"Join the next generation of developers building with RouKey's intelligent AI gateway. Start routing smarter today.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                delay: 0.6\n                                            },\n                                            className: \"flex flex-col sm:flex-row gap-6 justify-center items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.button, {\n                                                    whileHover: {\n                                                        scale: 1.05,\n                                                        boxShadow: \"0 25px 50px rgba(0,0,0,0.4)\",\n                                                        y: -2\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.95\n                                                    },\n                                                    className: \"bg-white text-[#ff6b35] px-12 py-5 rounded-2xl font-bold text-xl hover:bg-gray-50 transition-all duration-300 shadow-2xl min-w-[200px]\",\n                                                    children: \"Start Building Now\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.button, {\n                                                    whileHover: {\n                                                        scale: 1.05,\n                                                        backgroundColor: \"rgba(255, 255, 255, 0.1)\",\n                                                        y: -2\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.95\n                                                    },\n                                                    className: \"border-2 border-white text-white px-12 py-5 rounded-2xl font-bold text-xl hover:bg-white hover:text-[#ff6b35] transition-all duration-300 min-w-[200px]\",\n                                                    children: \"View Documentation\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                delay: 0.8\n                                            },\n                                            className: \"mt-16 grid grid-cols-1 sm:grid-cols-3 gap-8 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/10 backdrop-blur-sm rounded-2xl p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-3xl font-bold mb-2\",\n                                                            children: \"300+\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-white/80\",\n                                                            children: \"AI Models\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                            lineNumber: 381,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/10 backdrop-blur-sm rounded-2xl p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-3xl font-bold mb-2\",\n                                                            children: \"99.9%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                            lineNumber: 384,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-white/80\",\n                                                            children: \"Uptime\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/10 backdrop-blur-sm rounded-2xl p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-3xl font-bold mb-2\",\n                                                            children: \"<500ms\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-white/80\",\n                                                            children: \"Response Time\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, this);\n}\n_s(FeaturesSection, \"FPQn8a98tPjpohC7NUYORQR8GJE=\");\n_c = FeaturesSection;\nvar _c;\n$RefreshReg$(_c, \"FeaturesSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/FeaturesSection.tsx\n"));

/***/ })

});