@import "tailwindcss";

/* R<PERSON><PERSON><PERSON> Dark Theme - Portkey Inspired */
:root {
  /* Background Colors - Dark Theme */
  --background-primary: #0a0a0a;
  --background-secondary: #151515;
  --background-tertiary: #1f1f1f;
  --background-card: #1a1a1a;
  --background-hover: #2a2a2a;

  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #9ca3af;
  --text-muted: #6b7280;
  --text-accent: #00d4aa;

  /* Brand Colors */
  --primary-teal: #14b8a6;
  --primary-cyan: #00d4aa;
  --accent-orange: #f59e0b;
  --accent-purple: #8b5cf6;
  --accent-red: #ef4444;
  --accent-green: #22c55e;

  /* Border Colors */
  --border-primary: #374151;
  --border-secondary: #2a2a2a;
  --border-accent: #00d4aa;

  /* Shadow System */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.4), 0 1px 2px -1px rgb(0 0 0 / 0.4);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.4), 0 4px 6px -4px rgb(0 0 0 / 0.4);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.4), 0 8px 10px -6px rgb(0 0 0 / 0.4);
  --shadow-glow: 0 0 20px rgba(0, 212, 170, 0.3);
  --shadow-glow-lg: 0 0 40px rgba(0, 212, 170, 0.4);
}

/* Base Styles - Dark Theme Default */
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  background-color: var(--background-primary);
  color: var(--text-primary);
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  font-feature-settings: 'cv11', 'ss01';
  font-variation-settings: 'opsz' 32;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--background-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-primary);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* Selection Styling */
::selection {
  background-color: var(--primary-cyan);
  color: var(--background-primary);
}

/* Component Styles */
.card {
  background-color: var(--background-card);
  border: 1px solid var(--border-secondary);
  border-radius: 12px;
  box-shadow: var(--shadow);
  transition: all 0.2s ease;
}

.card:hover {
  border-color: var(--border-primary);
  box-shadow: var(--shadow-md);
}

.btn-primary {
  background-color: var(--primary-cyan);
  color: var(--background-primary);
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
}

.btn-primary:hover {
  background-color: var(--primary-teal);
  box-shadow: var(--shadow-glow);
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: var(--background-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
}

.btn-secondary:hover {
  background-color: var(--background-hover);
  border-color: var(--border-accent);
}

/* Input Styles */
.input {
  background-color: var(--background-tertiary);
  border: 1px solid var(--border-secondary);
  border-radius: 8px;
  padding: 0.75rem 1rem;
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.input:focus {
  outline: none;
  border-color: var(--border-accent);
  box-shadow: 0 0 0 3px rgba(0, 212, 170, 0.1);
}

.input::placeholder {
  color: var(--text-muted);
}

/* Focus styles */
*:focus-visible {
  outline: 2px solid var(--primary-cyan);
  outline-offset: 2px;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes glow {
  0%, 100% { box-shadow: var(--shadow-glow); }
  50% { box-shadow: var(--shadow-glow-lg); }
}

/* Utility Classes */
.animate-fade-in { animation: fadeIn 0.3s ease-out; }
.animate-slide-in { animation: slideIn 0.3s ease-out; }
.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
.animate-spin { animation: spin 1s linear infinite; }
.animate-glow { animation: glow 2s ease-in-out infinite; }

/* Text Utilities */
.text-gradient {
  background: linear-gradient(135deg, var(--primary-cyan), var(--primary-teal));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glass Effect */
.glass {
  background: rgba(26, 26, 26, 0.8);
  backdrop-filter: blur(12px);
  border: 1px solid var(--border-secondary);
}

/* Page Transitions */
.page-transition {
  transition: opacity 0.2s ease-out, transform 0.2s ease-out;
}

/* Hover Effects */
.hover-lift {
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Interactive Elements */
.interactive {
  cursor: pointer;
  transition: all 0.2s ease;
}

.interactive:hover {
  transform: translateY(-1px);
}

.interactive:active {
  transform: scale(0.98);
}
