'use client';

import React, { useState, useEffect, useRef } from 'react';
import { ChatMessage } from './ChatMessage';
import { TypingIndicator } from './TypingIndicator';
import { SparklesIcon } from '@heroicons/react/24/outline';

interface OrchestrationEvent {
  type: string;
  role_id?: string;
  model_name?: string;
  data?: any;
  timestamp?: string;
}

interface OrchestrationChatroomProps {
  executionId: string;
  events: OrchestrationEvent[];
  isConnected: boolean;
  error: string | null;
  isComplete: boolean;
}

interface ChatMessageData {
  id: string;
  sender: 'moderator' | 'specialist';
  senderName: string;
  roleId?: string;
  content: string;
  timestamp: Date;
  type: 'message' | 'assignment' | 'handoff' | 'clarification' | 'completion';
}

export const OrchestrationChatroom: React.FC<OrchestrationChatroomProps> = ({
  executionId,
  events,
  isConnected,
  error,
  isComplete
}) => {
  const [chatMessages, setChatMessages] = useState<ChatMessageData[]>([]);
  const [typingSpecialists, setTypingSpecialists] = useState<Set<string>>(new Set());
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [chatMessages]);

  // Convert orchestration events to chat messages
  useEffect(() => {
    const newMessages: ChatMessageData[] = [];
    const currentlyTyping = new Set<string>();

    events.forEach((event, index) => {
      const timestamp = new Date(event.timestamp || Date.now());
      const messageId = `${executionId}-${index}`;

      switch (event.type) {
        case 'orchestration_started':
          newMessages.push({
            id: messageId,
            sender: 'moderator',
            senderName: 'Moderator',
            content: "🎬 Welcome to the AI Team Collaboration! I'm assembling the perfect team for this task.",
            timestamp,
            type: 'message'
          });
          break;

        case 'task_decomposed':
          const steps = event.data?.steps || [];
          const teamIntro = steps.map((step: any) => 
            `🤖 @${step.roleId} - ${step.modelName || 'AI Specialist'}`
          ).join('\n');
          
          newMessages.push({
            id: messageId,
            sender: 'moderator',
            senderName: 'Moderator',
            content: `📋 I've analyzed the task and assembled this expert team:\n\n${teamIntro}\n\nLet's begin the collaboration!`,
            timestamp,
            type: 'assignment'
          });
          break;

        case 'step_assigned':
          newMessages.push({
            id: messageId,
            sender: 'moderator',
            senderName: 'Moderator',
            roleId: event.role_id,
            content: `🎯 @${event.role_id}, you're up! ${event.data?.commentary || 'Please begin your specialized work on this task.'}`,
            timestamp,
            type: 'assignment'
          });
          break;

        case 'moderator_assignment':
          newMessages.push({
            id: messageId,
            sender: 'moderator',
            senderName: 'Moderator',
            roleId: event.role_id,
            content: event.data?.message || `🎯 @${event.role_id}, you're up! Please begin your specialized work on this task.`,
            timestamp,
            type: 'assignment'
          });
          break;

        case 'specialist_acknowledgment':
          newMessages.push({
            id: messageId,
            sender: 'specialist',
            senderName: event.role_id || 'Specialist',
            roleId: event.role_id,
            content: event.data?.message || `✅ Understood! I'm ${event.role_id} and I'll handle this task with expertise. Starting work now...`,
            timestamp,
            type: 'message'
          });
          break;

        case 'step_started':
          // Add to typing indicators
          if (event.role_id) {
            currentlyTyping.add(event.role_id);
          }
          break;

        case 'step_progress':
          if (event.role_id) {
            currentlyTyping.add(event.role_id);
          }
          break;

        case 'specialist_message':
          newMessages.push({
            id: messageId,
            sender: 'specialist',
            senderName: event.role_id || 'Specialist',
            roleId: event.role_id,
            content: `${event.data?.message || '🎉 Perfect! I\'ve completed my part of the task. Here\'s what I\'ve delivered:'}\n\n${event.data?.output || 'Task completed successfully!'}`,
            timestamp,
            type: 'completion'
          });
          break;

        case 'step_completed':
          // Remove from typing
          if (event.role_id) {
            currentlyTyping.delete(event.role_id);
          }
          break;

        case 'handoff_message':
          newMessages.push({
            id: messageId,
            sender: 'moderator',
            senderName: 'Moderator',
            content: event.data?.message || `✨ Excellent work, @${event.data?.fromRole}! Quality looks great. Now passing to @${event.data?.toRole}...`,
            timestamp,
            type: 'handoff'
          });
          break;

        case 'synthesis_started':
          newMessages.push({
            id: messageId,
            sender: 'moderator',
            senderName: 'Moderator',
            content: event.data?.message || `🧩 Fantastic teamwork everyone! Now I'll synthesize all your excellent contributions into the final deliverable...`,
            timestamp,
            type: 'message'
          });
          currentlyTyping.add('moderator');
          break;

        case 'synthesis_complete':
          currentlyTyping.delete('moderator');
          newMessages.push({
            id: messageId,
            sender: 'moderator',
            senderName: 'Moderator',
            content: event.data?.message || `🎊 Mission accomplished! The team has delivered an outstanding result. Great collaboration everyone!`,
            timestamp,
            type: 'completion'
          });
          break;
      }
    });

    setChatMessages(newMessages);
    setTypingSpecialists(currentlyTyping);
  }, [events, executionId]);

  if (error) {
    return (
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="text-center">
          <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Connection Error</h3>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Connection Status */}
      <div className={`px-4 py-2 text-xs font-medium backdrop-blur-sm ${
        isConnected
          ? 'bg-green-50/80 text-green-700 border-b border-green-100/60'
          : 'bg-amber-50/80 text-amber-700 border-b border-amber-100/60'
      }`}>
        <div className="flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full ${
            isConnected ? 'bg-green-500 shadow-sm' : 'bg-amber-500 animate-pulse'
          }`} />
          <span>{isConnected ? 'Connected to AI Team' : 'Connecting...'}</span>
          {isComplete && (
            <div className="ml-auto flex items-center space-x-1">
              <SparklesIcon className="w-3 h-3 text-green-500" />
              <span className="text-green-600 font-medium">Complete</span>
            </div>
          )}
        </div>
      </div>

      {/* Chat Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {chatMessages.length === 0 && (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-4 shadow-sm">
              <svg className="w-8 h-8 text-blue-500 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </div>
            <p className="text-gray-600 font-medium">Waiting for AI team to start collaboration...</p>
            <p className="text-gray-400 text-sm mt-1">The specialists are preparing to work together</p>
          </div>
        )}

        {/* Enhanced Message Display with Phase Separators */}
        {chatMessages.map((message, index) => {
          const isPhaseStart = message.type === 'assignment' ||
                             (message.type === 'message' && message.content.includes('🎬'));
          const isPhaseEnd = message.type === 'completion';

          return (
            <div key={message.id}>
              {/* Phase Separator */}
              {isPhaseStart && index > 0 && (
                <div className="flex items-center my-6">
                  <div className="flex-1 h-px bg-gradient-to-r from-transparent via-gray-300/60 to-transparent" />
                  <div className="px-3 py-1 text-xs text-gray-500 bg-white/80 backdrop-blur-sm rounded-full border border-gray-200/60 shadow-sm">
                    New Phase
                  </div>
                  <div className="flex-1 h-px bg-gradient-to-r from-transparent via-gray-300/60 to-transparent" />
                </div>
              )}

              <ChatMessage
                key={message.id}
                message={message}
              />

              {/* Completion Celebration */}
              {isPhaseEnd && (
                <div className="flex justify-center my-4">
                  <div className="bg-gradient-to-r from-green-50/80 to-emerald-50/80 border border-green-200/60 rounded-full px-4 py-2 shadow-sm backdrop-blur-sm">
                    <span className="text-sm text-green-700 font-medium flex items-center space-x-2">
                      <SparklesIcon className="w-4 h-4" />
                      <span>Phase Complete</span>
                    </span>
                  </div>
                </div>
              )}
            </div>
          );
        })}

        {/* Enhanced Typing Indicators */}
        {Array.from(typingSpecialists).map((specialist) => (
          <div key={specialist} className="relative">
            <TypingIndicator
              senderName={specialist}
              roleId={specialist !== 'moderator' ? specialist : undefined}
            />
          </div>
        ))}

        <div ref={messagesEndRef} />
      </div>

      {/* Enhanced Completion Footer */}
      {isComplete && (
        <div className="p-4 bg-gradient-to-r from-green-50/80 to-emerald-50/80 border-t border-green-200/60 backdrop-blur-sm">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center shadow-sm">
                <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <div>
                <p className="text-sm text-green-800 font-medium">AI Team Collaboration Complete!</p>
                <p className="text-xs text-green-600">All specialists have finished their work</p>
              </div>
            </div>

            {/* Export Button */}
            <button className="text-xs text-green-700 hover:text-green-800 bg-white/60 hover:bg-white/80 px-3 py-1.5 rounded-full border border-green-200/60 transition-all duration-200 shadow-sm">
              Export Results
            </button>
          </div>
        </div>
      )}
    </div>
  );
};
