"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/OrchestrationChatroom.tsx":
/*!**************************************************!*\
  !*** ./src/components/OrchestrationChatroom.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OrchestrationChatroom: function() { return /* binding */ OrchestrationChatroom; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ChatMessage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChatMessage */ \"(app-pages-browser)/./src/components/ChatMessage.tsx\");\n/* harmony import */ var _TypingIndicator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TypingIndicator */ \"(app-pages-browser)/./src/components/TypingIndicator.tsx\");\n/* harmony import */ var _barrel_optimize_names_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* __next_internal_client_entry_do_not_use__ OrchestrationChatroom auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst OrchestrationChatroom = (param)=>{\n    let { executionId, events, isConnected, error, isComplete } = param;\n    _s();\n    const [chatMessages, setChatMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [typingSpecialists, setTypingSpecialists] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Scroll to bottom when new messages arrive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    }, [\n        chatMessages\n    ]);\n    // Convert orchestration events to chat messages\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const newMessages = [];\n        const currentlyTyping = new Set();\n        events.forEach((event, index)=>{\n            const timestamp = new Date(event.timestamp || Date.now());\n            const messageId = \"\".concat(executionId, \"-\").concat(index);\n            switch(event.type){\n                case \"orchestration_started\":\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        content: \"\\uD83C\\uDFAC Welcome to the AI Team Collaboration! I'm assembling the perfect team for this task.\",\n                        timestamp,\n                        type: \"message\"\n                    });\n                    break;\n                case \"task_decomposed\":\n                    var _event_data;\n                    const steps = ((_event_data = event.data) === null || _event_data === void 0 ? void 0 : _event_data.steps) || [];\n                    const teamIntro = steps.map((step)=>\"\\uD83E\\uDD16 @\".concat(step.roleId, \" - \").concat(step.modelName || \"AI Specialist\")).join(\"\\n\");\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        content: \"\\uD83D\\uDCCB I've analyzed the task and assembled this expert team:\\n\\n\".concat(teamIntro, \"\\n\\nLet's begin the collaboration!\"),\n                        timestamp,\n                        type: \"assignment\"\n                    });\n                    break;\n                case \"step_assigned\":\n                    var _event_data1;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        roleId: event.role_id,\n                        content: \"\\uD83C\\uDFAF @\".concat(event.role_id, \", you're up! \").concat(((_event_data1 = event.data) === null || _event_data1 === void 0 ? void 0 : _event_data1.commentary) || \"Please begin your specialized work on this task.\"),\n                        timestamp,\n                        type: \"assignment\"\n                    });\n                    break;\n                case \"moderator_assignment\":\n                    var _event_data2;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        roleId: event.role_id,\n                        content: ((_event_data2 = event.data) === null || _event_data2 === void 0 ? void 0 : _event_data2.message) || \"\\uD83C\\uDFAF @\".concat(event.role_id, \", you're up! Please begin your specialized work on this task.\"),\n                        timestamp,\n                        type: \"assignment\"\n                    });\n                    break;\n                case \"specialist_acknowledgment\":\n                    var _event_data3;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"specialist\",\n                        senderName: event.role_id || \"Specialist\",\n                        roleId: event.role_id,\n                        content: ((_event_data3 = event.data) === null || _event_data3 === void 0 ? void 0 : _event_data3.message) || \"✅ Understood! I'm \".concat(event.role_id, \" and I'll handle this task with expertise. Starting work now...\"),\n                        timestamp,\n                        type: \"message\"\n                    });\n                    break;\n                case \"step_started\":\n                    // Add to typing indicators\n                    if (event.role_id) {\n                        currentlyTyping.add(event.role_id);\n                    }\n                    break;\n                case \"step_progress\":\n                    if (event.role_id) {\n                        currentlyTyping.add(event.role_id);\n                    }\n                    break;\n                case \"specialist_message\":\n                    var _event_data4, _event_data5;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"specialist\",\n                        senderName: event.role_id || \"Specialist\",\n                        roleId: event.role_id,\n                        content: \"\".concat(((_event_data4 = event.data) === null || _event_data4 === void 0 ? void 0 : _event_data4.message) || \"\\uD83C\\uDF89 Perfect! I've completed my part of the task. Here's what I've delivered:\", \"\\n\\n\").concat(((_event_data5 = event.data) === null || _event_data5 === void 0 ? void 0 : _event_data5.output) || \"Task completed successfully!\"),\n                        timestamp,\n                        type: \"completion\"\n                    });\n                    break;\n                case \"step_completed\":\n                    // Remove from typing\n                    if (event.role_id) {\n                        currentlyTyping.delete(event.role_id);\n                    }\n                    break;\n                case \"handoff_message\":\n                    var _event_data6, _event_data7, _event_data8;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        content: ((_event_data6 = event.data) === null || _event_data6 === void 0 ? void 0 : _event_data6.message) || \"✨ Excellent work, @\".concat((_event_data7 = event.data) === null || _event_data7 === void 0 ? void 0 : _event_data7.fromRole, \"! Quality looks great. Now passing to @\").concat((_event_data8 = event.data) === null || _event_data8 === void 0 ? void 0 : _event_data8.toRole, \"...\"),\n                        timestamp,\n                        type: \"handoff\"\n                    });\n                    break;\n                case \"synthesis_started\":\n                    var _event_data9;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        content: ((_event_data9 = event.data) === null || _event_data9 === void 0 ? void 0 : _event_data9.message) || \"\\uD83E\\uDDE9 Fantastic teamwork everyone! Now I'll synthesize all your excellent contributions into the final deliverable...\",\n                        timestamp,\n                        type: \"message\"\n                    });\n                    currentlyTyping.add(\"moderator\");\n                    break;\n                case \"synthesis_complete\":\n                    var _event_data10;\n                    currentlyTyping.delete(\"moderator\");\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        content: ((_event_data10 = event.data) === null || _event_data10 === void 0 ? void 0 : _event_data10.message) || \"\\uD83C\\uDF8A Mission accomplished! The team has delivered an outstanding result. Great collaboration everyone!\",\n                        timestamp,\n                        type: \"completion\"\n                    });\n                    break;\n            }\n        });\n        setChatMessages(newMessages);\n        setTypingSpecialists(currentlyTyping);\n    }, [\n        events,\n        executionId\n    ]);\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 flex items-center justify-center p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-6 h-6 text-red-600\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                        children: \"Connection Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                lineNumber: 199,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n            lineNumber: 198,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-2 text-xs font-medium backdrop-blur-sm \".concat(isConnected ? \"bg-green-50/80 text-green-700 border-b border-green-100/60\" : \"bg-amber-50/80 text-amber-700 border-b border-amber-100/60\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2 h-2 rounded-full \".concat(isConnected ? \"bg-green-500 shadow-sm\" : \"bg-amber-500 animate-pulse\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: isConnected ? \"Connected to AI Team\" : \"Connecting...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 11\n                        }, undefined),\n                        isComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-auto flex items-center space-x-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-3 h-3 text-green-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-green-600 font-medium\",\n                                    children: \"Complete\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                    lineNumber: 220,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                lineNumber: 215,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-4 space-y-4\",\n                children: [\n                    chatMessages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-4 shadow-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-8 h-8 text-blue-500 animate-pulse\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 font-medium\",\n                                children: \"Waiting for AI team to start collaboration...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm mt-1\",\n                                children: \"The specialists are preparing to work together\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 11\n                    }, undefined),\n                    chatMessages.map((message, index)=>{\n                        const isPhaseStart = message.type === \"assignment\" || message.type === \"message\" && message.content.includes(\"\\uD83C\\uDFAC\");\n                        const isPhaseEnd = message.type === \"completion\";\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                isPhaseStart && index > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center my-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 h-px bg-gradient-to-r from-transparent via-gray-300/60 to-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-3 py-1 text-xs text-gray-500 bg-white/80 backdrop-blur-sm rounded-full border border-gray-200/60 shadow-sm\",\n                                            children: \"New Phase\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 h-px bg-gradient-to-r from-transparent via-gray-300/60 to-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatMessage__WEBPACK_IMPORTED_MODULE_2__.ChatMessage, {\n                                    message: message\n                                }, message.id, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 15\n                                }, undefined),\n                                isPhaseEnd && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center my-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-green-50/80 to-emerald-50/80 border border-green-200/60 rounded-full px-4 py-2 shadow-sm backdrop-blur-sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-green-700 font-medium flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Phase Complete\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, message.id, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 13\n                        }, undefined);\n                    }),\n                    Array.from(typingSpecialists).map((specialist)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TypingIndicator__WEBPACK_IMPORTED_MODULE_3__.TypingIndicator, {\n                                senderName: specialist,\n                                roleId: specialist !== \"moderator\" ? specialist : undefined\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 13\n                            }, undefined)\n                        }, specialist, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, undefined)),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                lineNumber: 235,\n                columnNumber: 7\n            }, undefined),\n            isComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-gradient-to-r from-green-50/80 to-emerald-50/80 border-t border-green-200/60 backdrop-blur-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6 h-6 bg-green-500 rounded-full flex items-center justify-center shadow-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 text-white\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M5 13l4 4L19 7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-green-800 font-medium\",\n                                            children: \"AI Team Collaboration Complete!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-green-600\",\n                                            children: \"All specialists have finished their work\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"text-xs text-green-700 hover:text-green-800 bg-white/60 hover:bg-white/80 px-3 py-1.5 rounded-full border border-green-200/60 transition-all duration-200 shadow-sm\",\n                            children: \"Export Results\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                    lineNumber: 303,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                lineNumber: 302,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n        lineNumber: 213,\n        columnNumber: 5\n    }, undefined);\n};\n_s(OrchestrationChatroom, \"Trlsk+ImATjahibYSefjX0C5OX4=\");\n_c = OrchestrationChatroom;\nvar _c;\n$RefreshReg$(_c, \"OrchestrationChatroom\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OrchestrationChatroom.tsx\n"));

/***/ })

});