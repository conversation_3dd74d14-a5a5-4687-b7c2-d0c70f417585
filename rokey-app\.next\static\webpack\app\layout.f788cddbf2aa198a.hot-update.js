"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"5cb26d6fb53c\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MTAxNiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjVjYjI2ZDZmYjUzY1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/design-system.css":
/*!**************************************!*\
  !*** ./src/styles/design-system.css ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"15c66de1a2be\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZGVzaWduLXN5c3RlbS5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9zdHlsZXMvZGVzaWduLXN5c3RlbS5jc3M/ZWE5YyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjE1YzY2ZGUxYTJiZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/design-system.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Navbar.tsx":
/*!***********************************!*\
  !*** ./src/components/Navbar.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Navbar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_Cog6ToothIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,Cog6ToothIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_Cog6ToothIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,Cog6ToothIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_Cog6ToothIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,Cog6ToothIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/SidebarContext */ \"(app-pages-browser)/./src/contexts/SidebarContext.tsx\");\n/* harmony import */ var _hooks_useBreadcrumb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useBreadcrumb */ \"(app-pages-browser)/./src/hooks/useBreadcrumb.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction Navbar() {\n    _s();\n    const { toggleSidebar } = (0,_contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_1__.useSidebar)();\n    const { breadcrumb } = (0,_hooks_useBreadcrumb__WEBPACK_IMPORTED_MODULE_2__.useBreadcrumb)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"header border-b border-gray-200 bg-white/95 backdrop-blur-sm\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center h-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: toggleSidebar,\n                                className: \"lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200\",\n                                title: \"Toggle sidebar\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_Cog6ToothIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-6 w-6 text-gray-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 18,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-bold text-gray-900\",\n                                    children: \"RoKey\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 28,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden lg:flex items-center space-x-2 text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: breadcrumb.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 33,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"/\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-900 font-medium\",\n                                        children: breadcrumb.subtitle\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 sm:space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden xl:block\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Search...\",\n                                            className: \"w-64 pl-10 pr-4 py-2.5 text-sm bg-white border border-gray-200 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500/20 focus:border-orange-500 transition-all duration-200\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"h-4 w-4 text-gray-400\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 51,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 50,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_Cog6ToothIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-5 w-5 text-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"absolute -top-1 -right-1 h-3 w-3 bg-orange-500 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"hidden sm:block p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_Cog6ToothIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-5 w-5 text-gray-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 px-2 sm:px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 rounded-full bg-gradient-to-br from-orange-400 to-orange-500 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-semibold text-sm\",\n                                            children: \"DU\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden md:block\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-900\",\n                                                children: \"Demo User\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"Free Plan\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n                lineNumber: 14,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Navbar.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n_s(Navbar, \"VKpgnywD07cyVRcRv5cyVi8UFyM=\", false, function() {\n    return [\n        _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_1__.useSidebar,\n        _hooks_useBreadcrumb__WEBPACK_IMPORTED_MODULE_2__.useBreadcrumb\n    ];\n});\n_c = Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Navbar.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/Sidebar.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Sidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/KeyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BeakerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/AcademicCapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/KeyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/BeakerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/MapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/AcademicCapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/ChartBarIcon.js\");\n/* harmony import */ var _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/SidebarContext */ \"(app-pages-browser)/./src/contexts/SidebarContext.tsx\");\n/* harmony import */ var _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/NavigationContext */ \"(app-pages-browser)/./src/contexts/NavigationContext.tsx\");\n/* harmony import */ var _hooks_useRoutePrefetch__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useRoutePrefetch */ \"(app-pages-browser)/./src/hooks/useRoutePrefetch.ts\");\n/* harmony import */ var _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useChatHistory */ \"(app-pages-browser)/./src/hooks/useChatHistory.ts\");\n/* harmony import */ var _hooks_usePredictiveNavigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/usePredictiveNavigation */ \"(app-pages-browser)/./src/hooks/usePredictiveNavigation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst navItems = [\n    {\n        href: \"/dashboard\",\n        label: \"Dashboard\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        description: \"Overview & analytics\"\n    },\n    {\n        href: \"/my-models\",\n        label: \"My Models\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        description: \"API key management\"\n    },\n    {\n        href: \"/playground\",\n        label: \"Playground\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        description: \"Test your models\"\n    },\n    {\n        href: \"/routing-setup\",\n        label: \"Routing Setup\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        description: \"Configure routing\"\n    },\n    {\n        href: \"/logs\",\n        label: \"Logs\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n        description: \"Request history\"\n    },\n    {\n        href: \"/training\",\n        label: \"Prompt Engineering\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n        description: \"Custom prompts\"\n    },\n    {\n        href: \"/analytics\",\n        label: \"Analytics\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n        description: \"Advanced insights\"\n    }\n];\nfunction Sidebar() {\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { isCollapsed, isHovered, isHoverDisabled, setHovered } = (0,_contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_4__.useSidebar)();\n    const { navigateOptimistically } = (0,_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_5__.useNavigation)();\n    const { prefetchOnHover } = (0,_hooks_useRoutePrefetch__WEBPACK_IMPORTED_MODULE_6__.useRoutePrefetch)();\n    const { prefetchWhenIdle } = (0,_hooks_useRoutePrefetch__WEBPACK_IMPORTED_MODULE_6__.useIntelligentPrefetch)();\n    const { prefetchChatHistory } = (0,_hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_7__.useChatHistoryPrefetch)();\n    const { predictions, isLearning } = (0,_hooks_usePredictiveNavigation__WEBPACK_IMPORTED_MODULE_8__.usePredictiveNavigation)();\n    const contextualSuggestions = (0,_hooks_usePredictiveNavigation__WEBPACK_IMPORTED_MODULE_8__.useContextualSuggestions)();\n    // Enhanced prefetching with predictive navigation\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        const allRoutes = navItems.map((item)=>item.href);\n        // Combine predictive routes with standard prefetching\n        const predictiveRoutes = predictions.slice(0, 2); // Top 2 predictions\n        const contextualRoutes = contextualSuggestions.filter((s)=>s.priority === \"high\").map((s)=>s.route).slice(0, 2);\n        const routesToPrefetch = [\n            ...predictiveRoutes,\n            ...contextualRoutes,\n            ...allRoutes.filter((route)=>route !== pathname && !predictiveRoutes.includes(route) && !contextualRoutes.includes(route)),\n            \"/playground\",\n            \"/logs\"\n        ].slice(0, 6); // Increased limit for better coverage\n        console.log(\"\\uD83E\\uDDE0 [PREDICTIVE] Prefetching routes:\", {\n            predictive: predictiveRoutes,\n            contextual: contextualRoutes,\n            total: routesToPrefetch,\n            isLearning\n        });\n        const cleanup = prefetchWhenIdle(routesToPrefetch);\n        return cleanup;\n    }, [\n        pathname,\n        prefetchWhenIdle,\n        predictions,\n        contextualSuggestions,\n        isLearning\n    ]);\n    // Determine if sidebar should be expanded (hover or not collapsed)\n    const isExpanded = !isCollapsed || isHovered;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"sidebar flex flex-col h-screen flex-shrink-0 transition-all duration-200 ease-out z-50 \".concat(isExpanded ? \"w-64\" : \"w-16\"),\n        onMouseEnter: ()=>!isHoverDisabled && setHovered(true),\n        onMouseLeave: ()=>!isHoverDisabled && setHovered(false),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 relative overflow-hidden\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 transition-all duration-200 ease-out \".concat(isExpanded ? \"px-6\" : \"px-3\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 pt-4 transition-all duration-200 ease-out \".concat(isExpanded ? \"\" : \"text-center\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"transition-all duration-200 ease-out \".concat(isExpanded ? \"opacity-0 scale-75 -translate-y-2\" : \"opacity-100 scale-100 translate-y-0\", \" \").concat(isExpanded ? \"absolute\" : \"relative\", \" w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center mx-auto\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-bold text-sm\",\n                                        children: \"R\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"transition-all duration-200 ease-out \".concat(isExpanded ? \"opacity-100 scale-100 translate-y-0\" : \"opacity-0 scale-75 translate-y-2\", \" \").concat(isExpanded ? \"relative\" : \"absolute top-0 left-0 w-full\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-bold text-white tracking-tight whitespace-nowrap\",\n                                            children: \"RoKey\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-400 mt-1 whitespace-nowrap\",\n                                            children: \"Smart LLM Router\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"space-y-2\",\n                        children: navItems.map((item)=>{\n                            const isActive = pathname === item.href || pathname.startsWith(item.href + \"/\");\n                            const Icon = isActive ? item.iconSolid : item.icon;\n                            const isPredicted = predictions.includes(item.href);\n                            const contextualSuggestion = contextualSuggestions.find((s)=>s.route === item.href);\n                            // Enhanced prefetch for playground to include chat history\n                            const handlePlaygroundHover = ()=>{\n                                if (item.href === \"/playground\") {\n                                    // Prefetch route\n                                    prefetchOnHover(item.href, 50).onMouseEnter();\n                                    // Also prefetch chat history for current config if available\n                                    const currentConfigId = new URLSearchParams(window.location.search).get(\"config\");\n                                    if (currentConfigId) {\n                                        prefetchChatHistory(currentConfigId);\n                                    }\n                                }\n                            };\n                            const hoverProps = item.href === \"/playground\" ? {\n                                onMouseEnter: handlePlaygroundHover\n                            } : prefetchOnHover(item.href, 50);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: item.href,\n                                onClick: (e)=>{\n                                    e.preventDefault();\n                                    navigateOptimistically(item.href);\n                                },\n                                className: \"sidebar-nav-item group flex items-center transition-all duration-200 ease-out w-full text-left \".concat(isActive ? \"active\" : \"\", \" \").concat(isExpanded ? \"\" : \"collapsed\"),\n                                title: isExpanded ? undefined : item.label,\n                                ...hoverProps,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex items-center w-full overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative flex items-center justify-center transition-all duration-200 ease-out \".concat(isExpanded ? \"w-5 h-5 mr-3\" : \"w-10 h-10 rounded-xl\", \" \").concat(!isExpanded && isActive ? \"bg-white shadow-sm\" : !isExpanded ? \"bg-transparent hover:bg-white/10\" : \"\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"transition-all duration-200 ease-out \".concat(isExpanded ? \"h-5 w-5\" : \"h-5 w-5\", \" \").concat(isActive ? \"text-orange-500\" : \"text-white\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 23\n                                                }, this),\n                                                isPredicted && !isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute rounded-full bg-blue-400 animate-pulse transition-all duration-200 ease-out \".concat(isExpanded ? \"-top-1 -right-1 w-2 h-2\" : \"-top-1 -right-1 w-3 h-3\"),\n                                                    title: \"Predicted next destination\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 transition-all duration-200 ease-out \".concat(isExpanded ? \"opacity-100 translate-x-0 max-w-full\" : \"opacity-0 translate-x-4 max-w-0\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between whitespace-nowrap\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium text-sm\",\n                                                            children: item.label\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 228,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        contextualSuggestion && !isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs px-1.5 py-0.5 rounded-full ml-2 \".concat(contextualSuggestion.priority === \"high\" ? \"bg-blue-500/20 text-blue-300\" : \"bg-gray-500/20 text-gray-300\"),\n                                                            children: contextualSuggestion.priority === \"high\" ? \"!\" : \"\\xb7\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs transition-colors duration-200 whitespace-nowrap \".concat(isActive ? \"text-orange-400\" : \"text-gray-400\"),\n                                                    children: contextualSuggestion ? contextualSuggestion.reason : item.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 19\n                                }, this)\n                            }, item.href, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 139,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n            lineNumber: 137,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n        lineNumber: 130,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"eS+ydXAo96PN1Qszj9rKXwHTkQk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_4__.useSidebar,\n        _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_5__.useNavigation,\n        _hooks_useRoutePrefetch__WEBPACK_IMPORTED_MODULE_6__.useRoutePrefetch,\n        _hooks_useRoutePrefetch__WEBPACK_IMPORTED_MODULE_6__.useIntelligentPrefetch,\n        _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_7__.useChatHistoryPrefetch,\n        _hooks_usePredictiveNavigation__WEBPACK_IMPORTED_MODULE_8__.usePredictiveNavigation,\n        _hooks_usePredictiveNavigation__WEBPACK_IMPORTED_MODULE_8__.useContextualSuggestions\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sidebar.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useChatHistory.ts":
/*!*************************************!*\
  !*** ./src/hooks/useChatHistory.ts ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   chatHistoryCache: function() { return /* binding */ chatHistoryCache; },\n/* harmony export */   useChatHistory: function() { return /* binding */ useChatHistory; },\n/* harmony export */   useChatHistoryPrefetch: function() { return /* binding */ useChatHistoryPrefetch; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useChatHistory,useChatHistoryPrefetch,chatHistoryCache auto */ \n// Global cache shared across all instances\nconst globalCache = new Map();\nconst cacheStats = {\n    hits: 0,\n    misses: 0\n};\n// Background prefetch queue\nconst prefetchQueue = new Set();\nlet isPrefetching = false;\nconst useChatHistory = (param)=>{\n    let { configId, enablePrefetch = true, cacheTimeout = 300000, staleTimeout = 30000 // 30 seconds\n     } = param;\n    const [chatHistory, setChatHistory] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isStale, setIsStale] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const lastFetchRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Fetch function with aggressive caching\n    const fetchChatHistory = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async function(targetConfigId) {\n        let force = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false, background = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        const cacheKey = targetConfigId;\n        const cached = globalCache.get(cacheKey);\n        const now = Date.now();\n        // Return cached data if valid and not forced\n        if (!force && cached && now - cached.timestamp < cacheTimeout) {\n            cacheStats.hits++;\n            // Check if data is stale but still valid\n            const isDataStale = now - cached.timestamp > staleTimeout;\n            if (isDataStale && !cached.isStale) {\n                // Mark as stale and trigger background refresh\n                cached.isStale = true;\n                if (enablePrefetch) {\n                    prefetchQueue.add(targetConfigId);\n                    processPrefetchQueue();\n                }\n            }\n            return cached.data;\n        }\n        cacheStats.misses++;\n        // Cancel previous request if still pending\n        if (abortControllerRef.current) {\n            abortControllerRef.current.abort();\n        }\n        // Create new abort controller\n        abortControllerRef.current = new AbortController();\n        try {\n            const url = \"/api/chat/conversations?custom_api_config_id=\".concat(targetConfigId);\n            const response = await fetch(url, {\n                signal: abortControllerRef.current.signal,\n                headers: {\n                    \"Cache-Control\": \"no-cache\",\n                    \"X-Requested-With\": \"XMLHttpRequest\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to fetch chat history: \".concat(response.status, \" \").concat(response.statusText));\n            }\n            const conversations = await response.json();\n            // Update cache\n            globalCache.set(cacheKey, {\n                data: conversations,\n                timestamp: now,\n                isStale: false\n            });\n            console.log(\"\\uD83D\\uDCE6 Chat history \".concat(background ? \"prefetched\" : \"loaded\", \" for config \").concat(targetConfigId, \": \").concat(conversations.length, \" conversations\"));\n            return conversations;\n        } catch (err) {\n            if (err.name === \"AbortError\") {\n                console.log(\"Chat history fetch aborted\");\n                throw err;\n            }\n            console.error(\"Error fetching chat history:\", err);\n            // Return stale data if available\n            if (cached && cached.data.length > 0) {\n                console.log(\"Returning stale chat history data due to fetch error\");\n                return cached.data;\n            }\n            throw err;\n        }\n    }, [\n        cacheTimeout,\n        staleTimeout,\n        enablePrefetch\n    ]);\n    // Background prefetch processor\n    const processPrefetchQueue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        if (isPrefetching || prefetchQueue.size === 0) return;\n        isPrefetching = true;\n        try {\n            const configsToFetch = Array.from(prefetchQueue);\n            prefetchQueue.clear();\n            for (const configId of configsToFetch){\n                try {\n                    await fetchChatHistory(configId, true, true);\n                    // Small delay between prefetches\n                    await new Promise((resolve)=>setTimeout(resolve, 100));\n                } catch (error) {\n                    console.warn(\"Failed to prefetch chat history for config \".concat(configId, \":\"), error);\n                }\n            }\n        } finally{\n            isPrefetching = false;\n        }\n    }, [\n        fetchChatHistory\n    ]);\n    // Main refetch function\n    const refetch = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async function() {\n        let force = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        if (!configId) return;\n        // Optimistic update: show cached data immediately if available\n        const cached = globalCache.get(configId);\n        if (!force && cached && cached.data.length > 0) {\n            setChatHistory(cached.data);\n            setIsStale(cached.isStale);\n            setError(null);\n        }\n        setIsLoading(true);\n        lastFetchRef.current = configId;\n        try {\n            const conversations = await fetchChatHistory(configId, force);\n            // Only update if this is still the current config\n            if (lastFetchRef.current === configId) {\n                setChatHistory(conversations);\n                setIsStale(false);\n                setError(null);\n            }\n        } catch (err) {\n            if (err.name !== \"AbortError\" && lastFetchRef.current === configId) {\n                setError(\"Failed to load chat history: \".concat(err.message));\n            }\n        } finally{\n            if (lastFetchRef.current === configId) {\n                setIsLoading(false);\n            }\n        }\n    }, [\n        configId,\n        fetchChatHistory\n    ]);\n    // Prefetch function for external use\n    const prefetch = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (targetConfigId)=>{\n        if (!enablePrefetch) return;\n        prefetchQueue.add(targetConfigId);\n        processPrefetchQueue();\n    }, [\n        enablePrefetch,\n        processPrefetchQueue\n    ]);\n    // Cache invalidation\n    const invalidateCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((targetConfigId)=>{\n        if (targetConfigId) {\n            globalCache.delete(targetConfigId);\n            console.log(\"\\uD83D\\uDDD1️ Invalidated chat history cache for config \".concat(targetConfigId));\n        } else {\n            globalCache.clear();\n            console.log(\"\\uD83D\\uDDD1️ Cleared all chat history cache\");\n        }\n    }, []);\n    // Cache stats\n    const getCacheStats = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>({\n            size: globalCache.size,\n            hits: cacheStats.hits,\n            misses: cacheStats.misses\n        }), []);\n    // Effect to load data when configId changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (configId) {\n            refetch();\n        } else {\n            setChatHistory([]);\n            setIsLoading(false);\n            setError(null);\n            setIsStale(false);\n        }\n    }, [\n        configId,\n        refetch\n    ]);\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        return ()=>{\n            if (abortControllerRef.current) {\n                abortControllerRef.current.abort();\n            }\n        };\n    }, []);\n    return {\n        chatHistory,\n        isLoading,\n        isStale,\n        error,\n        refetch,\n        prefetch,\n        invalidateCache,\n        getCacheStats\n    };\n};\n// Hook for prefetching chat history on navigation\nconst useChatHistoryPrefetch = ()=>{\n    const prefetchedConfigs = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(new Set());\n    const prefetchChatHistory = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (configId)=>{\n        if (prefetchedConfigs.current.has(configId)) return;\n        prefetchedConfigs.current.add(configId);\n        prefetchQueue.add(configId);\n        // Process queue after a short delay to batch requests\n        setTimeout(()=>{\n            if (prefetchQueue.size > 0) {\n                const processPrefetch = async ()=>{\n                    if (isPrefetching) return;\n                    isPrefetching = true;\n                    try {\n                        const configsToFetch = Array.from(prefetchQueue);\n                        prefetchQueue.clear();\n                        for (const id of configsToFetch){\n                            try {\n                                const url = \"/api/chat/conversations?custom_api_config_id=\".concat(id);\n                                const response = await fetch(url, {\n                                    headers: {\n                                        \"X-Prefetch\": \"true\"\n                                    }\n                                });\n                                if (response.ok) {\n                                    const conversations = await response.json();\n                                    globalCache.set(id, {\n                                        data: conversations,\n                                        timestamp: Date.now(),\n                                        isStale: false\n                                    });\n                                    console.log(\"\\uD83D\\uDCE6 Prefetched chat history for config \".concat(id));\n                                }\n                            } catch (error) {\n                                console.warn(\"Failed to prefetch chat history for \".concat(id, \":\"), error);\n                            }\n                            // Small delay between requests\n                            await new Promise((resolve)=>setTimeout(resolve, 100));\n                        }\n                    } finally{\n                        isPrefetching = false;\n                    }\n                };\n                processPrefetch();\n            }\n        }, 200);\n    }, []);\n    return {\n        prefetchChatHistory\n    };\n};\n// Export cache utilities for debugging\nconst chatHistoryCache = {\n    getCache: ()=>globalCache,\n    getStats: ()=>cacheStats,\n    clear: ()=>{\n        globalCache.clear();\n        cacheStats.hits = 0;\n        cacheStats.misses = 0;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9ob29rcy91c2VDaGF0SGlzdG9yeS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs0R0FFaUU7QUEyQmpFLDJDQUEyQztBQUMzQyxNQUFNSSxjQUFjLElBQUlDO0FBQ3hCLE1BQU1DLGFBQWE7SUFBRUMsTUFBTTtJQUFHQyxRQUFRO0FBQUU7QUFFeEMsNEJBQTRCO0FBQzVCLE1BQU1DLGdCQUFnQixJQUFJQztBQUMxQixJQUFJQyxnQkFBZ0I7QUFFYixNQUFNQyxpQkFBaUI7UUFBQyxFQUM3QkMsUUFBUSxFQUNSQyxpQkFBaUIsSUFBSSxFQUNyQkMsZUFBZSxNQUFNLEVBQ3JCQyxlQUFlLE1BQVEsYUFBYTtJQUFoQixFQUNFO0lBQ3RCLE1BQU0sQ0FBQ0MsYUFBYUMsZUFBZSxHQUFHbEIsK0NBQVFBLENBQXFCLEVBQUU7SUFDckUsTUFBTSxDQUFDbUIsV0FBV0MsYUFBYSxHQUFHcEIsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDcUIsU0FBU0MsV0FBVyxHQUFHdEIsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDdUIsT0FBT0MsU0FBUyxHQUFHeEIsK0NBQVFBLENBQWdCO0lBRWxELE1BQU15QixxQkFBcUJ2Qiw2Q0FBTUEsQ0FBeUI7SUFDMUQsTUFBTXdCLGVBQWV4Qiw2Q0FBTUEsQ0FBZ0I7SUFFM0MseUNBQXlDO0lBQ3pDLE1BQU15QixtQkFBbUJ4QixrREFBV0EsQ0FBQyxlQUNuQ3lCO1lBQ0FDLHlFQUFRLE9BQ1JDLDhFQUFhO1FBRWIsTUFBTUMsV0FBV0g7UUFDakIsTUFBTUksU0FBUzVCLFlBQVk2QixHQUFHLENBQUNGO1FBQy9CLE1BQU1HLE1BQU1DLEtBQUtELEdBQUc7UUFFcEIsNkNBQTZDO1FBQzdDLElBQUksQ0FBQ0wsU0FBU0csVUFBVSxNQUFPQSxPQUFPSSxTQUFTLEdBQUlyQixjQUFjO1lBQy9EVCxXQUFXQyxJQUFJO1lBRWYseUNBQXlDO1lBQ3pDLE1BQU04QixjQUFjLE1BQU9MLE9BQU9JLFNBQVMsR0FBSXBCO1lBQy9DLElBQUlxQixlQUFlLENBQUNMLE9BQU9YLE9BQU8sRUFBRTtnQkFDbEMsK0NBQStDO2dCQUMvQ1csT0FBT1gsT0FBTyxHQUFHO2dCQUNqQixJQUFJUCxnQkFBZ0I7b0JBQ2xCTCxjQUFjNkIsR0FBRyxDQUFDVjtvQkFDbEJXO2dCQUNGO1lBQ0Y7WUFFQSxPQUFPUCxPQUFPUSxJQUFJO1FBQ3BCO1FBRUFsQyxXQUFXRSxNQUFNO1FBRWpCLDJDQUEyQztRQUMzQyxJQUFJaUIsbUJBQW1CZ0IsT0FBTyxFQUFFO1lBQzlCaEIsbUJBQW1CZ0IsT0FBTyxDQUFDQyxLQUFLO1FBQ2xDO1FBRUEsOEJBQThCO1FBQzlCakIsbUJBQW1CZ0IsT0FBTyxHQUFHLElBQUlFO1FBRWpDLElBQUk7WUFDRixNQUFNQyxNQUFNLGdEQUErRCxPQUFmaEI7WUFDNUQsTUFBTWlCLFdBQVcsTUFBTUMsTUFBTUYsS0FBSztnQkFDaENHLFFBQVF0QixtQkFBbUJnQixPQUFPLENBQUNNLE1BQU07Z0JBQ3pDQyxTQUFTO29CQUNQLGlCQUFpQjtvQkFDakIsb0JBQW9CO2dCQUN0QjtZQUNGO1lBRUEsSUFBSSxDQUFDSCxTQUFTSSxFQUFFLEVBQUU7Z0JBQ2hCLE1BQU0sSUFBSUMsTUFBTSxpQ0FBb0RMLE9BQW5CQSxTQUFTTSxNQUFNLEVBQUMsS0FBdUIsT0FBcEJOLFNBQVNPLFVBQVU7WUFDekY7WUFFQSxNQUFNQyxnQkFBb0MsTUFBTVIsU0FBU1MsSUFBSTtZQUU3RCxlQUFlO1lBQ2ZsRCxZQUFZbUQsR0FBRyxDQUFDeEIsVUFBVTtnQkFDeEJTLE1BQU1hO2dCQUNOakIsV0FBV0Y7Z0JBQ1hiLFNBQVM7WUFDWDtZQUVBbUMsUUFBUUMsR0FBRyxDQUFDLDZCQUFzRTdCLE9BQW5ERSxhQUFhLGVBQWUsVUFBUyxnQkFBaUN1QixPQUFuQnpCLGdCQUFlLE1BQXlCLE9BQXJCeUIsY0FBY0ssTUFBTSxFQUFDO1lBRTFILE9BQU9MO1FBRVQsRUFBRSxPQUFPTSxLQUFVO1lBQ2pCLElBQUlBLElBQUlDLElBQUksS0FBSyxjQUFjO2dCQUM3QkosUUFBUUMsR0FBRyxDQUFDO2dCQUNaLE1BQU1FO1lBQ1I7WUFFQUgsUUFBUWpDLEtBQUssQ0FBQyxnQ0FBZ0NvQztZQUU5QyxpQ0FBaUM7WUFDakMsSUFBSTNCLFVBQVVBLE9BQU9RLElBQUksQ0FBQ2tCLE1BQU0sR0FBRyxHQUFHO2dCQUNwQ0YsUUFBUUMsR0FBRyxDQUFDO2dCQUNaLE9BQU96QixPQUFPUSxJQUFJO1lBQ3BCO1lBRUEsTUFBTW1CO1FBQ1I7SUFDRixHQUFHO1FBQUM1QztRQUFjQztRQUFjRjtLQUFlO0lBRS9DLGdDQUFnQztJQUNoQyxNQUFNeUIsdUJBQXVCcEMsa0RBQVdBLENBQUM7UUFDdkMsSUFBSVEsaUJBQWlCRixjQUFjb0QsSUFBSSxLQUFLLEdBQUc7UUFFL0NsRCxnQkFBZ0I7UUFFaEIsSUFBSTtZQUNGLE1BQU1tRCxpQkFBaUJDLE1BQU1DLElBQUksQ0FBQ3ZEO1lBQ2xDQSxjQUFjd0QsS0FBSztZQUVuQixLQUFLLE1BQU1wRCxZQUFZaUQsZUFBZ0I7Z0JBQ3JDLElBQUk7b0JBQ0YsTUFBTW5DLGlCQUFpQmQsVUFBVSxNQUFNO29CQUN2QyxpQ0FBaUM7b0JBQ2pDLE1BQU0sSUFBSXFELFFBQVFDLENBQUFBLFVBQVdDLFdBQVdELFNBQVM7Z0JBQ25ELEVBQUUsT0FBTzVDLE9BQU87b0JBQ2RpQyxRQUFRYSxJQUFJLENBQUMsOENBQXVELE9BQVR4RCxVQUFTLE1BQUlVO2dCQUMxRTtZQUNGO1FBQ0YsU0FBVTtZQUNSWixnQkFBZ0I7UUFDbEI7SUFDRixHQUFHO1FBQUNnQjtLQUFpQjtJQUVyQix3QkFBd0I7SUFDeEIsTUFBTTJDLFVBQVVuRSxrREFBV0EsQ0FBQztZQUFPMEIseUVBQVE7UUFDekMsSUFBSSxDQUFDaEIsVUFBVTtRQUVmLCtEQUErRDtRQUMvRCxNQUFNbUIsU0FBUzVCLFlBQVk2QixHQUFHLENBQUNwQjtRQUMvQixJQUFJLENBQUNnQixTQUFTRyxVQUFVQSxPQUFPUSxJQUFJLENBQUNrQixNQUFNLEdBQUcsR0FBRztZQUM5Q3hDLGVBQWVjLE9BQU9RLElBQUk7WUFDMUJsQixXQUFXVSxPQUFPWCxPQUFPO1lBQ3pCRyxTQUFTO1FBQ1g7UUFFQUosYUFBYTtRQUNiTSxhQUFhZSxPQUFPLEdBQUc1QjtRQUV2QixJQUFJO1lBQ0YsTUFBTXdDLGdCQUFnQixNQUFNMUIsaUJBQWlCZCxVQUFVZ0I7WUFFdkQsa0RBQWtEO1lBQ2xELElBQUlILGFBQWFlLE9BQU8sS0FBSzVCLFVBQVU7Z0JBQ3JDSyxlQUFlbUM7Z0JBQ2YvQixXQUFXO2dCQUNYRSxTQUFTO1lBQ1g7UUFDRixFQUFFLE9BQU9tQyxLQUFVO1lBQ2pCLElBQUlBLElBQUlDLElBQUksS0FBSyxnQkFBZ0JsQyxhQUFhZSxPQUFPLEtBQUs1QixVQUFVO2dCQUNsRVcsU0FBUyxnQ0FBNEMsT0FBWm1DLElBQUlZLE9BQU87WUFDdEQ7UUFDRixTQUFVO1lBQ1IsSUFBSTdDLGFBQWFlLE9BQU8sS0FBSzVCLFVBQVU7Z0JBQ3JDTyxhQUFhO1lBQ2Y7UUFDRjtJQUNGLEdBQUc7UUFBQ1A7UUFBVWM7S0FBaUI7SUFFL0IscUNBQXFDO0lBQ3JDLE1BQU02QyxXQUFXckUsa0RBQVdBLENBQUMsT0FBT3lCO1FBQ2xDLElBQUksQ0FBQ2QsZ0JBQWdCO1FBRXJCTCxjQUFjNkIsR0FBRyxDQUFDVjtRQUNsQlc7SUFDRixHQUFHO1FBQUN6QjtRQUFnQnlCO0tBQXFCO0lBRXpDLHFCQUFxQjtJQUNyQixNQUFNa0Msa0JBQWtCdEUsa0RBQVdBLENBQUMsQ0FBQ3lCO1FBQ25DLElBQUlBLGdCQUFnQjtZQUNsQnhCLFlBQVlzRSxNQUFNLENBQUM5QztZQUNuQjRCLFFBQVFDLEdBQUcsQ0FBQywyREFBZ0UsT0FBZjdCO1FBQy9ELE9BQU87WUFDTHhCLFlBQVk2RCxLQUFLO1lBQ2pCVCxRQUFRQyxHQUFHLENBQUM7UUFDZDtJQUNGLEdBQUcsRUFBRTtJQUVMLGNBQWM7SUFDZCxNQUFNa0IsZ0JBQWdCeEUsa0RBQVdBLENBQUMsSUFBTztZQUN2QzBELE1BQU16RCxZQUFZeUQsSUFBSTtZQUN0QnRELE1BQU1ELFdBQVdDLElBQUk7WUFDckJDLFFBQVFGLFdBQVdFLE1BQU07UUFDM0IsSUFBSSxFQUFFO0lBRU4sNENBQTRDO0lBQzVDUCxnREFBU0EsQ0FBQztRQUNSLElBQUlZLFVBQVU7WUFDWnlEO1FBQ0YsT0FBTztZQUNMcEQsZUFBZSxFQUFFO1lBQ2pCRSxhQUFhO1lBQ2JJLFNBQVM7WUFDVEYsV0FBVztRQUNiO0lBQ0YsR0FBRztRQUFDVDtRQUFVeUQ7S0FBUTtJQUV0QixxQkFBcUI7SUFDckJyRSxnREFBU0EsQ0FBQztRQUNSLE9BQU87WUFDTCxJQUFJd0IsbUJBQW1CZ0IsT0FBTyxFQUFFO2dCQUM5QmhCLG1CQUFtQmdCLE9BQU8sQ0FBQ0MsS0FBSztZQUNsQztRQUNGO0lBQ0YsR0FBRyxFQUFFO0lBRUwsT0FBTztRQUNMekI7UUFDQUU7UUFDQUU7UUFDQUU7UUFDQStDO1FBQ0FFO1FBQ0FDO1FBQ0FFO0lBQ0Y7QUFDRixFQUFFO0FBRUYsa0RBQWtEO0FBQzNDLE1BQU1DLHlCQUF5QjtJQUNwQyxNQUFNQyxvQkFBb0IzRSw2Q0FBTUEsQ0FBQyxJQUFJUTtJQUVyQyxNQUFNb0Usc0JBQXNCM0Usa0RBQVdBLENBQUMsT0FBT1U7UUFDN0MsSUFBSWdFLGtCQUFrQnBDLE9BQU8sQ0FBQ3NDLEdBQUcsQ0FBQ2xFLFdBQVc7UUFFN0NnRSxrQkFBa0JwQyxPQUFPLENBQUNILEdBQUcsQ0FBQ3pCO1FBQzlCSixjQUFjNkIsR0FBRyxDQUFDekI7UUFFbEIsc0RBQXNEO1FBQ3REdUQsV0FBVztZQUNULElBQUkzRCxjQUFjb0QsSUFBSSxHQUFHLEdBQUc7Z0JBQzFCLE1BQU1tQixrQkFBa0I7b0JBQ3RCLElBQUlyRSxlQUFlO29CQUNuQkEsZ0JBQWdCO29CQUVoQixJQUFJO3dCQUNGLE1BQU1tRCxpQkFBaUJDLE1BQU1DLElBQUksQ0FBQ3ZEO3dCQUNsQ0EsY0FBY3dELEtBQUs7d0JBRW5CLEtBQUssTUFBTWdCLE1BQU1uQixlQUFnQjs0QkFDL0IsSUFBSTtnQ0FDRixNQUFNbEIsTUFBTSxnREFBbUQsT0FBSHFDO2dDQUM1RCxNQUFNcEMsV0FBVyxNQUFNQyxNQUFNRixLQUFLO29DQUNoQ0ksU0FBUzt3Q0FBRSxjQUFjO29DQUFPO2dDQUNsQztnQ0FFQSxJQUFJSCxTQUFTSSxFQUFFLEVBQUU7b0NBQ2YsTUFBTUksZ0JBQWdCLE1BQU1SLFNBQVNTLElBQUk7b0NBQ3pDbEQsWUFBWW1ELEdBQUcsQ0FBQzBCLElBQUk7d0NBQ2xCekMsTUFBTWE7d0NBQ05qQixXQUFXRCxLQUFLRCxHQUFHO3dDQUNuQmIsU0FBUztvQ0FDWDtvQ0FDQW1DLFFBQVFDLEdBQUcsQ0FBQyxtREFBNEMsT0FBSHdCO2dDQUN2RDs0QkFDRixFQUFFLE9BQU8xRCxPQUFPO2dDQUNkaUMsUUFBUWEsSUFBSSxDQUFDLHVDQUEwQyxPQUFIWSxJQUFHLE1BQUkxRDs0QkFDN0Q7NEJBRUEsK0JBQStCOzRCQUMvQixNQUFNLElBQUkyQyxRQUFRQyxDQUFBQSxVQUFXQyxXQUFXRCxTQUFTO3dCQUNuRDtvQkFDRixTQUFVO3dCQUNSeEQsZ0JBQWdCO29CQUNsQjtnQkFDRjtnQkFFQXFFO1lBQ0Y7UUFDRixHQUFHO0lBQ0wsR0FBRyxFQUFFO0lBRUwsT0FBTztRQUFFRjtJQUFvQjtBQUMvQixFQUFFO0FBRUYsdUNBQXVDO0FBQ2hDLE1BQU1JLG1CQUFtQjtJQUM5QkMsVUFBVSxJQUFNL0U7SUFDaEJnRixVQUFVLElBQU05RTtJQUNoQjJELE9BQU87UUFDTDdELFlBQVk2RCxLQUFLO1FBQ2pCM0QsV0FBV0MsSUFBSSxHQUFHO1FBQ2xCRCxXQUFXRSxNQUFNLEdBQUc7SUFDdEI7QUFDRixFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9ob29rcy91c2VDaGF0SGlzdG9yeS50cz8zYTZmIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCwgdXNlUmVmLCB1c2VDYWxsYmFjayB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHR5cGUgQ2hhdENvbnZlcnNhdGlvbiB9IGZyb20gJ0AvdHlwZXMvY2hhdEhpc3RvcnknO1xuXG5pbnRlcmZhY2UgQ2hhdEhpc3RvcnlDYWNoZSB7XG4gIGRhdGE6IENoYXRDb252ZXJzYXRpb25bXTtcbiAgdGltZXN0YW1wOiBudW1iZXI7XG4gIGlzU3RhbGU6IGJvb2xlYW47XG59XG5cbmludGVyZmFjZSBVc2VDaGF0SGlzdG9yeU9wdGlvbnMge1xuICBjb25maWdJZDogc3RyaW5nIHwgbnVsbDtcbiAgZW5hYmxlUHJlZmV0Y2g/OiBib29sZWFuO1xuICBjYWNoZVRpbWVvdXQ/OiBudW1iZXI7XG4gIHN0YWxlVGltZW91dD86IG51bWJlcjtcbn1cblxuaW50ZXJmYWNlIFVzZUNoYXRIaXN0b3J5UmV0dXJuIHtcbiAgY2hhdEhpc3Rvcnk6IENoYXRDb252ZXJzYXRpb25bXTtcbiAgaXNMb2FkaW5nOiBib29sZWFuO1xuICBpc1N0YWxlOiBib29sZWFuO1xuICBlcnJvcjogc3RyaW5nIHwgbnVsbDtcbiAgcmVmZXRjaDogKGZvcmNlPzogYm9vbGVhbikgPT4gUHJvbWlzZTx2b2lkPjtcbiAgcHJlZmV0Y2g6IChjb25maWdJZDogc3RyaW5nKSA9PiBQcm9taXNlPHZvaWQ+O1xuICBpbnZhbGlkYXRlQ2FjaGU6IChjb25maWdJZD86IHN0cmluZykgPT4gdm9pZDtcbiAgZ2V0Q2FjaGVTdGF0czogKCkgPT4geyBzaXplOiBudW1iZXI7IGhpdHM6IG51bWJlcjsgbWlzc2VzOiBudW1iZXIgfTtcbn1cblxuLy8gR2xvYmFsIGNhY2hlIHNoYXJlZCBhY3Jvc3MgYWxsIGluc3RhbmNlc1xuY29uc3QgZ2xvYmFsQ2FjaGUgPSBuZXcgTWFwPHN0cmluZywgQ2hhdEhpc3RvcnlDYWNoZT4oKTtcbmNvbnN0IGNhY2hlU3RhdHMgPSB7IGhpdHM6IDAsIG1pc3NlczogMCB9O1xuXG4vLyBCYWNrZ3JvdW5kIHByZWZldGNoIHF1ZXVlXG5jb25zdCBwcmVmZXRjaFF1ZXVlID0gbmV3IFNldDxzdHJpbmc+KCk7XG5sZXQgaXNQcmVmZXRjaGluZyA9IGZhbHNlO1xuXG5leHBvcnQgY29uc3QgdXNlQ2hhdEhpc3RvcnkgPSAoe1xuICBjb25maWdJZCxcbiAgZW5hYmxlUHJlZmV0Y2ggPSB0cnVlLFxuICBjYWNoZVRpbWVvdXQgPSAzMDAwMDAsIC8vIDUgbWludXRlc1xuICBzdGFsZVRpbWVvdXQgPSAzMDAwMCAgIC8vIDMwIHNlY29uZHNcbn06IFVzZUNoYXRIaXN0b3J5T3B0aW9ucyk6IFVzZUNoYXRIaXN0b3J5UmV0dXJuID0+IHtcbiAgY29uc3QgW2NoYXRIaXN0b3J5LCBzZXRDaGF0SGlzdG9yeV0gPSB1c2VTdGF0ZTxDaGF0Q29udmVyc2F0aW9uW10+KFtdKTtcbiAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2lzU3RhbGUsIHNldElzU3RhbGVdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuICBcbiAgY29uc3QgYWJvcnRDb250cm9sbGVyUmVmID0gdXNlUmVmPEFib3J0Q29udHJvbGxlciB8IG51bGw+KG51bGwpO1xuICBjb25zdCBsYXN0RmV0Y2hSZWYgPSB1c2VSZWY8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG5cbiAgLy8gRmV0Y2ggZnVuY3Rpb24gd2l0aCBhZ2dyZXNzaXZlIGNhY2hpbmdcbiAgY29uc3QgZmV0Y2hDaGF0SGlzdG9yeSA9IHVzZUNhbGxiYWNrKGFzeW5jIChcbiAgICB0YXJnZXRDb25maWdJZDogc3RyaW5nLFxuICAgIGZvcmNlID0gZmFsc2UsXG4gICAgYmFja2dyb3VuZCA9IGZhbHNlXG4gICk6IFByb21pc2U8Q2hhdENvbnZlcnNhdGlvbltdPiA9PiB7XG4gICAgY29uc3QgY2FjaGVLZXkgPSB0YXJnZXRDb25maWdJZDtcbiAgICBjb25zdCBjYWNoZWQgPSBnbG9iYWxDYWNoZS5nZXQoY2FjaGVLZXkpO1xuICAgIGNvbnN0IG5vdyA9IERhdGUubm93KCk7XG5cbiAgICAvLyBSZXR1cm4gY2FjaGVkIGRhdGEgaWYgdmFsaWQgYW5kIG5vdCBmb3JjZWRcbiAgICBpZiAoIWZvcmNlICYmIGNhY2hlZCAmJiAobm93IC0gY2FjaGVkLnRpbWVzdGFtcCkgPCBjYWNoZVRpbWVvdXQpIHtcbiAgICAgIGNhY2hlU3RhdHMuaGl0cysrO1xuICAgICAgXG4gICAgICAvLyBDaGVjayBpZiBkYXRhIGlzIHN0YWxlIGJ1dCBzdGlsbCB2YWxpZFxuICAgICAgY29uc3QgaXNEYXRhU3RhbGUgPSAobm93IC0gY2FjaGVkLnRpbWVzdGFtcCkgPiBzdGFsZVRpbWVvdXQ7XG4gICAgICBpZiAoaXNEYXRhU3RhbGUgJiYgIWNhY2hlZC5pc1N0YWxlKSB7XG4gICAgICAgIC8vIE1hcmsgYXMgc3RhbGUgYW5kIHRyaWdnZXIgYmFja2dyb3VuZCByZWZyZXNoXG4gICAgICAgIGNhY2hlZC5pc1N0YWxlID0gdHJ1ZTtcbiAgICAgICAgaWYgKGVuYWJsZVByZWZldGNoKSB7XG4gICAgICAgICAgcHJlZmV0Y2hRdWV1ZS5hZGQodGFyZ2V0Q29uZmlnSWQpO1xuICAgICAgICAgIHByb2Nlc3NQcmVmZXRjaFF1ZXVlKCk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIFxuICAgICAgcmV0dXJuIGNhY2hlZC5kYXRhO1xuICAgIH1cblxuICAgIGNhY2hlU3RhdHMubWlzc2VzKys7XG5cbiAgICAvLyBDYW5jZWwgcHJldmlvdXMgcmVxdWVzdCBpZiBzdGlsbCBwZW5kaW5nXG4gICAgaWYgKGFib3J0Q29udHJvbGxlclJlZi5jdXJyZW50KSB7XG4gICAgICBhYm9ydENvbnRyb2xsZXJSZWYuY3VycmVudC5hYm9ydCgpO1xuICAgIH1cblxuICAgIC8vIENyZWF0ZSBuZXcgYWJvcnQgY29udHJvbGxlclxuICAgIGFib3J0Q29udHJvbGxlclJlZi5jdXJyZW50ID0gbmV3IEFib3J0Q29udHJvbGxlcigpO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHVybCA9IGAvYXBpL2NoYXQvY29udmVyc2F0aW9ucz9jdXN0b21fYXBpX2NvbmZpZ19pZD0ke3RhcmdldENvbmZpZ0lkfWA7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKHVybCwge1xuICAgICAgICBzaWduYWw6IGFib3J0Q29udHJvbGxlclJlZi5jdXJyZW50LnNpZ25hbCxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdDYWNoZS1Db250cm9sJzogJ25vLWNhY2hlJyxcbiAgICAgICAgICAnWC1SZXF1ZXN0ZWQtV2l0aCc6ICdYTUxIdHRwUmVxdWVzdCdcbiAgICAgICAgfVxuICAgICAgfSk7XG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZmV0Y2ggY2hhdCBoaXN0b3J5OiAke3Jlc3BvbnNlLnN0YXR1c30gJHtyZXNwb25zZS5zdGF0dXNUZXh0fWApO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBjb252ZXJzYXRpb25zOiBDaGF0Q29udmVyc2F0aW9uW10gPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG5cbiAgICAgIC8vIFVwZGF0ZSBjYWNoZVxuICAgICAgZ2xvYmFsQ2FjaGUuc2V0KGNhY2hlS2V5LCB7XG4gICAgICAgIGRhdGE6IGNvbnZlcnNhdGlvbnMsXG4gICAgICAgIHRpbWVzdGFtcDogbm93LFxuICAgICAgICBpc1N0YWxlOiBmYWxzZVxuICAgICAgfSk7XG5cbiAgICAgIGNvbnNvbGUubG9nKGDwn5OmIENoYXQgaGlzdG9yeSAke2JhY2tncm91bmQgPyAncHJlZmV0Y2hlZCcgOiAnbG9hZGVkJ30gZm9yIGNvbmZpZyAke3RhcmdldENvbmZpZ0lkfTogJHtjb252ZXJzYXRpb25zLmxlbmd0aH0gY29udmVyc2F0aW9uc2ApO1xuICAgICAgXG4gICAgICByZXR1cm4gY29udmVyc2F0aW9ucztcblxuICAgIH0gY2F0Y2ggKGVycjogYW55KSB7XG4gICAgICBpZiAoZXJyLm5hbWUgPT09ICdBYm9ydEVycm9yJykge1xuICAgICAgICBjb25zb2xlLmxvZygnQ2hhdCBoaXN0b3J5IGZldGNoIGFib3J0ZWQnKTtcbiAgICAgICAgdGhyb3cgZXJyO1xuICAgICAgfVxuICAgICAgXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBjaGF0IGhpc3Rvcnk6JywgZXJyKTtcbiAgICAgIFxuICAgICAgLy8gUmV0dXJuIHN0YWxlIGRhdGEgaWYgYXZhaWxhYmxlXG4gICAgICBpZiAoY2FjaGVkICYmIGNhY2hlZC5kYXRhLmxlbmd0aCA+IDApIHtcbiAgICAgICAgY29uc29sZS5sb2coJ1JldHVybmluZyBzdGFsZSBjaGF0IGhpc3RvcnkgZGF0YSBkdWUgdG8gZmV0Y2ggZXJyb3InKTtcbiAgICAgICAgcmV0dXJuIGNhY2hlZC5kYXRhO1xuICAgICAgfVxuICAgICAgXG4gICAgICB0aHJvdyBlcnI7XG4gICAgfVxuICB9LCBbY2FjaGVUaW1lb3V0LCBzdGFsZVRpbWVvdXQsIGVuYWJsZVByZWZldGNoXSk7XG5cbiAgLy8gQmFja2dyb3VuZCBwcmVmZXRjaCBwcm9jZXNzb3JcbiAgY29uc3QgcHJvY2Vzc1ByZWZldGNoUXVldWUgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XG4gICAgaWYgKGlzUHJlZmV0Y2hpbmcgfHwgcHJlZmV0Y2hRdWV1ZS5zaXplID09PSAwKSByZXR1cm47XG4gICAgXG4gICAgaXNQcmVmZXRjaGluZyA9IHRydWU7XG4gICAgXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGNvbmZpZ3NUb0ZldGNoID0gQXJyYXkuZnJvbShwcmVmZXRjaFF1ZXVlKTtcbiAgICAgIHByZWZldGNoUXVldWUuY2xlYXIoKTtcbiAgICAgIFxuICAgICAgZm9yIChjb25zdCBjb25maWdJZCBvZiBjb25maWdzVG9GZXRjaCkge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGF3YWl0IGZldGNoQ2hhdEhpc3RvcnkoY29uZmlnSWQsIHRydWUsIHRydWUpO1xuICAgICAgICAgIC8vIFNtYWxsIGRlbGF5IGJldHdlZW4gcHJlZmV0Y2hlc1xuICAgICAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCAxMDApKTtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICBjb25zb2xlLndhcm4oYEZhaWxlZCB0byBwcmVmZXRjaCBjaGF0IGhpc3RvcnkgZm9yIGNvbmZpZyAke2NvbmZpZ0lkfTpgLCBlcnJvcik7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9IGZpbmFsbHkge1xuICAgICAgaXNQcmVmZXRjaGluZyA9IGZhbHNlO1xuICAgIH1cbiAgfSwgW2ZldGNoQ2hhdEhpc3RvcnldKTtcblxuICAvLyBNYWluIHJlZmV0Y2ggZnVuY3Rpb25cbiAgY29uc3QgcmVmZXRjaCA9IHVzZUNhbGxiYWNrKGFzeW5jIChmb3JjZSA9IGZhbHNlKSA9PiB7XG4gICAgaWYgKCFjb25maWdJZCkgcmV0dXJuO1xuICAgIFxuICAgIC8vIE9wdGltaXN0aWMgdXBkYXRlOiBzaG93IGNhY2hlZCBkYXRhIGltbWVkaWF0ZWx5IGlmIGF2YWlsYWJsZVxuICAgIGNvbnN0IGNhY2hlZCA9IGdsb2JhbENhY2hlLmdldChjb25maWdJZCk7XG4gICAgaWYgKCFmb3JjZSAmJiBjYWNoZWQgJiYgY2FjaGVkLmRhdGEubGVuZ3RoID4gMCkge1xuICAgICAgc2V0Q2hhdEhpc3RvcnkoY2FjaGVkLmRhdGEpO1xuICAgICAgc2V0SXNTdGFsZShjYWNoZWQuaXNTdGFsZSk7XG4gICAgICBzZXRFcnJvcihudWxsKTtcbiAgICB9XG5cbiAgICBzZXRJc0xvYWRpbmcodHJ1ZSk7XG4gICAgbGFzdEZldGNoUmVmLmN1cnJlbnQgPSBjb25maWdJZDtcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCBjb252ZXJzYXRpb25zID0gYXdhaXQgZmV0Y2hDaGF0SGlzdG9yeShjb25maWdJZCwgZm9yY2UpO1xuICAgICAgXG4gICAgICAvLyBPbmx5IHVwZGF0ZSBpZiB0aGlzIGlzIHN0aWxsIHRoZSBjdXJyZW50IGNvbmZpZ1xuICAgICAgaWYgKGxhc3RGZXRjaFJlZi5jdXJyZW50ID09PSBjb25maWdJZCkge1xuICAgICAgICBzZXRDaGF0SGlzdG9yeShjb252ZXJzYXRpb25zKTtcbiAgICAgICAgc2V0SXNTdGFsZShmYWxzZSk7XG4gICAgICAgIHNldEVycm9yKG51bGwpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycjogYW55KSB7XG4gICAgICBpZiAoZXJyLm5hbWUgIT09ICdBYm9ydEVycm9yJyAmJiBsYXN0RmV0Y2hSZWYuY3VycmVudCA9PT0gY29uZmlnSWQpIHtcbiAgICAgICAgc2V0RXJyb3IoYEZhaWxlZCB0byBsb2FkIGNoYXQgaGlzdG9yeTogJHtlcnIubWVzc2FnZX1gKTtcbiAgICAgIH1cbiAgICB9IGZpbmFsbHkge1xuICAgICAgaWYgKGxhc3RGZXRjaFJlZi5jdXJyZW50ID09PSBjb25maWdJZCkge1xuICAgICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgICAgfVxuICAgIH1cbiAgfSwgW2NvbmZpZ0lkLCBmZXRjaENoYXRIaXN0b3J5XSk7XG5cbiAgLy8gUHJlZmV0Y2ggZnVuY3Rpb24gZm9yIGV4dGVybmFsIHVzZVxuICBjb25zdCBwcmVmZXRjaCA9IHVzZUNhbGxiYWNrKGFzeW5jICh0YXJnZXRDb25maWdJZDogc3RyaW5nKSA9PiB7XG4gICAgaWYgKCFlbmFibGVQcmVmZXRjaCkgcmV0dXJuO1xuICAgIFxuICAgIHByZWZldGNoUXVldWUuYWRkKHRhcmdldENvbmZpZ0lkKTtcbiAgICBwcm9jZXNzUHJlZmV0Y2hRdWV1ZSgpO1xuICB9LCBbZW5hYmxlUHJlZmV0Y2gsIHByb2Nlc3NQcmVmZXRjaFF1ZXVlXSk7XG5cbiAgLy8gQ2FjaGUgaW52YWxpZGF0aW9uXG4gIGNvbnN0IGludmFsaWRhdGVDYWNoZSA9IHVzZUNhbGxiYWNrKCh0YXJnZXRDb25maWdJZD86IHN0cmluZykgPT4ge1xuICAgIGlmICh0YXJnZXRDb25maWdJZCkge1xuICAgICAgZ2xvYmFsQ2FjaGUuZGVsZXRlKHRhcmdldENvbmZpZ0lkKTtcbiAgICAgIGNvbnNvbGUubG9nKGDwn5eR77iPIEludmFsaWRhdGVkIGNoYXQgaGlzdG9yeSBjYWNoZSBmb3IgY29uZmlnICR7dGFyZ2V0Q29uZmlnSWR9YCk7XG4gICAgfSBlbHNlIHtcbiAgICAgIGdsb2JhbENhY2hlLmNsZWFyKCk7XG4gICAgICBjb25zb2xlLmxvZygn8J+Xke+4jyBDbGVhcmVkIGFsbCBjaGF0IGhpc3RvcnkgY2FjaGUnKTtcbiAgICB9XG4gIH0sIFtdKTtcblxuICAvLyBDYWNoZSBzdGF0c1xuICBjb25zdCBnZXRDYWNoZVN0YXRzID0gdXNlQ2FsbGJhY2soKCkgPT4gKHtcbiAgICBzaXplOiBnbG9iYWxDYWNoZS5zaXplLFxuICAgIGhpdHM6IGNhY2hlU3RhdHMuaGl0cyxcbiAgICBtaXNzZXM6IGNhY2hlU3RhdHMubWlzc2VzXG4gIH0pLCBbXSk7XG5cbiAgLy8gRWZmZWN0IHRvIGxvYWQgZGF0YSB3aGVuIGNvbmZpZ0lkIGNoYW5nZXNcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoY29uZmlnSWQpIHtcbiAgICAgIHJlZmV0Y2goKTtcbiAgICB9IGVsc2Uge1xuICAgICAgc2V0Q2hhdEhpc3RvcnkoW10pO1xuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICAgIHNldEVycm9yKG51bGwpO1xuICAgICAgc2V0SXNTdGFsZShmYWxzZSk7XG4gICAgfVxuICB9LCBbY29uZmlnSWQsIHJlZmV0Y2hdKTtcblxuICAvLyBDbGVhbnVwIG9uIHVubW91bnRcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgaWYgKGFib3J0Q29udHJvbGxlclJlZi5jdXJyZW50KSB7XG4gICAgICAgIGFib3J0Q29udHJvbGxlclJlZi5jdXJyZW50LmFib3J0KCk7XG4gICAgICB9XG4gICAgfTtcbiAgfSwgW10pO1xuXG4gIHJldHVybiB7XG4gICAgY2hhdEhpc3RvcnksXG4gICAgaXNMb2FkaW5nLFxuICAgIGlzU3RhbGUsXG4gICAgZXJyb3IsXG4gICAgcmVmZXRjaCxcbiAgICBwcmVmZXRjaCxcbiAgICBpbnZhbGlkYXRlQ2FjaGUsXG4gICAgZ2V0Q2FjaGVTdGF0c1xuICB9O1xufTtcblxuLy8gSG9vayBmb3IgcHJlZmV0Y2hpbmcgY2hhdCBoaXN0b3J5IG9uIG5hdmlnYXRpb25cbmV4cG9ydCBjb25zdCB1c2VDaGF0SGlzdG9yeVByZWZldGNoID0gKCkgPT4ge1xuICBjb25zdCBwcmVmZXRjaGVkQ29uZmlncyA9IHVzZVJlZihuZXcgU2V0PHN0cmluZz4oKSk7XG5cbiAgY29uc3QgcHJlZmV0Y2hDaGF0SGlzdG9yeSA9IHVzZUNhbGxiYWNrKGFzeW5jIChjb25maWdJZDogc3RyaW5nKSA9PiB7XG4gICAgaWYgKHByZWZldGNoZWRDb25maWdzLmN1cnJlbnQuaGFzKGNvbmZpZ0lkKSkgcmV0dXJuO1xuICAgIFxuICAgIHByZWZldGNoZWRDb25maWdzLmN1cnJlbnQuYWRkKGNvbmZpZ0lkKTtcbiAgICBwcmVmZXRjaFF1ZXVlLmFkZChjb25maWdJZCk7XG4gICAgXG4gICAgLy8gUHJvY2VzcyBxdWV1ZSBhZnRlciBhIHNob3J0IGRlbGF5IHRvIGJhdGNoIHJlcXVlc3RzXG4gICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICBpZiAocHJlZmV0Y2hRdWV1ZS5zaXplID4gMCkge1xuICAgICAgICBjb25zdCBwcm9jZXNzUHJlZmV0Y2ggPSBhc3luYyAoKSA9PiB7XG4gICAgICAgICAgaWYgKGlzUHJlZmV0Y2hpbmcpIHJldHVybjtcbiAgICAgICAgICBpc1ByZWZldGNoaW5nID0gdHJ1ZTtcbiAgICAgICAgICBcbiAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc3QgY29uZmlnc1RvRmV0Y2ggPSBBcnJheS5mcm9tKHByZWZldGNoUXVldWUpO1xuICAgICAgICAgICAgcHJlZmV0Y2hRdWV1ZS5jbGVhcigpO1xuICAgICAgICAgICAgXG4gICAgICAgICAgICBmb3IgKGNvbnN0IGlkIG9mIGNvbmZpZ3NUb0ZldGNoKSB7XG4gICAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgY29uc3QgdXJsID0gYC9hcGkvY2hhdC9jb252ZXJzYXRpb25zP2N1c3RvbV9hcGlfY29uZmlnX2lkPSR7aWR9YDtcbiAgICAgICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKHVybCwge1xuICAgICAgICAgICAgICAgICAgaGVhZGVyczogeyAnWC1QcmVmZXRjaCc6ICd0cnVlJyB9XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XG4gICAgICAgICAgICAgICAgICBjb25zdCBjb252ZXJzYXRpb25zID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICAgICAgICAgICAgZ2xvYmFsQ2FjaGUuc2V0KGlkLCB7XG4gICAgICAgICAgICAgICAgICAgIGRhdGE6IGNvbnZlcnNhdGlvbnMsXG4gICAgICAgICAgICAgICAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKSxcbiAgICAgICAgICAgICAgICAgICAgaXNTdGFsZTogZmFsc2VcbiAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coYPCfk6YgUHJlZmV0Y2hlZCBjaGF0IGhpc3RvcnkgZm9yIGNvbmZpZyAke2lkfWApO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oYEZhaWxlZCB0byBwcmVmZXRjaCBjaGF0IGhpc3RvcnkgZm9yICR7aWR9OmAsIGVycm9yKTtcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgLy8gU21hbGwgZGVsYXkgYmV0d2VlbiByZXF1ZXN0c1xuICAgICAgICAgICAgICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgMTAwKSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSBmaW5hbGx5IHtcbiAgICAgICAgICAgIGlzUHJlZmV0Y2hpbmcgPSBmYWxzZTtcbiAgICAgICAgICB9XG4gICAgICAgIH07XG4gICAgICAgIFxuICAgICAgICBwcm9jZXNzUHJlZmV0Y2goKTtcbiAgICAgIH1cbiAgICB9LCAyMDApO1xuICB9LCBbXSk7XG5cbiAgcmV0dXJuIHsgcHJlZmV0Y2hDaGF0SGlzdG9yeSB9O1xufTtcblxuLy8gRXhwb3J0IGNhY2hlIHV0aWxpdGllcyBmb3IgZGVidWdnaW5nXG5leHBvcnQgY29uc3QgY2hhdEhpc3RvcnlDYWNoZSA9IHtcbiAgZ2V0Q2FjaGU6ICgpID0+IGdsb2JhbENhY2hlLFxuICBnZXRTdGF0czogKCkgPT4gY2FjaGVTdGF0cyxcbiAgY2xlYXI6ICgpID0+IHtcbiAgICBnbG9iYWxDYWNoZS5jbGVhcigpO1xuICAgIGNhY2hlU3RhdHMuaGl0cyA9IDA7XG4gICAgY2FjaGVTdGF0cy5taXNzZXMgPSAwO1xuICB9XG59O1xuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlUmVmIiwidXNlQ2FsbGJhY2siLCJnbG9iYWxDYWNoZSIsIk1hcCIsImNhY2hlU3RhdHMiLCJoaXRzIiwibWlzc2VzIiwicHJlZmV0Y2hRdWV1ZSIsIlNldCIsImlzUHJlZmV0Y2hpbmciLCJ1c2VDaGF0SGlzdG9yeSIsImNvbmZpZ0lkIiwiZW5hYmxlUHJlZmV0Y2giLCJjYWNoZVRpbWVvdXQiLCJzdGFsZVRpbWVvdXQiLCJjaGF0SGlzdG9yeSIsInNldENoYXRIaXN0b3J5IiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwiaXNTdGFsZSIsInNldElzU3RhbGUiLCJlcnJvciIsInNldEVycm9yIiwiYWJvcnRDb250cm9sbGVyUmVmIiwibGFzdEZldGNoUmVmIiwiZmV0Y2hDaGF0SGlzdG9yeSIsInRhcmdldENvbmZpZ0lkIiwiZm9yY2UiLCJiYWNrZ3JvdW5kIiwiY2FjaGVLZXkiLCJjYWNoZWQiLCJnZXQiLCJub3ciLCJEYXRlIiwidGltZXN0YW1wIiwiaXNEYXRhU3RhbGUiLCJhZGQiLCJwcm9jZXNzUHJlZmV0Y2hRdWV1ZSIsImRhdGEiLCJjdXJyZW50IiwiYWJvcnQiLCJBYm9ydENvbnRyb2xsZXIiLCJ1cmwiLCJyZXNwb25zZSIsImZldGNoIiwic2lnbmFsIiwiaGVhZGVycyIsIm9rIiwiRXJyb3IiLCJzdGF0dXMiLCJzdGF0dXNUZXh0IiwiY29udmVyc2F0aW9ucyIsImpzb24iLCJzZXQiLCJjb25zb2xlIiwibG9nIiwibGVuZ3RoIiwiZXJyIiwibmFtZSIsInNpemUiLCJjb25maWdzVG9GZXRjaCIsIkFycmF5IiwiZnJvbSIsImNsZWFyIiwiUHJvbWlzZSIsInJlc29sdmUiLCJzZXRUaW1lb3V0Iiwid2FybiIsInJlZmV0Y2giLCJtZXNzYWdlIiwicHJlZmV0Y2giLCJpbnZhbGlkYXRlQ2FjaGUiLCJkZWxldGUiLCJnZXRDYWNoZVN0YXRzIiwidXNlQ2hhdEhpc3RvcnlQcmVmZXRjaCIsInByZWZldGNoZWRDb25maWdzIiwicHJlZmV0Y2hDaGF0SGlzdG9yeSIsImhhcyIsInByb2Nlc3NQcmVmZXRjaCIsImlkIiwiY2hhdEhpc3RvcnlDYWNoZSIsImdldENhY2hlIiwiZ2V0U3RhdHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useChatHistory.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/AcademicCapIcon.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/solid/esm/AcademicCapIcon.js ***!
  \***********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction AcademicCapIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        viewBox: \"0 0 24 24\",\n        fill: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        d: \"M11.7 2.805a.75.75 0 0 1 .6 0A60.65 60.65 0 0 1 22.83 8.72a.75.75 0 0 1-.231 1.337 49.948 49.948 0 0 0-9.902 3.912l-.003.002c-.114.06-.227.119-.34.18a.75.75 0 0 1-.707 0A50.88 50.88 0 0 0 7.5 12.173v-.224c0-.131.067-.248.172-.311a54.615 54.615 0 0 1 4.653-2.52.75.75 0 0 0-.65-1.352 56.123 56.123 0 0 0-4.78 2.589 1.858 1.858 0 0 0-.859 1.228 49.803 49.803 0 0 0-4.634-1.527.75.75 0 0 1-.231-1.337A60.653 60.653 0 0 1 11.7 2.805Z\"\n    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        d: \"M13.06 15.473a48.45 48.45 0 0 1 7.666-3.282c.134 1.414.22 2.843.255 4.284a.75.75 0 0 1-.46.711 47.87 47.87 0 0 0-8.105 4.342.75.75 0 0 1-.832 0 47.87 47.87 0 0 0-8.104-4.342.75.75 0 0 1-.461-.71c.035-1.442.121-2.87.255-4.286.921.304 1.83.634 2.726.99v1.27a1.5 1.5 0 0 0-.14 2.508c-.09.38-.222.753-.397 1.11.452.213.901.434 1.346.66a6.727 6.727 0 0 0 .551-1.607 1.5 1.5 0 0 0 .14-2.67v-.645a48.549 48.549 0 0 1 3.44 1.667 2.25 2.25 0 0 0 2.12 0Z\"\n    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        d: \"M4.462 19.462c.42-.419.753-.89 1-1.395.453.214.902.435 1.347.662a6.742 6.742 0 0 1-1.286 1.794.75.75 0 0 1-1.06-1.06Z\"\n    }));\n}\n_c = AcademicCapIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(AcademicCapIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"AcademicCapIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/AcademicCapIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/BeakerIcon.js":
/*!******************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/solid/esm/BeakerIcon.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction BeakerIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        viewBox: \"0 0 24 24\",\n        fill: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        fillRule: \"evenodd\",\n        d: \"M10.5 3.798v5.02a3 3 0 0 1-.879 2.121l-2.377 2.377a9.845 9.845 0 0 1 5.091 1.013 8.315 8.315 0 0 0 5.713.636l.285-.071-3.954-3.955a3 3 0 0 1-.879-2.121v-5.02a23.614 23.614 0 0 0-3 0Zm4.5.138a.75.75 0 0 0 .093-1.495A24.837 24.837 0 0 0 12 2.25a25.048 25.048 0 0 0-3.093.191A.75.75 0 0 0 9 3.936v4.882a1.5 1.5 0 0 1-.44 1.06l-6.293 6.294c-1.62 1.621-.903 4.475 1.471 4.88 2.686.46 5.447.698 8.262.698 2.816 0 5.576-.239 8.262-.697 2.373-.406 3.092-3.26 1.47-4.881L15.44 9.879A1.5 1.5 0 0 1 15 8.818V3.936Z\",\n        clipRule: \"evenodd\"\n    }));\n}\n_c = BeakerIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(BeakerIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"BeakerIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/BeakerIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/ChartBarIcon.js":
/*!********************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/solid/esm/ChartBarIcon.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction ChartBarIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        viewBox: \"0 0 24 24\",\n        fill: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        d: \"M18.375 2.25c-1.035 0-1.875.84-1.875 1.875v15.75c0 1.035.84 1.875 1.875 1.875h.75c1.035 0 1.875-.84 1.875-1.875V4.125c0-1.036-.84-1.875-1.875-1.875h-.75ZM9.75 8.625c0-1.036.84-1.875 1.875-1.875h.75c1.036 0 1.875.84 1.875 1.875v11.25c0 1.035-.84 1.875-1.875 1.875h-.75a1.875 1.875 0 0 1-1.875-1.875V8.625ZM3 13.125c0-1.036.84-1.875 1.875-1.875h.75c1.036 0 1.875.84 1.875 1.875v6.75c0 1.035-.84 1.875-1.875 1.875h-.75A1.875 1.875 0 0 1 3 19.875v-6.75Z\"\n    }));\n}\n_c = ChartBarIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ChartBarIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"ChartBarIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/ChartBarIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/DocumentTextIcon.js":
/*!************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/solid/esm/DocumentTextIcon.js ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction DocumentTextIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        viewBox: \"0 0 24 24\",\n        fill: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        fillRule: \"evenodd\",\n        d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625ZM7.5 15a.75.75 0 0 1 .75-.75h7.5a.75.75 0 0 1 0 1.5h-7.5A.75.75 0 0 1 7.5 15Zm.75 2.25a.75.75 0 0 0 0 1.5H12a.75.75 0 0 0 0-1.5H8.25Z\",\n        clipRule: \"evenodd\"\n    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n    }));\n}\n_c = DocumentTextIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(DocumentTextIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"DocumentTextIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/DocumentTextIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/HomeIcon.js":
/*!****************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/solid/esm/HomeIcon.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction HomeIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        viewBox: \"0 0 24 24\",\n        fill: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        d: \"M11.47 3.841a.75.75 0 0 1 1.06 0l8.69 8.69a.75.75 0 1 0 1.06-1.061l-8.689-8.69a2.25 2.25 0 0 0-3.182 0l-8.69 8.69a.75.75 0 1 0 1.061 1.06l8.69-8.689Z\"\n    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        d: \"m12 5.432 8.159 8.159c.03.03.06.058.091.086v6.198c0 1.035-.84 1.875-1.875 1.875H15a.75.75 0 0 1-.75-.75v-4.5a.75.75 0 0 0-.75-.75h-3a.75.75 0 0 0-.75.75V21a.75.75 0 0 1-.75.75H5.625a1.875 1.875 0 0 1-1.875-1.875v-6.198a2.29 2.29 0 0 0 .091-.086L12 5.432Z\"\n    }));\n}\n_c = HomeIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(HomeIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"HomeIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/HomeIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/KeyIcon.js":
/*!***************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/solid/esm/KeyIcon.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction KeyIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        viewBox: \"0 0 24 24\",\n        fill: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        fillRule: \"evenodd\",\n        d: \"M15.75 1.5a6.75 6.75 0 0 0-6.651 7.906c.067.39-.032.717-.221.906l-6.5 6.499a3 3 0 0 0-.878 2.121v2.818c0 .414.336.75.75.75H6a.75.75 0 0 0 .75-.75v-1.5h1.5A.75.75 0 0 0 9 19.5V18h1.5a.75.75 0 0 0 .53-.22l2.658-2.658c.19-.189.517-.288.906-.22A6.75 6.75 0 1 0 15.75 1.5Zm0 3a.75.75 0 0 0 0 1.5A2.25 2.25 0 0 1 18 8.25a.75.75 0 0 0 1.5 0 3.75 3.75 0 0 0-3.75-3.75Z\",\n        clipRule: \"evenodd\"\n    }));\n}\n_c = KeyIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(KeyIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"KeyIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/KeyIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/MapIcon.js":
/*!***************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/solid/esm/MapIcon.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction MapIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        viewBox: \"0 0 24 24\",\n        fill: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        fillRule: \"evenodd\",\n        d: \"M8.161 2.58a1.875 1.875 0 0 1 1.678 0l4.993 2.498c.**************.336 0l3.869-1.935A1.875 1.875 0 0 1 21.75 4.82v12.485c0 .71-.401 1.36-1.037 1.677l-4.875 2.437a1.875 1.875 0 0 1-1.676 0l-4.994-2.497a.375.375 0 0 0-.336 0l-3.868 1.935A1.875 1.875 0 0 1 2.25 19.18V6.695c0-.71.401-1.36 1.036-1.677l4.875-2.437ZM9 6a.75.75 0 0 1 .75.75V15a.75.75 0 0 1-1.5 0V6.75A.75.75 0 0 1 9 6Zm6.75 3a.75.75 0 0 0-1.5 0v8.25a.75.75 0 0 0 1.5 0V9Z\",\n        clipRule: \"evenodd\"\n    }));\n}\n_c = MapIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(MapIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"MapIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/MapIcon.js\n"));

/***/ })

});