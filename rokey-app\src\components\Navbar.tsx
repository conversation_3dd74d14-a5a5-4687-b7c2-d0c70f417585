'use client';

import Link from 'next/link';
import { UserCircleIcon, BellIcon, Cog6ToothIcon, Bars3Icon } from '@heroicons/react/24/outline';
import { useSidebar } from '@/contexts/SidebarContext';
import { useBreadcrumb } from '@/hooks/useBreadcrumb';

export default function Navbar() {
  const { toggleSidebar } = useSidebar();
  const { breadcrumb } = useBreadcrumb();
  return (
    <nav className="header border-b border-border-primary bg-background-secondary/95 backdrop-blur-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Left side - Mobile menu button and breadcrumb */}
          <div className="flex items-center space-x-4">
            {/* Mobile menu button - visible on mobile only */}
            <button
              onClick={toggleSidebar}
              className="lg:hidden p-2 rounded-lg hover:bg-background-hover transition-colors duration-200"
              title="Toggle sidebar"
            >
              <Bars3Icon className="h-6 w-6 text-text-primary" />
            </button>

            {/* Mobile logo - visible on mobile only */}
            <div className="lg:hidden">
              <h1 className="text-xl font-bold text-gradient">RouKey</h1>
            </div>

            {/* Dynamic Breadcrumb - hidden on mobile */}
            <div className="hidden lg:flex items-center space-x-2 text-sm text-text-secondary">
              <span>{breadcrumb.title}</span>
              <span>/</span>
              <span className="text-text-primary font-medium">{breadcrumb.subtitle}</span>
            </div>
          </div>

          {/* Right side - responsive */}
          <div className="flex items-center space-x-2 sm:space-x-4">
            {/* Search - Hidden on mobile, visible on larger screens */}
            <div className="hidden xl:block">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search..."
                  className="w-64 pl-10 pr-4 py-2.5 text-sm bg-background-tertiary border border-border-secondary rounded-lg text-text-primary placeholder-text-muted focus:outline-none focus:ring-2 focus:ring-primary-500/20 focus:border-primary-500 transition-all duration-200"
                />
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-4 w-4 text-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              </div>
            </div>

            {/* Notifications - Always visible */}
            <button className="p-2 rounded-lg hover:bg-background-hover transition-colors duration-200 relative">
              <BellIcon className="h-5 w-5 text-text-primary" />
              <span className="absolute -top-1 -right-1 h-3 w-3 bg-primary-500 rounded-full animate-pulse"></span>
            </button>

            {/* Settings - Hidden on mobile */}
            <button className="hidden sm:block p-2 rounded-lg hover:bg-background-hover transition-colors duration-200">
              <Cog6ToothIcon className="h-5 w-5 text-text-primary" />
            </button>

            {/* User Profile - Responsive */}
            <div className="flex items-center space-x-3 px-2 sm:px-3 py-2 rounded-lg hover:bg-background-hover transition-colors duration-200 cursor-pointer">
              <div className="w-8 h-8 rounded-full bg-gradient-to-br from-primary-400 to-primary-600 flex items-center justify-center shadow-glow">
                <span className="text-dark-950 font-semibold text-sm">DU</span>
              </div>
              <div className="hidden md:block">
                <p className="text-sm font-medium text-text-primary">Demo User</p>
                <p className="text-xs text-text-secondary">Pro Plan</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
}