'use client';

import Link from 'next/link';
import { UserCircleIcon, BellIcon, Cog6ToothIcon, Bars3Icon } from '@heroicons/react/24/outline';
import { useSidebar } from '@/contexts/SidebarContext';
import { useBreadcrumb } from '@/hooks/useBreadcrumb';
import ThemeToggle from '@/components/ui/ThemeToggle';

export default function Navbar() {
  const { toggleSidebar } = useSidebar();
  const { breadcrumb } = useBreadcrumb();
  return (
    <nav className="header border-b border-gray-200 dark:border-gray-700 bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Left side - Mobile menu button and breadcrumb */}
          <div className="flex items-center space-x-4">
            {/* Mobile menu button - visible on mobile only */}
            <button
              onClick={toggleSidebar}
              className="lg:hidden p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200"
              title="Toggle sidebar"
            >
              <Bars3Icon className="h-6 w-6 text-gray-600 dark:text-gray-300" />
            </button>

            {/* Mobile logo - visible on mobile only */}
            <div className="lg:hidden">
              <h1 className="text-xl font-bold text-gray-900 dark:text-white neon-text-blue">RoKey</h1>
            </div>

            {/* Dynamic Breadcrumb - hidden on mobile */}
            <div className="hidden lg:flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300">
              <span>{breadcrumb.title}</span>
              <span>/</span>
              <span className="text-gray-900 dark:text-white font-medium">{breadcrumb.subtitle}</span>
            </div>
          </div>

          {/* Right side - responsive */}
          <div className="flex items-center space-x-2 sm:space-x-4">
            {/* Search - Hidden on mobile, visible on larger screens */}
            <div className="hidden xl:block">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search..."
                  className="w-64 pl-10 pr-4 py-2.5 text-sm bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500/20 dark:focus:ring-neon-blue/20 focus:border-orange-500 dark:focus:border-neon-blue transition-all duration-200"
                />
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-4 w-4 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              </div>
            </div>

            {/* Notifications - Always visible */}
            <button className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200 relative neon-glow-hover">
              <BellIcon className="h-5 w-5 text-gray-600 dark:text-gray-300" />
              <span className="absolute -top-1 -right-1 h-3 w-3 bg-orange-500 dark:bg-neon-blue rounded-full animate-pulse"></span>
            </button>

            {/* Theme Toggle - Always visible */}
            <ThemeToggle size="md" className="mx-1" />

            {/* Settings - Hidden on mobile */}
            <button className="hidden sm:block p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200">
              <Cog6ToothIcon className="h-5 w-5 text-gray-600 dark:text-gray-300" />
            </button>

            {/* User Profile - Responsive */}
            <div className="flex items-center space-x-3 px-2 sm:px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200 cursor-pointer neon-glow-hover">
              <div className="w-8 h-8 rounded-full bg-gradient-to-br from-orange-400 to-orange-500 dark:from-neon-blue dark:to-neon-purple flex items-center justify-center shadow-lg dark:shadow-neon-blue">
                <span className="text-white font-semibold text-sm">DU</span>
              </div>
              <div className="hidden md:block">
                <p className="text-sm font-medium text-gray-900 dark:text-white">Demo User</p>
                <p className="text-xs text-gray-500 dark:text-gray-400">Free Plan</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
}