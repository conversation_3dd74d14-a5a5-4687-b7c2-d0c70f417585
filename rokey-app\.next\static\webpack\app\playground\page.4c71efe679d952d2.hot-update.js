"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/OrchestrationChatroom.tsx":
/*!**************************************************!*\
  !*** ./src/components/OrchestrationChatroom.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OrchestrationChatroom: function() { return /* binding */ OrchestrationChatroom; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ChatMessage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChatMessage */ \"(app-pages-browser)/./src/components/ChatMessage.tsx\");\n/* harmony import */ var _TypingIndicator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TypingIndicator */ \"(app-pages-browser)/./src/components/TypingIndicator.tsx\");\n/* __next_internal_client_entry_do_not_use__ OrchestrationChatroom auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst OrchestrationChatroom = (param)=>{\n    let { executionId, events, isConnected, error, isComplete } = param;\n    _s();\n    const [chatMessages, setChatMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [typingSpecialists, setTypingSpecialists] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Scroll to bottom when new messages arrive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    }, [\n        chatMessages\n    ]);\n    // Convert orchestration events to chat messages\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const newMessages = [];\n        const currentlyTyping = new Set();\n        events.forEach((event, index)=>{\n            const timestamp = new Date(event.timestamp || Date.now());\n            const messageId = \"\".concat(executionId, \"-\").concat(index);\n            switch(event.type){\n                case \"orchestration_started\":\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        content: \"\\uD83C\\uDFAC Welcome to the AI Team Collaboration! I'm assembling the perfect team for this task.\",\n                        timestamp,\n                        type: \"message\"\n                    });\n                    break;\n                case \"task_decomposed\":\n                    var _event_data;\n                    const steps = ((_event_data = event.data) === null || _event_data === void 0 ? void 0 : _event_data.steps) || [];\n                    const teamIntro = steps.map((step)=>\"\\uD83E\\uDD16 @\".concat(step.roleId, \" - \").concat(step.modelName || \"AI Specialist\")).join(\"\\n\");\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        content: \"\\uD83D\\uDCCB I've analyzed the task and assembled this expert team:\\n\\n\".concat(teamIntro, \"\\n\\nLet's begin the collaboration!\"),\n                        timestamp,\n                        type: \"assignment\"\n                    });\n                    break;\n                case \"step_assigned\":\n                    var _event_data1;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        roleId: event.role_id,\n                        content: \"\\uD83C\\uDFAF @\".concat(event.role_id, \", you're up! \").concat(((_event_data1 = event.data) === null || _event_data1 === void 0 ? void 0 : _event_data1.commentary) || \"Please begin your specialized work on this task.\"),\n                        timestamp,\n                        type: \"assignment\"\n                    });\n                    break;\n                case \"moderator_assignment\":\n                    var _event_data2;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        roleId: event.role_id,\n                        content: ((_event_data2 = event.data) === null || _event_data2 === void 0 ? void 0 : _event_data2.message) || \"\\uD83C\\uDFAF @\".concat(event.role_id, \", you're up! Please begin your specialized work on this task.\"),\n                        timestamp,\n                        type: \"assignment\"\n                    });\n                    break;\n                case \"specialist_acknowledgment\":\n                    var _event_data3;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"specialist\",\n                        senderName: event.role_id || \"Specialist\",\n                        roleId: event.role_id,\n                        content: ((_event_data3 = event.data) === null || _event_data3 === void 0 ? void 0 : _event_data3.message) || \"✅ Understood! I'm \".concat(event.role_id, \" and I'll handle this task with expertise. Starting work now...\"),\n                        timestamp,\n                        type: \"message\"\n                    });\n                    break;\n                case \"step_started\":\n                    // Add to typing indicators\n                    if (event.role_id) {\n                        currentlyTyping.add(event.role_id);\n                    }\n                    break;\n                case \"step_progress\":\n                    if (event.role_id) {\n                        currentlyTyping.add(event.role_id);\n                    }\n                    break;\n                case \"specialist_message\":\n                    var _event_data4, _event_data5;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"specialist\",\n                        senderName: event.role_id || \"Specialist\",\n                        roleId: event.role_id,\n                        content: \"\".concat(((_event_data4 = event.data) === null || _event_data4 === void 0 ? void 0 : _event_data4.message) || \"\\uD83C\\uDF89 Perfect! I've completed my part of the task. Here's what I've delivered:\", \"\\n\\n\").concat(((_event_data5 = event.data) === null || _event_data5 === void 0 ? void 0 : _event_data5.output) || \"Task completed successfully!\"),\n                        timestamp,\n                        type: \"completion\"\n                    });\n                    break;\n                case \"step_completed\":\n                    // Remove from typing\n                    if (event.role_id) {\n                        currentlyTyping.delete(event.role_id);\n                    }\n                    break;\n                case \"handoff_message\":\n                    var _event_data6, _event_data7, _event_data8;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        content: ((_event_data6 = event.data) === null || _event_data6 === void 0 ? void 0 : _event_data6.message) || \"✨ Excellent work, @\".concat((_event_data7 = event.data) === null || _event_data7 === void 0 ? void 0 : _event_data7.fromRole, \"! Quality looks great. Now passing to @\").concat((_event_data8 = event.data) === null || _event_data8 === void 0 ? void 0 : _event_data8.toRole, \"...\"),\n                        timestamp,\n                        type: \"handoff\"\n                    });\n                    break;\n                case \"synthesis_started\":\n                    var _event_data9;\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        content: ((_event_data9 = event.data) === null || _event_data9 === void 0 ? void 0 : _event_data9.message) || \"\\uD83E\\uDDE9 Fantastic teamwork everyone! Now I'll synthesize all your excellent contributions into the final deliverable...\",\n                        timestamp,\n                        type: \"message\"\n                    });\n                    currentlyTyping.add(\"moderator\");\n                    break;\n                case \"synthesis_complete\":\n                    var _event_data10;\n                    currentlyTyping.delete(\"moderator\");\n                    newMessages.push({\n                        id: messageId,\n                        sender: \"moderator\",\n                        senderName: \"Moderator\",\n                        content: ((_event_data10 = event.data) === null || _event_data10 === void 0 ? void 0 : _event_data10.message) || \"\\uD83C\\uDF8A Mission accomplished! The team has delivered an outstanding result. Great collaboration everyone!\",\n                        timestamp,\n                        type: \"completion\"\n                    });\n                    break;\n            }\n        });\n        setChatMessages(newMessages);\n        setTypingSpecialists(currentlyTyping);\n    }, [\n        events,\n        executionId\n    ]);\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 flex items-center justify-center p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-6 h-6 text-red-600\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                        children: \"Connection Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                lineNumber: 198,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n            lineNumber: 197,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-2 text-xs font-medium backdrop-blur-sm \".concat(isConnected ? \"bg-green-50/80 text-green-700 border-b border-green-100/60\" : \"bg-amber-50/80 text-amber-700 border-b border-amber-100/60\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2 h-2 rounded-full \".concat(isConnected ? \"bg-green-500 shadow-sm\" : \"bg-amber-500 animate-pulse\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: isConnected ? \"Connected to AI Team\" : \"Connecting...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, undefined),\n                        isComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-auto flex items-center space-x-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SparklesIcon, {\n                                    className: \"w-3 h-3 text-green-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-green-600 font-medium\",\n                                    children: \"Complete\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-4 space-y-4\",\n                children: [\n                    chatMessages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-4 shadow-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-8 h-8 text-blue-500 animate-pulse\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 font-medium\",\n                                children: \"Waiting for AI team to start collaboration...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm mt-1\",\n                                children: \"The specialists are preparing to work together\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 11\n                    }, undefined),\n                    chatMessages.map((message, index)=>{\n                        const isPhaseStart = message.type === \"assignment\" || message.type === \"message\" && message.content.includes(\"\\uD83C\\uDFAC\");\n                        const isPhaseEnd = message.type === \"completion\";\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                isPhaseStart && index > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center my-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 h-px bg-gradient-to-r from-transparent via-gray-300/60 to-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-3 py-1 text-xs text-gray-500 bg-white/80 backdrop-blur-sm rounded-full border border-gray-200/60 shadow-sm\",\n                                            children: \"New Phase\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 h-px bg-gradient-to-r from-transparent via-gray-300/60 to-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatMessage__WEBPACK_IMPORTED_MODULE_2__.ChatMessage, {\n                                    message: message\n                                }, message.id, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, undefined),\n                                isPhaseEnd && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center my-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-green-50/80 to-emerald-50/80 border border-green-200/60 rounded-full px-4 py-2 shadow-sm backdrop-blur-sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-green-700 font-medium flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SparklesIcon, {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Phase Complete\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, message.id, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 13\n                        }, undefined);\n                    }),\n                    Array.from(typingSpecialists).map((specialist)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TypingIndicator__WEBPACK_IMPORTED_MODULE_3__.TypingIndicator, {\n                                senderName: specialist,\n                                roleId: specialist !== \"moderator\" ? specialist : undefined\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 13\n                            }, undefined)\n                        }, specialist, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 11\n                        }, undefined)),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                lineNumber: 234,\n                columnNumber: 7\n            }, undefined),\n            isComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-gradient-to-r from-green-50/80 to-emerald-50/80 border-t border-green-200/60 backdrop-blur-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6 h-6 bg-green-500 rounded-full flex items-center justify-center shadow-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 text-white\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M5 13l4 4L19 7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-green-800 font-medium\",\n                                            children: \"AI Team Collaboration Complete!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-green-600\",\n                                            children: \"All specialists have finished their work\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"text-xs text-green-700 hover:text-green-800 bg-white/60 hover:bg-white/80 px-3 py-1.5 rounded-full border border-green-200/60 transition-all duration-200 shadow-sm\",\n                            children: \"Export Results\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                    lineNumber: 302,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                lineNumber: 301,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n        lineNumber: 212,\n        columnNumber: 5\n    }, undefined);\n};\n_s(OrchestrationChatroom, \"Trlsk+ImATjahibYSefjX0C5OX4=\");\n_c = OrchestrationChatroom;\nvar _c;\n$RefreshReg$(_c, \"OrchestrationChatroom\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OrchestrationChatroom.tsx\n"));

/***/ })

});