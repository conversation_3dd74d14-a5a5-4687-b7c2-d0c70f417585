'use client';

import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';
import {
  BoltIcon,
  CodeBracketIcon,
  PencilIcon,
  ChartBarIcon,
  CpuChipIcon,
  ArrowRightIcon,
  ShieldCheckIcon,
  EyeIcon,
  CogIcon,
  CloudIcon
} from '@heroicons/react/24/outline';

// AI Provider logos/icons (simplified representations)
const aiProviders = [
  { name: 'OpenAI', color: 'bg-green-500', icon: '🤖' },
  { name: 'Anthropic', color: 'bg-orange-500', icon: '🧠' },
  { name: 'Google', color: 'bg-blue-500', icon: '🔍' },
  { name: 'Meta', color: 'bg-purple-500', icon: '📘' },
  { name: 'Mistral', color: 'bg-red-500', icon: '🌪️' },
  { name: 'Cohere', color: 'bg-teal-500', icon: '💬' }
];

const gatewayFeatures = [
  { name: 'OBSERVABILITY', color: 'bg-orange-500' },
  { name: 'GOVERNANCE', color: 'bg-purple-500' },
  { name: 'ROUTING', color: 'bg-green-500' },
  { name: 'PROMPT MANAGEMENT', color: 'bg-blue-500' },
  { name: 'GUARDRAILS', color: 'bg-red-500' },
  { name: 'FINOPS', color: 'bg-yellow-500' }
];

const routingExamples = [
  {
    id: 1,
    prompt: "Solve this complex math problem",
    role: "Logic & Reasoning",
    model: "GPT-4",
    icon: BoltIcon,
    color: "text-blue-600"
  },
  {
    id: 2,
    prompt: "Write creative content",
    role: "Creative Writing",
    model: "Claude 4",
    icon: PencilIcon,
    color: "text-purple-600"
  },
  {
    id: 3,
    prompt: "Build a React component",
    role: "Code Generation",
    model: "DeepSeek Coder",
    icon: CodeBracketIcon,
    color: "text-green-600"
  }
];

export default function RoutingVisualization() {
  const [activeExample, setActiveExample] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const [animatingConnections, setAnimatingConnections] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      setIsAnimating(true);
      setAnimatingConnections(true);
      setTimeout(() => {
        setActiveExample((prev) => (prev + 1) % routingExamples.length);
        setIsAnimating(false);
      }, 800);
      setTimeout(() => {
        setAnimatingConnections(false);
      }, 1500);
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const currentExample = routingExamples[activeExample];

  return (
    <section className="py-24 bg-gradient-to-br from-slate-900 via-gray-900 to-slate-800 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.02"%3E%3Ccircle cx="30" cy="30" r="1"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-40"></div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Section Header */}
        <div className="text-center mb-20">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-4xl sm:text-5xl font-bold text-white mb-6"
          >
            Introducing the AI Gateway Pattern
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.1 }}
            className="text-xl text-gray-300 max-w-3xl mx-auto"
          >
            Watch how RouKey intelligently routes your requests through our unified gateway to the perfect AI model
          </motion.p>
        </div>

        {/* AI Gateway Visualization */}
        <div className="relative">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12 items-center">

            {/* Your AI APP */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              className="relative"
            >
              <div className="bg-slate-800 border border-slate-700 rounded-2xl p-8 text-center">
                <div className="text-gray-400 text-sm font-medium mb-4">Your AI APP</div>
                <div className="w-24 h-24 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl mx-auto mb-6 flex items-center justify-center">
                  <div className="text-3xl">⚡</div>
                </div>
                <div className="space-y-2 text-left">
                  <div className="text-gray-300 text-sm">Current Request:</div>
                  <div className="bg-slate-700 rounded-lg p-3 text-sm text-white">
                    "{currentExample.prompt}"
                  </div>
                  <div className="flex items-center text-xs text-gray-400">
                    <div className="w-2 h-2 bg-[#ff6b35] rounded-full mr-2 animate-pulse"></div>
                    Analyzing...
                  </div>
                </div>
              </div>

              {/* Animated Connection Lines */}
              <div className="hidden lg:block absolute top-1/2 -right-6 transform -translate-y-1/2">
                <motion.div
                  animate={{
                    opacity: animatingConnections ? [0.3, 1, 0.3] : 0.6,
                    scale: animatingConnections ? [1, 1.1, 1] : 1
                  }}
                  transition={{ duration: 1, repeat: animatingConnections ? Infinity : 0 }}
                  className="w-12 h-0.5 bg-gradient-to-r from-[#ff6b35] to-transparent"
                />
              </div>
            </motion.div>

            {/* RouKey AI Gateway */}
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.2 }}
              className="relative"
            >
              <div className="bg-slate-800 border border-slate-700 rounded-2xl p-8 text-center">
                <div className="text-gray-400 text-sm font-medium mb-4">RouKey AI Gateway</div>

                {/* Gateway Features Grid */}
                <div className="grid grid-cols-2 gap-3 mb-6">
                  {gatewayFeatures.map((feature, index) => (
                    <motion.div
                      key={feature.name}
                      initial={{ opacity: 0, scale: 0.8 }}
                      whileInView={{ opacity: 1, scale: 1 }}
                      viewport={{ once: true }}
                      transition={{ delay: 0.3 + index * 0.1 }}
                      className={`${feature.color} rounded-lg p-3 text-white text-xs font-medium`}
                    >
                      {feature.name}
                    </motion.div>
                  ))}
                </div>

                {/* Central Processing Indicator */}
                <div className="relative">
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                    className="w-16 h-16 border-4 border-[#ff6b35] border-t-transparent rounded-full mx-auto mb-4"
                  />
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-8 h-8 bg-[#ff6b35] rounded-full flex items-center justify-center">
                      <CogIcon className="w-4 h-4 text-white" />
                    </div>
                  </div>
                </div>

                <div className="text-xs text-gray-400">
                  <div>Custom Model</div>
                  <div>Fine-Tune</div>
                </div>
              </div>

              {/* Connection Lines to Providers */}
              <div className="hidden lg:block absolute top-1/2 -right-6 transform -translate-y-1/2">
                <motion.div
                  animate={{
                    opacity: animatingConnections ? [0.3, 1, 0.3] : 0.6,
                    scale: animatingConnections ? [1, 1.1, 1] : 1
                  }}
                  transition={{ duration: 1, delay: 0.2, repeat: animatingConnections ? Infinity : 0 }}
                  className="w-12 h-0.5 bg-gradient-to-r from-[#ff6b35] to-transparent"
                />
              </div>
            </motion.div>

            {/* AI Providers */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.4 }}
              className="relative"
            >
              <div className="bg-slate-800 border border-slate-700 rounded-2xl p-8 text-center">
                <div className="text-gray-400 text-sm font-medium mb-6">AI Providers</div>

                {/* Provider Grid */}
                <div className="grid grid-cols-3 gap-4 mb-6">
                  {aiProviders.map((provider, index) => (
                    <motion.div
                      key={provider.name}
                      initial={{ opacity: 0, scale: 0.8 }}
                      whileInView={{ opacity: 1, scale: 1 }}
                      viewport={{ once: true }}
                      transition={{ delay: 0.5 + index * 0.1 }}
                      className={`${provider.color} w-12 h-12 rounded-xl flex items-center justify-center text-white text-lg hover:scale-110 transition-transform cursor-pointer`}
                      whileHover={{ scale: 1.1 }}
                    >
                      {provider.icon}
                    </motion.div>
                  ))}
                </div>

                {/* Selected Model Highlight */}
                <motion.div
                  key={`selected-${activeExample}`}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5 }}
                  className="bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-lg p-4 text-white"
                >
                  <currentExample.icon className="w-6 h-6 mx-auto mb-2" />
                  <div className="text-sm font-medium">{currentExample.model}</div>
                  <div className="text-xs opacity-90">Optimal for {currentExample.role}</div>
                </motion.div>
              </div>
            </motion.div>
          </div>

          {/* Example Selector */}
          <div className="flex justify-center mt-16 space-x-3">
            {routingExamples.map((example, index) => (
              <button
                key={example.id}
                onClick={() => setActiveExample(index)}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                  index === activeExample
                    ? 'bg-[#ff6b35] text-white shadow-lg'
                    : 'bg-slate-700 text-gray-300 hover:bg-slate-600'
                }`}
              >
                {example.role}
              </button>
            ))}
          </div>
        </div>

        {/* Benefits */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-4 gap-8 mt-24"
        >
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
              <EyeIcon className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-xl font-semibold text-white mb-3">Full Observability</h3>
            <p className="text-gray-300">Complete visibility into every request, response, and routing decision</p>
          </div>

          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
              <BoltIcon className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-xl font-semibold text-white mb-3">Intelligent Routing</h3>
            <p className="text-gray-300">AI-powered routing decisions based on content, performance, and cost</p>
          </div>

          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
              <ShieldCheckIcon className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-xl font-semibold text-white mb-3">Enterprise Security</h3>
            <p className="text-gray-300">Built-in guardrails, compliance, and security controls</p>
          </div>

          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
              <CloudIcon className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-xl font-semibold text-white mb-3">Global Scale</h3>
            <p className="text-gray-300">Distributed infrastructure with 99.9% uptime and low latency</p>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
