'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';
import {
  BoltIcon,
  CodeBracketIcon,
  PencilIcon,
  ChartBarIcon,
  CpuChipIcon,
  ArrowRightIcon,
  ShieldCheckIcon,
  EyeIcon,
  CogIcon,
  CloudIcon,
  CheckIcon
} from '@heroicons/react/24/outline';

const routingExamples = [
  {
    id: 1,
    prompt: "Solve this complex math problem: 2x + 5 = 15",
    role: "logic_reasoning",
    roleName: "Logic & Reasoning",
    model: "GPT-4o",
    provider: "OpenAI",
    icon: BoltIcon,
    color: "text-blue-400",
    bgColor: "bg-blue-500/10",
    borderColor: "border-blue-500/20",
    glowColor: "shadow-blue-500/50"
  },
  {
    id: 2,
    prompt: "Write a blog post about AI trends",
    role: "writing",
    roleName: "Writing & Content Creation",
    model: "Claude 3.5 Sonnet",
    provider: "Anthropic",
    icon: PencilIcon,
    color: "text-purple-400",
    bgColor: "bg-purple-500/10",
    borderColor: "border-purple-500/20",
    glowColor: "shadow-purple-500/50"
  },
  {
    id: 3,
    prompt: "Build a React component with TypeScript",
    role: "coding_frontend",
    roleName: "Frontend Development",
    model: "DeepSeek Coder V2",
    provider: "DeepSeek",
    icon: CodeBracketIcon,
    color: "text-green-400",
    bgColor: "bg-green-500/10",
    borderColor: "border-green-500/20",
    glowColor: "shadow-green-500/50"
  },
  {
    id: 4,
    prompt: "Summarize this research paper",
    role: "research_synthesis",
    roleName: "Research & Analysis",
    model: "Gemini 2.0 Flash",
    provider: "Google",
    icon: ChartBarIcon,
    color: "text-cyan-400",
    bgColor: "bg-cyan-500/10",
    borderColor: "border-cyan-500/20",
    glowColor: "shadow-cyan-500/50"
  }
];

export default function RoutingVisualization() {
  const [activeExample, setActiveExample] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      setIsAnimating(true);
      setTimeout(() => {
        setActiveExample((prev) => (prev + 1) % routingExamples.length);
        setIsAnimating(false);
      }, 500);
    }, 5000); // Slower transition for better viewing

    return () => clearInterval(interval);
  }, []);

  const currentExample = routingExamples[activeExample];

  return (
    <section className="py-20 bg-gradient-to-br from-slate-900 via-gray-900 to-slate-800 relative overflow-hidden">
      {/* Custom CSS for flowing animations */}
      <style jsx>{`
        @keyframes flowRight {
          0% { transform: translateX(-100%); opacity: 0; }
          50% { opacity: 1; }
          100% { transform: translateX(100%); opacity: 0; }
        }
        @keyframes flowLeft {
          0% { transform: translateX(100%); opacity: 0; }
          50% { opacity: 1; }
          100% { transform: translateX(-100%); opacity: 0; }
        }
        .flow-right {
          animation: flowRight 2s ease-in-out infinite;
        }
        .flow-left {
          animation: flowLeft 2s ease-in-out infinite;
          animation-delay: 1s;
        }
        .glow-pulse {
          animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
      `}</style>

      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:60px_60px]" />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-4xl sm:text-5xl font-bold text-white mb-6"
          >
            Introducing the AI Gateway Pattern
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.1 }}
            className="text-xl text-gray-300 max-w-3xl mx-auto"
          >
            Watch how RouKey intelligently routes your requests through our unified gateway to the perfect AI model
          </motion.p>
        </div>

        {/* Intelligent Role Routing Visualization */}
        <div className="relative max-w-7xl mx-auto">
          {/* Subtle background glow */}
          <div className="absolute inset-0 bg-gradient-to-r from-[#ff6b35]/5 via-transparent to-green-500/5 rounded-3xl blur-3xl"></div>

          <div className="relative grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-16 items-start">

            {/* Column 1: User Prompt */}
            <motion.div
              key={`prompt-${activeExample}`}
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              className="relative"
            >
              <div className="text-center mb-6">
                <h3 className="text-xl font-semibold text-white mb-2">User Prompt</h3>
                <p className="text-sm text-gray-400">Your request enters RouKey</p>
              </div>

              <div className={`bg-slate-800 border-2 ${currentExample.borderColor} rounded-xl p-6 relative ${currentExample.bgColor} shadow-lg ${currentExample.glowColor}`}>
                {/* Animated border glow */}
                <div className="absolute inset-0 rounded-xl border-2 border-dashed border-gray-500 opacity-30 animate-pulse"></div>

                {/* Prompt content */}
                <div className="relative z-10">
                  <div className="text-sm text-gray-400 mb-3">Incoming Request</div>
                  <div className="text-white font-medium mb-4 text-lg leading-relaxed">
                    "{currentExample.prompt}"
                  </div>
                  <div className="flex items-center text-sm text-gray-400">
                    <div className={`w-2 h-2 bg-[#ff6b35] rounded-full mr-2 animate-pulse`}></div>
                    Analyzing prompt...
                  </div>
                </div>

                {/* Connection dots with glow */}
                <div className="absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2">
                  <div className="flex space-x-1">
                    <div className="w-1 h-8 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50"></div>
                    <div className="w-1 h-6 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50" style={{animationDelay: '0.2s'}}></div>
                    <div className="w-1 h-4 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50" style={{animationDelay: '0.4s'}}></div>
                  </div>
                </div>
              </div>

              {/* Animated connection line with flowing current */}
              <div className="absolute right-0 top-1/2 transform translate-x-full -translate-y-1/2 hidden lg:block">
                <div className="relative w-16 h-px">
                  <div className="absolute inset-0 bg-gradient-to-r from-[#ff6b35] to-transparent opacity-60"></div>
                  <div className="absolute top-0 left-0 w-4 h-px bg-gradient-to-r from-white to-[#ff6b35] flow-right shadow-lg shadow-orange-500/50"></div>
                  <div className="absolute top-0 left-0 w-2 h-px bg-white flow-right shadow-sm shadow-white/50" style={{animationDelay: '0.5s'}}></div>
                </div>
              </div>
            </motion.div>

            {/* Column 2: Intelligent Role Classification */}
            <motion.div
              key={`classification-${activeExample}`}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="relative"
            >
              <div className="text-center mb-6">
                <h3 className="text-xl font-semibold text-white mb-2">Intelligent Role Classification</h3>
                <p className="text-sm text-gray-400">AI analyzes & routes to optimal model</p>
              </div>

              <div className="bg-slate-800 border-2 border-slate-700 rounded-xl p-6 relative shadow-xl">
                {/* Animated scanning border */}
                <div className="absolute inset-0 rounded-xl border-2 border-dashed border-[#ff6b35] opacity-40 animate-pulse"></div>

                {/* Classification Process */}
                <div className="relative z-10 space-y-4">
                  {/* AI Brain Icon */}
                  <div className="text-center mb-6">
                    <div className="w-16 h-16 bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-lg shadow-orange-500/30">
                      <BoltIcon className="w-8 h-8 text-white" />
                    </div>
                    <div className="text-sm text-gray-400">Gemini 2.0 Flash Classifier</div>
                  </div>

                  {/* Role Detection */}
                  <div className={`${currentExample.bgColor} ${currentExample.borderColor} border-2 rounded-lg p-4 text-center shadow-lg ${currentExample.glowColor}`}>
                    {React.createElement(currentExample.icon, {
                      className: `h-6 w-6 ${currentExample.color} mx-auto mb-2`
                    })}
                    <div className="text-xs text-gray-400 mb-1">Classified as</div>
                    <div className={`font-semibold text-sm ${currentExample.color}`}>
                      {currentExample.roleName}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      Role ID: {currentExample.role}
                    </div>
                  </div>

                  {/* Processing Indicators */}
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div className="bg-slate-700 rounded p-2 text-center">
                      <div className="text-green-400 mb-1">✓ Context Analysis</div>
                      <div className="text-gray-500">Complete</div>
                    </div>
                    <div className="bg-slate-700 rounded p-2 text-center">
                      <div className="text-green-400 mb-1">✓ Role Matching</div>
                      <div className="text-gray-500">Complete</div>
                    </div>
                  </div>
                </div>

                {/* Connection dots with animated glow */}
                <div className="absolute left-0 top-1/2 transform -translate-x-1/2 -translate-y-1/2">
                  <div className="flex space-x-1">
                    <div className="w-1 h-4 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50" style={{animationDelay: '0.6s'}}></div>
                    <div className="w-1 h-6 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50" style={{animationDelay: '0.4s'}}></div>
                    <div className="w-1 h-8 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50" style={{animationDelay: '0.2s'}}></div>
                  </div>
                </div>
                <div className="absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2">
                  <div className="flex space-x-1">
                    <div className="w-1 h-8 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50"></div>
                    <div className="w-1 h-6 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50" style={{animationDelay: '0.2s'}}></div>
                    <div className="w-1 h-4 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50" style={{animationDelay: '0.4s'}}></div>
                  </div>
                </div>
              </div>

              {/* Animated connection lines with flowing current */}
              <div className="absolute left-0 top-1/2 transform -translate-x-full -translate-y-1/2 hidden lg:block">
                <div className="relative w-16 h-px">
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-[#ff6b35] to-transparent opacity-60"></div>
                  <div className="absolute top-0 right-0 w-4 h-px bg-gradient-to-l from-white to-[#ff6b35] flow-left shadow-lg shadow-orange-500/50"></div>
                  <div className="absolute top-0 right-0 w-2 h-px bg-white flow-left shadow-sm shadow-white/50" style={{animationDelay: '0.3s'}}></div>
                </div>
              </div>
              <div className="absolute right-0 top-1/2 transform translate-x-full -translate-y-1/2 hidden lg:block">
                <div className="relative w-16 h-px">
                  <div className="absolute inset-0 bg-gradient-to-r from-[#ff6b35] to-transparent opacity-60"></div>
                  <div className="absolute top-0 left-0 w-4 h-px bg-gradient-to-r from-white to-[#ff6b35] flow-right shadow-lg shadow-orange-500/50"></div>
                  <div className="absolute top-0 left-0 w-2 h-px bg-white flow-right shadow-sm shadow-white/50" style={{animationDelay: '0.7s'}}></div>
                </div>
              </div>
            </motion.div>

            {/* Column 3: Optimal Model Selection */}
            <motion.div
              key={`model-${activeExample}`}
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="relative"
            >
              <div className="text-center mb-6">
                <h3 className="text-xl font-semibold text-white mb-2">Optimal Model Selection</h3>
                <p className="text-sm text-gray-400">Routed to the perfect model</p>
              </div>

              <div className="bg-gradient-to-br from-slate-800 to-slate-900 border-2 border-green-500/30 rounded-xl p-6 relative shadow-xl shadow-green-500/20">
                {/* Success glow border */}
                <div className="absolute inset-0 rounded-xl border-2 border-dashed border-green-400 opacity-50 animate-pulse"></div>

                <div className="relative z-10">
                  {/* Selected Model */}
                  <div className="text-center mb-6">
                    <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-lg shadow-green-500/30">
                      <CpuChipIcon className="w-8 h-8 text-white" />
                    </div>
                    <div className="text-lg font-bold text-white mb-1">{currentExample.model}</div>
                    <div className="text-sm text-green-400 font-medium">{currentExample.provider}</div>
                  </div>

                  {/* Role Assignment */}
                  <div className={`${currentExample.bgColor} ${currentExample.borderColor} border-2 rounded-lg p-4 text-center mb-4 shadow-lg ${currentExample.glowColor}`}>
                    <div className="text-xs text-gray-400 mb-1">Assigned Role</div>
                    <div className={`font-semibold text-sm ${currentExample.color}`}>
                      {currentExample.roleName}
                    </div>
                  </div>

                  {/* Performance Metrics */}
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div className="bg-slate-700 rounded p-2 text-center">
                      <div className="text-green-400 mb-1">⚡ Speed</div>
                      <div className="text-white font-medium">Optimal</div>
                    </div>
                    <div className="bg-slate-700 rounded p-2 text-center">
                      <div className="text-blue-400 mb-1">🎯 Accuracy</div>
                      <div className="text-white font-medium">High</div>
                    </div>
                  </div>

                  {/* Success indicator */}
                  <div className="mt-4 flex items-center justify-center text-green-400">
                    <CheckIcon className="w-4 h-4 mr-2" />
                    <span className="text-sm font-medium">Route Established</span>
                  </div>
                </div>

                {/* Connection dots with success glow */}
                <div className="absolute left-0 top-1/2 transform -translate-x-1/2 -translate-y-1/2">
                  <div className="flex space-x-1">
                    <div className="w-1 h-4 bg-green-400 rounded-full animate-pulse shadow-lg shadow-green-500/50" style={{animationDelay: '0.8s'}}></div>
                    <div className="w-1 h-6 bg-green-400 rounded-full animate-pulse shadow-lg shadow-green-500/50" style={{animationDelay: '0.6s'}}></div>
                    <div className="w-1 h-8 bg-green-400 rounded-full animate-pulse shadow-lg shadow-green-500/50" style={{animationDelay: '0.4s'}}></div>
                  </div>
                </div>
              </div>

              {/* Final connection line with success flow */}
              <div className="absolute left-0 top-1/2 transform -translate-x-full -translate-y-1/2 hidden lg:block">
                <div className="relative w-16 h-px">
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-green-400 to-green-400 opacity-60"></div>
                  <div className="absolute top-0 right-0 w-4 h-px bg-gradient-to-l from-white to-green-400 flow-left shadow-lg shadow-green-500/50"></div>
                  <div className="absolute top-0 right-0 w-2 h-px bg-white flow-left shadow-sm shadow-white/50" style={{animationDelay: '1s'}}></div>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Example Selector */}
          <div className="flex justify-center mt-12 space-x-2">
            {routingExamples.map((example, index) => (
              <button
                key={example.id}
                onClick={() => setActiveExample(index)}
                className={`w-3 h-3 rounded-full transition-all duration-200 ${
                  index === activeExample
                    ? 'bg-[#ff6b35] scale-125'
                    : 'bg-gray-500 hover:bg-gray-400'
                }`}
              />
            ))}
          </div>
        </div>

        {/* RouKey's Intelligent Routing Benefits */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-4 gap-8 mt-20"
        >
          <div className="text-center">
            <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center mx-auto mb-4 shadow-lg shadow-blue-500/20">
              <BoltIcon className="h-6 w-6 text-blue-400" />
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">Role-Based Classification</h3>
            <p className="text-gray-400">Gemini 2.0 Flash analyzes context and classifies requests into 15+ specialized roles</p>
          </div>

          <div className="text-center">
            <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center mx-auto mb-4 shadow-lg shadow-green-500/20">
              <CpuChipIcon className="h-6 w-6 text-green-400" />
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">Smart Model Matching</h3>
            <p className="text-gray-400">Each role routes to your pre-configured optimal model for maximum performance</p>
          </div>

          <div className="text-center">
            <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center mx-auto mb-4 shadow-lg shadow-purple-500/20">
              <ChartBarIcon className="h-6 w-6 text-purple-400" />
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">Contextual Continuity</h3>
            <p className="text-gray-400">Maintains conversation context and role consistency across multi-turn interactions</p>
          </div>

          <div className="text-center">
            <div className="w-12 h-12 bg-orange-500/20 rounded-lg flex items-center justify-center mx-auto mb-4 shadow-lg shadow-orange-500/20">
              <ShieldCheckIcon className="h-6 w-6 text-orange-400" />
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">Fallback Protection</h3>
            <p className="text-gray-400">Automatic fallback to default general chat model when no role match is found</p>
          </div>
        </motion.div>

        {/* Role Examples */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.2 }}
          className="mt-16 text-center"
        >
          <h4 className="text-xl font-semibold text-white mb-8">Supported Role Types</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 max-w-4xl mx-auto">
            {[
              { name: 'General Chat', color: 'text-gray-400' },
              { name: 'Logic & Reasoning', color: 'text-blue-400' },
              { name: 'Writing & Content', color: 'text-purple-400' },
              { name: 'Frontend Coding', color: 'text-green-400' },
              { name: 'Backend Coding', color: 'text-emerald-400' },
              { name: 'Research & Analysis', color: 'text-cyan-400' },
              { name: 'Summarization', color: 'text-yellow-400' },
              { name: 'Translation', color: 'text-pink-400' },
              { name: 'Data Extraction', color: 'text-indigo-400' },
              { name: 'Brainstorming', color: 'text-orange-400' },
              { name: 'Education', color: 'text-red-400' },
              { name: 'Audio Transcription', color: 'text-teal-400' }
            ].map((role, index) => (
              <div key={role.name} className="bg-slate-800 rounded-lg p-3 border border-slate-700">
                <div className={`text-sm font-medium ${role.color}`}>{role.name}</div>
              </div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
}
