'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';
import {
  BoltIcon,
  CodeBracketIcon,
  PencilIcon,
  ChartBarIcon,
  CpuChipIcon,
  ArrowRightIcon,
  ShieldCheckIcon,
  EyeIcon,
  CogIcon,
  CloudIcon
} from '@heroicons/react/24/outline';

const routingExamples = [
  {
    id: 1,
    prompt: "Solve this complex math problem",
    role: "Logic & Reasoning",
    model: "GPT-4",
    icon: BoltIcon,
    color: "text-blue-600",
    bgColor: "bg-blue-50",
    borderColor: "border-blue-200"
  },
  {
    id: 2,
    prompt: "Write creative content",
    role: "Creative Writing",
    model: "Claude 4",
    icon: PencilIcon,
    color: "text-purple-600",
    bgColor: "bg-purple-50",
    borderColor: "border-purple-200"
  },
  {
    id: 3,
    prompt: "Build a React component",
    role: "Code Generation",
    model: "DeepSeek Coder",
    icon: CodeBracketIcon,
    color: "text-green-600",
    bgColor: "bg-green-50",
    borderColor: "border-green-200"
  }
];

export default function RoutingVisualization() {
  const [activeExample, setActiveExample] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      setIsAnimating(true);
      setTimeout(() => {
        setActiveExample((prev) => (prev + 1) % routingExamples.length);
        setIsAnimating(false);
      }, 500);
    }, 4000);

    return () => clearInterval(interval);
  }, []);

  const currentExample = routingExamples[activeExample];

  return (
    <section className="py-20 bg-gradient-to-br from-slate-900 via-gray-900 to-slate-800 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:60px_60px]" />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-4xl sm:text-5xl font-bold text-white mb-6"
          >
            Introducing the AI Gateway Pattern
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.1 }}
            className="text-xl text-gray-300 max-w-3xl mx-auto"
          >
            Watch how RouKey intelligently routes your requests through our unified gateway to the perfect AI model
          </motion.p>
        </div>

        {/* Flow Diagram */}
        <div className="relative">
          <div className="grid grid-cols-1 lg:grid-cols-5 gap-8 items-center">
            {/* Input */}
            <motion.div
              key={`input-${activeExample}`}
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              className="lg:col-span-2"
            >
              <div className="bg-slate-800 border border-slate-700 rounded-xl p-6">
                <div className="text-sm text-gray-400 mb-2">Your Prompt</div>
                <div className="text-lg font-medium text-white mb-4">
                  "{currentExample.prompt}"
                </div>
                <div className="flex items-center text-sm text-gray-400">
                  <div className="w-2 h-2 bg-[#ff6b35] rounded-full mr-2 animate-pulse"></div>
                  Analyzing prompt...
                </div>
              </div>
            </motion.div>

            {/* Arrow */}
            <div className="hidden lg:flex justify-center">
              <motion.div
                animate={{ x: isAnimating ? [0, 10, 0] : 0 }}
                transition={{ duration: 0.5 }}
              >
                <ArrowRightIcon className="h-8 w-8 text-[#ff6b35]" />
              </motion.div>
            </div>

            {/* AI Classification */}
            <motion.div
              key={`classification-${activeExample}`}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="lg:col-span-1"
            >
              <div className={`${currentExample.bgColor} ${currentExample.borderColor} rounded-xl p-6 border-2 text-center`}>
                {React.createElement(currentExample.icon, {
                  className: `h-8 w-8 ${currentExample.color} mx-auto mb-3`
                })}
                <div className="text-sm text-gray-500 mb-1">Classified as</div>
                <div className={`font-semibold ${currentExample.color}`}>
                  {currentExample.role}
                </div>
              </div>
            </motion.div>

            {/* Arrow */}
            <div className="hidden lg:flex justify-center">
              <motion.div
                animate={{ x: isAnimating ? [0, 10, 0] : 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                <ArrowRightIcon className="h-8 w-8 text-[#ff6b35]" />
              </motion.div>
            </div>

            {/* Output */}
            <motion.div
              key={`output-${activeExample}`}
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="lg:col-span-1"
            >
              <div className="bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-xl p-6 text-white">
                <CpuChipIcon className="h-8 w-8 mb-3" />
                <div className="text-sm opacity-90 mb-1">Routed to</div>
                <div className="font-semibold text-lg">
                  {currentExample.model}
                </div>
                <div className="text-sm opacity-75 mt-2">
                  Optimal for {currentExample.role.toLowerCase()}
                </div>
              </div>
            </motion.div>
          </div>

          {/* Example Selector */}
          <div className="flex justify-center mt-12 space-x-2">
            {routingExamples.map((example, index) => (
              <button
                key={example.id}
                onClick={() => setActiveExample(index)}
                className={`w-3 h-3 rounded-full transition-all duration-200 ${
                  index === activeExample
                    ? 'bg-[#ff6b35] scale-125'
                    : 'bg-gray-500 hover:bg-gray-400'
                }`}
              />
            ))}
          </div>
        </div>

        {/* Benefits */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-4 gap-8 mt-20"
        >
          <div className="text-center">
            <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center mx-auto mb-4">
              <BoltIcon className="h-6 w-6 text-blue-400" />
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">Automatic Classification</h3>
            <p className="text-gray-400">AI analyzes your prompt and determines the best model type automatically</p>
          </div>

          <div className="text-center">
            <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center mx-auto mb-4">
              <ArrowRightIcon className="h-6 w-6 text-green-400" />
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">Instant Routing</h3>
            <p className="text-gray-400">Requests are routed to the optimal model in milliseconds</p>
          </div>

          <div className="text-center">
            <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center mx-auto mb-4">
              <ChartBarIcon className="h-6 w-6 text-purple-400" />
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">Better Results</h3>
            <p className="text-gray-400">Each model excels at specific tasks, ensuring optimal output quality</p>
          </div>

          <div className="text-center">
            <div className="w-12 h-12 bg-orange-500/20 rounded-lg flex items-center justify-center mx-auto mb-4">
              <ShieldCheckIcon className="h-6 w-6 text-orange-400" />
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">Enterprise Ready</h3>
            <p className="text-gray-400">Built-in security, compliance, and monitoring for production use</p>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
