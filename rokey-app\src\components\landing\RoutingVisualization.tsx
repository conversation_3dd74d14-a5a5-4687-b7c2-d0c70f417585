'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';
import {
  BoltIcon,
  CodeBracketIcon,
  PencilIcon,
  ChartBarIcon,
  CpuChipIcon,
  ArrowRightIcon,
  ShieldCheckIcon,
  EyeIcon,
  CogIcon,
  CloudIcon,
  CheckIcon
} from '@heroicons/react/24/outline';

const routingExamples = [
  {
    id: 1,
    prompt: "Solve this complex math problem: 2x + 5 = 15",
    role: "logic_reasoning",
    roleName: "Logic & Reasoning",
    model: "GPT-4o",
    provider: "OpenAI",
    icon: BoltIcon,
    color: "text-blue-400",
    bgColor: "bg-blue-500/10",
    borderColor: "border-blue-500/20",
    glowColor: "shadow-blue-500/50"
  },
  {
    id: 2,
    prompt: "Write a blog post about AI trends",
    role: "writing",
    roleName: "Writing & Content Creation",
    model: "GPT-4o",
    provider: "OpenAI",
    icon: PencilIcon,
    color: "text-purple-400",
    bgColor: "bg-purple-500/10",
    borderColor: "border-purple-500/20",
    glowColor: "shadow-purple-500/50"
  },
  {
    id: 3,
    prompt: "Build a React component with TypeScript",
    role: "coding_frontend",
    roleName: "Frontend Development",
    model: "Claude 4 Opus",
    provider: "Anthropic",
    icon: CodeBracketIcon,
    color: "text-green-400",
    bgColor: "bg-green-500/10",
    borderColor: "border-green-500/20",
    glowColor: "shadow-green-500/50"
  },
  {
    id: 4,
    prompt: "Summarize this research paper",
    role: "research_synthesis",
    roleName: "Research & Analysis",
    model: "DeepSeek R1 0528",
    provider: "DeepSeek",
    icon: ChartBarIcon,
    color: "text-cyan-400",
    bgColor: "bg-cyan-500/10",
    borderColor: "border-cyan-500/20",
    glowColor: "shadow-cyan-500/50"
  }
];

export default function RoutingVisualization() {
  const [activeExample, setActiveExample] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      setIsAnimating(true);
      setTimeout(() => {
        setActiveExample((prev) => (prev + 1) % routingExamples.length);
        setIsAnimating(false);
      }, 500);
    }, 5000); // Slower transition for better viewing

    return () => clearInterval(interval);
  }, []);

  const currentExample = routingExamples[activeExample];

  return (
    <section className="py-20 bg-black relative overflow-hidden">
      {/* Custom CSS for flowing current animations */}
      <style jsx>{`
        @keyframes flowCurrent {
          0% {
            transform: translateX(-100%);
            opacity: 0;
          }
          20% {
            opacity: 1;
          }
          80% {
            opacity: 1;
          }
          100% {
            transform: translateX(200%);
            opacity: 0;
          }
        }
        .current-flow {
          animation: flowCurrent 3s ease-in-out infinite;
        }
        .current-flow-delayed {
          animation: flowCurrent 3s ease-in-out infinite;
          animation-delay: 1.5s;
        }
      `}</style>

      {/* Background Pattern */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-br from-gray-900/30 to-black"></div>
        <div
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: `
              linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: '60px 60px'
          }}
        ></div>

        {/* Floating orbs */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-[#ff6b35]/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-[#f7931e]/5 rounded-full blur-3xl"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-4xl sm:text-5xl font-bold text-white mb-6"
          >
            Introducing the AI Gateway Pattern
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.1 }}
            className="text-xl text-gray-300 max-w-3xl mx-auto"
          >
            Watch how RouKey intelligently routes your requests through our unified gateway to the perfect AI model
          </motion.p>
        </div>

        {/* Intelligent Role Routing Visualization */}
        <div className="relative max-w-7xl mx-auto">
          {/* Subtle background glow */}
          <div className="absolute inset-0 bg-gradient-to-r from-[#ff6b35]/5 via-transparent to-green-500/5 rounded-3xl blur-3xl"></div>

          <div className="relative grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-16 items-start">

            {/* Column 1: User Prompt */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="relative"
            >
              <div className="text-center mb-6">
                <h3 className="text-xl font-semibold text-white mb-2">User Prompt</h3>
                <p className="text-sm text-gray-400">Your request enters RouKey</p>
              </div>

              <div className={`bg-slate-800 border-2 ${currentExample.borderColor} rounded-xl p-6 relative ${currentExample.bgColor} shadow-lg ${currentExample.glowColor}`}>
                {/* Animated border glow */}
                <div className="absolute inset-0 rounded-xl border-2 border-dashed border-gray-500 opacity-30 animate-pulse"></div>

                {/* Prompt content */}
                <div className="relative z-10">
                  <div className="text-sm text-gray-400 mb-3">Incoming Request</div>
                  <div className="text-white font-medium mb-4 text-lg leading-relaxed transition-all duration-500">
                    "{currentExample.prompt}"
                  </div>
                  <div className="flex items-center text-sm text-gray-400">
                    <div className={`w-2 h-2 bg-[#ff6b35] rounded-full mr-2 animate-pulse`}></div>
                    Analyzing prompt...
                  </div>
                </div>

                {/* Connection dots with glow */}
                <div className="absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2">
                  <div className="flex space-x-1">
                    <div className="w-1 h-8 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50"></div>
                    <div className="w-1 h-6 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50" style={{animationDelay: '0.2s'}}></div>
                    <div className="w-1 h-4 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50" style={{animationDelay: '0.4s'}}></div>
                  </div>
                </div>
              </div>

              {/* Connection line with flowing current */}
              <div className="absolute right-0 top-1/2 transform translate-x-full -translate-y-1/2 hidden lg:block">
                <div className="relative w-16 h-px overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-r from-[#ff6b35] to-transparent opacity-40"></div>
                  <div className="absolute top-0 left-0 w-8 h-px bg-gradient-to-r from-transparent via-white to-[#ff6b35] current-flow shadow-lg shadow-orange-500/50"></div>
                  <div className="absolute top-0 left-0 w-4 h-px bg-white current-flow-delayed shadow-sm shadow-white/50"></div>
                </div>
              </div>
            </motion.div>

            {/* Column 2: Intelligent Role Classification */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="relative"
            >
              <div className="text-center mb-6">
                <h3 className="text-xl font-semibold text-white mb-2">Intelligent Role Classification</h3>
                <p className="text-sm text-gray-400">AI analyzes & routes to optimal model</p>
              </div>

              <div className="bg-slate-800 border-2 border-slate-700 rounded-xl p-6 relative shadow-xl">
                {/* Animated scanning border */}
                <div className="absolute inset-0 rounded-xl border-2 border-dashed border-[#ff6b35] opacity-40 animate-pulse"></div>

                {/* Classification Process */}
                <div className="relative z-10 space-y-4">
                  {/* AI Brain Icon */}
                  <div className="text-center mb-6">
                    <div className="w-16 h-16 bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-lg shadow-orange-500/30">
                      <BoltIcon className="w-8 h-8 text-white" />
                    </div>
                    <div className="text-sm text-gray-400">RouKey Smart Classifier</div>
                  </div>

                  {/* Role Detection */}
                  <div className={`${currentExample.bgColor} ${currentExample.borderColor} border-2 rounded-lg p-4 text-center shadow-lg ${currentExample.glowColor}`}>
                    {React.createElement(currentExample.icon, {
                      className: `h-6 w-6 ${currentExample.color} mx-auto mb-2`
                    })}
                    <div className="text-xs text-gray-400 mb-1">Classified as</div>
                    <div className={`font-semibold text-sm ${currentExample.color}`}>
                      {currentExample.roleName}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      Role ID: {currentExample.role}
                    </div>
                  </div>

                  {/* Processing Indicators */}
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div className="bg-slate-700 rounded p-2 text-center">
                      <div className="text-green-400 mb-1">✓ Context Analysis</div>
                      <div className="text-gray-500">Complete</div>
                    </div>
                    <div className="bg-slate-700 rounded p-2 text-center">
                      <div className="text-green-400 mb-1">✓ Role Matching</div>
                      <div className="text-gray-500">Complete</div>
                    </div>
                  </div>
                </div>

                {/* Connection dots with animated glow */}
                <div className="absolute left-0 top-1/2 transform -translate-x-1/2 -translate-y-1/2">
                  <div className="flex space-x-1">
                    <div className="w-1 h-4 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50" style={{animationDelay: '0.6s'}}></div>
                    <div className="w-1 h-6 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50" style={{animationDelay: '0.4s'}}></div>
                    <div className="w-1 h-8 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50" style={{animationDelay: '0.2s'}}></div>
                  </div>
                </div>
                <div className="absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2">
                  <div className="flex space-x-1">
                    <div className="w-1 h-8 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50"></div>
                    <div className="w-1 h-6 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50" style={{animationDelay: '0.2s'}}></div>
                    <div className="w-1 h-4 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50" style={{animationDelay: '0.4s'}}></div>
                  </div>
                </div>
              </div>

              {/* Connection lines with flowing current */}
              <div className="absolute left-0 top-1/2 transform -translate-x-full -translate-y-1/2 hidden lg:block">
                <div className="relative w-16 h-px overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-[#ff6b35] to-transparent opacity-40"></div>
                  <div className="absolute top-0 left-0 w-8 h-px bg-gradient-to-r from-transparent via-white to-[#ff6b35] current-flow shadow-lg shadow-orange-500/50"></div>
                  <div className="absolute top-0 left-0 w-4 h-px bg-white current-flow-delayed shadow-sm shadow-white/50"></div>
                </div>
              </div>
              <div className="absolute right-0 top-1/2 transform translate-x-full -translate-y-1/2 hidden lg:block">
                <div className="relative w-16 h-px overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-r from-[#ff6b35] to-transparent opacity-40"></div>
                  <div className="absolute top-0 left-0 w-8 h-px bg-gradient-to-r from-transparent via-white to-[#ff6b35] current-flow shadow-lg shadow-orange-500/50"></div>
                  <div className="absolute top-0 left-0 w-4 h-px bg-white current-flow-delayed shadow-sm shadow-white/50"></div>
                </div>
              </div>
            </motion.div>

            {/* Column 3: Optimal Model Selection */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="relative"
            >
              <div className="text-center mb-6">
                <h3 className="text-xl font-semibold text-white mb-2">Optimal Model Selection</h3>
                <p className="text-sm text-gray-400">Routed to the perfect model</p>
              </div>

              <div className="bg-gradient-to-br from-slate-800 to-slate-900 border-2 border-green-500/30 rounded-xl p-6 relative shadow-xl shadow-green-500/20">
                {/* Success glow border */}
                <div className="absolute inset-0 rounded-xl border-2 border-dashed border-green-400 opacity-50 animate-pulse"></div>

                <div className="relative z-10">
                  {/* Selected Model */}
                  <div className="text-center mb-6">
                    <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-lg shadow-green-500/30">
                      <CpuChipIcon className="w-8 h-8 text-white" />
                    </div>
                    <div className="text-lg font-bold text-white mb-1">{currentExample.model}</div>
                    <div className="text-sm text-green-400 font-medium">{currentExample.provider}</div>
                  </div>

                  {/* Role Assignment */}
                  <div className={`${currentExample.bgColor} ${currentExample.borderColor} border-2 rounded-lg p-4 text-center mb-4 shadow-lg ${currentExample.glowColor}`}>
                    <div className="text-xs text-gray-400 mb-1">Assigned Role</div>
                    <div className={`font-semibold text-sm ${currentExample.color}`}>
                      {currentExample.roleName}
                    </div>
                  </div>

                  {/* Performance Metrics */}
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div className="bg-slate-700 rounded p-2 text-center">
                      <div className="text-green-400 mb-1">⚡ Speed</div>
                      <div className="text-white font-medium">Optimal</div>
                    </div>
                    <div className="bg-slate-700 rounded p-2 text-center">
                      <div className="text-blue-400 mb-1">🎯 Accuracy</div>
                      <div className="text-white font-medium">High</div>
                    </div>
                  </div>

                  {/* Success indicator */}
                  <div className="mt-4 flex items-center justify-center text-green-400">
                    <CheckIcon className="w-4 h-4 mr-2" />
                    <span className="text-sm font-medium">Route Established</span>
                  </div>
                </div>

                {/* Connection dots with success glow */}
                <div className="absolute left-0 top-1/2 transform -translate-x-1/2 -translate-y-1/2">
                  <div className="flex space-x-1">
                    <div className="w-1 h-4 bg-green-400 rounded-full animate-pulse shadow-lg shadow-green-500/50" style={{animationDelay: '0.8s'}}></div>
                    <div className="w-1 h-6 bg-green-400 rounded-full animate-pulse shadow-lg shadow-green-500/50" style={{animationDelay: '0.6s'}}></div>
                    <div className="w-1 h-8 bg-green-400 rounded-full animate-pulse shadow-lg shadow-green-500/50" style={{animationDelay: '0.4s'}}></div>
                  </div>
                </div>
              </div>

              {/* Final connection line with success flow */}
              <div className="absolute left-0 top-1/2 transform -translate-x-full -translate-y-1/2 hidden lg:block">
                <div className="relative w-16 h-px overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-green-400 to-green-400 opacity-40"></div>
                  <div className="absolute top-0 left-0 w-8 h-px bg-gradient-to-r from-transparent via-white to-green-400 current-flow shadow-lg shadow-green-500/50"></div>
                  <div className="absolute top-0 left-0 w-4 h-px bg-white current-flow-delayed shadow-sm shadow-white/50"></div>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Example Selector */}
          <div className="flex justify-center mt-12 space-x-2">
            {routingExamples.map((example, index) => (
              <button
                key={example.id}
                onClick={() => setActiveExample(index)}
                className={`w-3 h-3 rounded-full transition-all duration-200 ${
                  index === activeExample
                    ? 'bg-[#ff6b35] scale-125'
                    : 'bg-gray-500 hover:bg-gray-400'
                }`}
              />
            ))}
          </div>
        </div>

        {/* RouKey's Intelligent Routing Benefits */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-4 gap-8 mt-20"
        >
          <div className="text-center">
            <div className="w-12 h-12 bg-[#ff6b35]/20 rounded-lg flex items-center justify-center mx-auto mb-4 shadow-lg shadow-orange-500/20">
              <BoltIcon className="h-6 w-6 text-[#ff6b35]" />
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">Role-Based Classification</h3>
            <p className="text-gray-400">RouKey Smart Classifier analyzes context and classifies requests into 15+ specialized roles</p>
          </div>

          <div className="text-center">
            <div className="w-12 h-12 bg-gray-700/50 rounded-lg flex items-center justify-center mx-auto mb-4 shadow-lg shadow-gray-500/20">
              <CpuChipIcon className="h-6 w-6 text-gray-300" />
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">Smart Model Matching</h3>
            <p className="text-gray-400">Each role routes to your pre-configured optimal model for maximum performance</p>
          </div>

          <div className="text-center">
            <div className="w-12 h-12 bg-[#f7931e]/20 rounded-lg flex items-center justify-center mx-auto mb-4 shadow-lg shadow-orange-400/20">
              <ChartBarIcon className="h-6 w-6 text-[#f7931e]" />
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">Contextual Continuity</h3>
            <p className="text-gray-400">Maintains conversation context and role consistency across multi-turn interactions</p>
          </div>

          <div className="text-center">
            <div className="w-12 h-12 bg-white/10 rounded-lg flex items-center justify-center mx-auto mb-4 shadow-lg shadow-white/10">
              <ShieldCheckIcon className="h-6 w-6 text-white" />
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">Fallback Protection</h3>
            <p className="text-gray-400">Automatic fallback to default general chat model when no role match is found</p>
          </div>
        </motion.div>

        {/* Premium Role Types Showcase */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.2 }}
          className="mt-24 relative"
        >
          {/* Section Header */}
          <div className="text-center mb-16">
            <motion.h4
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="text-4xl font-bold text-white mb-6"
            >
              15+ Built-in Role Types
            </motion.h4>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.1 }}
              className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed"
            >
              Each role is intelligently optimized for specific use cases, with the power to create
              <span className="text-[#ff6b35] font-semibold"> custom roles</span> for your unique workflows
            </motion.p>
          </div>

          {/* Interactive Role Grid with Connections */}
          <div className="relative max-w-6xl mx-auto">
            {/* Background glow effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-[#ff6b35]/5 via-transparent to-[#f7931e]/5 rounded-3xl blur-3xl"></div>

            {/* Connection lines background */}
            <div className="absolute inset-0 pointer-events-none">
              <svg className="w-full h-full opacity-20" viewBox="0 0 800 600">
                <defs>
                  <linearGradient id="connectionGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#ff6b35" stopOpacity="0.3" />
                    <stop offset="50%" stopColor="#f7931e" stopOpacity="0.6" />
                    <stop offset="100%" stopColor="#ff6b35" stopOpacity="0.3" />
                  </linearGradient>
                </defs>
                {/* Animated connection paths */}
                <motion.path
                  d="M100,150 Q400,50 700,150"
                  stroke="url(#connectionGradient)"
                  strokeWidth="1"
                  fill="none"
                  initial={{ pathLength: 0 }}
                  animate={{ pathLength: 1 }}
                  transition={{ duration: 3, repeat: Infinity, repeatType: "loop" }}
                />
                <motion.path
                  d="M100,300 Q400,200 700,300"
                  stroke="url(#connectionGradient)"
                  strokeWidth="1"
                  fill="none"
                  initial={{ pathLength: 0 }}
                  animate={{ pathLength: 1 }}
                  transition={{ duration: 3, repeat: Infinity, repeatType: "loop", delay: 1 }}
                />
                <motion.path
                  d="M100,450 Q400,350 700,450"
                  stroke="url(#connectionGradient)"
                  strokeWidth="1"
                  fill="none"
                  initial={{ pathLength: 0 }}
                  animate={{ pathLength: 1 }}
                  transition={{ duration: 3, repeat: Infinity, repeatType: "loop", delay: 2 }}
                />
              </svg>
            </div>

            {/* Role Cards Grid */}
            <div className="relative grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {[
                { name: 'General Chat', icon: '💬', gradient: 'from-gray-600 to-gray-700', delay: 0 },
                { name: 'Logic & Reasoning', icon: '🧠', gradient: 'from-[#ff6b35] to-[#f7931e]', delay: 0.1 },
                { name: 'Writing & Content', icon: '✍️', gradient: 'from-gray-700 to-gray-800', delay: 0.2 },
                { name: 'Frontend Coding', icon: '⚛️', gradient: 'from-[#f7931e] to-[#ff6b35]', delay: 0.3 },
                { name: 'Backend Coding', icon: '⚙️', gradient: 'from-gray-600 to-gray-700', delay: 0.4 },
                { name: 'Research & Analysis', icon: '🔬', gradient: 'from-[#ff6b35] to-[#f7931e]', delay: 0.5 },
                { name: 'Summarization', icon: '📝', gradient: 'from-gray-700 to-gray-800', delay: 0.6 },
                { name: 'Translation', icon: '🌐', gradient: 'from-[#f7931e] to-[#ff6b35]', delay: 0.7 },
                { name: 'Data Extraction', icon: '📊', gradient: 'from-gray-600 to-gray-700', delay: 0.8 },
                { name: 'Brainstorming', icon: '💡', gradient: 'from-[#ff6b35] to-[#f7931e]', delay: 0.9 },
                { name: 'Education', icon: '🎓', gradient: 'from-gray-700 to-gray-800', delay: 1.0 },
                { name: 'Audio Transcription', icon: '🎵', gradient: 'from-[#f7931e] to-[#ff6b35]', delay: 1.1 }
              ].map((role, index) => (
                <motion.div
                  key={role.name}
                  initial={{ opacity: 0, scale: 0.8, y: 20 }}
                  whileInView={{ opacity: 1, scale: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: role.delay, duration: 0.5 }}
                  whileHover={{ scale: 1.05, y: -5 }}
                  className="group relative"
                >
                  <div className={`bg-gradient-to-br ${role.gradient} rounded-2xl p-6 border border-gray-700 hover:border-[#ff6b35]/50 transition-all duration-300 shadow-lg hover:shadow-2xl hover:shadow-orange-500/10 backdrop-blur-sm`}>
                    {/* Glow effect on hover */}
                    <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-[#ff6b35]/10 to-[#f7931e]/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                    <div className="relative z-10 text-center">
                      <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300 filter drop-shadow-lg">
                        {role.icon}
                      </div>
                      <div className="text-white font-semibold text-sm leading-tight">
                        {role.name}
                      </div>
                    </div>

                    {/* Pulse animation for featured roles */}
                    {role.gradient.includes('ff6b35') && (
                      <div className="absolute inset-0 rounded-2xl border-2 border-[#ff6b35]/30 animate-pulse"></div>
                    )}
                  </div>
                </motion.div>
              ))}

              {/* Premium Custom Role Card */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8, y: 20 }}
                whileInView={{ opacity: 1, scale: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 1.2, duration: 0.5 }}
                whileHover={{ scale: 1.05, y: -5 }}
                className="group relative col-span-2 md:col-span-1"
              >
                <div className="bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-2xl p-6 border-2 border-dashed border-white/30 hover:border-white/60 transition-all duration-300 shadow-2xl shadow-orange-500/25 relative overflow-hidden">
                  {/* Animated background pattern */}
                  <div
                    className="absolute inset-0 opacity-10"
                    style={{
                      backgroundImage: `
                        linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                        linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px)
                      `,
                      backgroundSize: '10px 10px',
                      animation: 'grid-move 10s linear infinite'
                    }}
                  ></div>

                  <div className="relative z-10 text-center">
                    <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300 filter drop-shadow-lg">
                      🎯
                    </div>
                    <div className="text-white font-bold text-sm mb-2">
                      Custom Roles
                    </div>
                    <div className="text-white/80 text-xs">
                      Create your own
                    </div>
                  </div>

                  {/* Glowing border animation */}
                  <div className="absolute inset-0 rounded-2xl border-2 border-white/50 animate-pulse"></div>
                </div>
              </motion.div>
            </div>

            {/* Bottom stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 1.5 }}
              className="text-center mt-12"
            >
              <div className="inline-flex items-center bg-gradient-to-r from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-full px-8 py-4 border border-gray-700">
                <div className="flex items-center space-x-8">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white">15+</div>
                    <div className="text-xs text-gray-400">Built-in Roles</div>
                  </div>
                  <div className="w-px h-8 bg-gray-600"></div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-[#ff6b35]">∞</div>
                    <div className="text-xs text-gray-400">Custom Roles</div>
                  </div>
                  <div className="w-px h-8 bg-gray-600"></div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white">100%</div>
                    <div className="text-xs text-gray-400">Accuracy</div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </motion.div>

        {/* Custom Role Creation Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.4 }}
          className="mt-20 bg-gradient-to-br from-gray-900/50 to-black/50 rounded-2xl p-8 border border-gray-700"
        >
          <div className="text-center mb-12">
            <h4 className="text-2xl font-bold text-white mb-4">Create Custom Roles for Your Workflow</h4>
            <p className="text-gray-400 max-w-3xl mx-auto">
              Beyond our 15+ built-in roles, RouKey empowers you to create custom roles tailored to your specific needs.
              Define role patterns, assign optimal models, and let our AI classifier automatically route requests.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Step 1 */}
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg shadow-orange-500/30">
                <span className="text-2xl text-white font-bold">1</span>
              </div>
              <h5 className="text-lg font-semibold text-white mb-3">Define Role Pattern</h5>
              <p className="text-gray-400 text-sm leading-relaxed">
                Describe the types of requests this role should handle. Our AI learns from your examples and keywords.
              </p>
              <div className="mt-4 bg-gray-800 rounded-lg p-3 text-left border border-gray-700">
                <div className="text-xs text-gray-500 mb-1">Example Pattern:</div>
                <div className="text-sm text-[#ff6b35] font-mono">
                  "SQL queries, database optimization, schema design"
                </div>
              </div>
            </div>

            {/* Step 2 */}
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-gray-700 to-gray-800 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg shadow-gray-500/30">
                <span className="text-2xl text-white font-bold">2</span>
              </div>
              <h5 className="text-lg font-semibold text-white mb-3">Assign Optimal Model</h5>
              <p className="text-gray-400 text-sm leading-relaxed">
                Choose which model performs best for this role from your available providers and configurations.
              </p>
              <div className="mt-4 bg-gray-800 rounded-lg p-3 text-left border border-gray-700">
                <div className="text-xs text-gray-500 mb-1">Model Selection:</div>
                <div className="text-sm text-white font-medium">
                  Claude 3.5 Sonnet → Database Expert
                </div>
              </div>
            </div>

            {/* Step 3 */}
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-[#f7931e] to-[#ff6b35] rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg shadow-orange-400/30">
                <span className="text-2xl text-white font-bold">3</span>
              </div>
              <h5 className="text-lg font-semibold text-white mb-3">Automatic Routing</h5>
              <p className="text-gray-400 text-sm leading-relaxed">
                Our RouKey Smart Classifier automatically detects and routes matching requests to your custom role.
              </p>
              <div className="mt-4 bg-gray-800 rounded-lg p-3 text-left border border-gray-700">
                <div className="text-xs text-gray-500 mb-1">Auto-Detection:</div>
                <div className="text-sm text-[#f7931e]">
                  ✓ Pattern Recognition Active
                </div>
              </div>
            </div>
          </div>

          <div className="text-center mt-12">
            <div className="inline-flex items-center bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-lg px-6 py-3 text-white font-medium shadow-lg shadow-orange-500/30">
              <span>🚀 Start Creating Custom Roles</span>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
