'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';
import {
  BoltIcon,
  CodeBracketIcon,
  PencilIcon,
  ChartBarIcon,
  CpuChipIcon,
  ArrowRightIcon,
  ShieldCheckIcon,
  EyeIcon,
  CogIcon,
  CloudIcon
} from '@heroicons/react/24/outline';

const routingExamples = [
  {
    id: 1,
    prompt: "Solve this complex math problem",
    role: "Logic & Reasoning",
    model: "GPT-4",
    icon: BoltIcon,
    color: "text-blue-600",
    bgColor: "bg-blue-50",
    borderColor: "border-blue-200"
  },
  {
    id: 2,
    prompt: "Write creative content",
    role: "Creative Writing",
    model: "Claude 4",
    icon: PencilIcon,
    color: "text-purple-600",
    bgColor: "bg-purple-50",
    borderColor: "border-purple-200"
  },
  {
    id: 3,
    prompt: "Build a React component",
    role: "Code Generation",
    model: "DeepSeek Coder",
    icon: CodeBracketIcon,
    color: "text-green-600",
    bgColor: "bg-green-50",
    borderColor: "border-green-200"
  }
];

export default function RoutingVisualization() {
  const [activeExample, setActiveExample] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      setIsAnimating(true);
      setTimeout(() => {
        setActiveExample((prev) => (prev + 1) % routingExamples.length);
        setIsAnimating(false);
      }, 500);
    }, 4000);

    return () => clearInterval(interval);
  }, []);

  const currentExample = routingExamples[activeExample];

  return (
    <section className="py-20 bg-gradient-to-br from-slate-900 via-gray-900 to-slate-800 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:60px_60px]" />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-4xl sm:text-5xl font-bold text-white mb-6"
          >
            Introducing the AI Gateway Pattern
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.1 }}
            className="text-xl text-gray-300 max-w-3xl mx-auto"
          >
            Watch how RouKey intelligently routes your requests through our unified gateway to the perfect AI model
          </motion.p>
        </div>

        {/* Three Column Gateway Pattern */}
        <div className="relative max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12 items-center">

            {/* Column 1: Your AI App */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.2 }}
              className="relative"
            >
              <div className="text-center mb-6">
                <h3 className="text-lg font-semibold text-white mb-2">Your AI App</h3>
              </div>

              <div className="bg-slate-800 border border-slate-700 rounded-xl p-8 relative">
                {/* App Icon */}
                <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                  <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                  </svg>
                </div>

                {/* Connection dots */}
                <div className="absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2">
                  <div className="flex space-x-1">
                    <div className="w-1 h-8 bg-slate-600 rounded-full"></div>
                    <div className="w-1 h-6 bg-slate-600 rounded-full"></div>
                    <div className="w-1 h-4 bg-slate-600 rounded-full"></div>
                  </div>
                </div>
              </div>

              {/* Connection line */}
              <div className="absolute right-0 top-1/2 transform translate-x-full -translate-y-1/2 hidden lg:block">
                <div className="w-12 h-px bg-slate-600"></div>
              </div>
            </motion.div>

            {/* Column 2: RouKey AI Gateway */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.4 }}
              className="relative"
            >
              <div className="text-center mb-6">
                <h3 className="text-lg font-semibold text-white mb-2">RouKey AI Gateway</h3>
              </div>

              <div className="bg-slate-800 border border-slate-700 rounded-xl p-6 relative">
                {/* Gateway Features Grid */}
                <div className="grid grid-cols-2 gap-3 mb-6">
                  <div className="bg-orange-500/10 border border-orange-500/20 rounded-lg p-3 text-center">
                    <div className="text-xs font-medium text-orange-400 mb-1">OBSERVABILITY</div>
                  </div>
                  <div className="bg-purple-500/10 border border-purple-500/20 rounded-lg p-3 text-center">
                    <div className="text-xs font-medium text-purple-400 mb-1">GOVERNANCE</div>
                  </div>
                  <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-3 text-center">
                    <div className="text-xs font-medium text-green-400 mb-1">ROUTING</div>
                  </div>
                  <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3 text-center">
                    <div className="text-xs font-medium text-blue-400 mb-1">PROMPT MGMT</div>
                  </div>
                  <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3 text-center">
                    <div className="text-xs font-medium text-red-400 mb-1">GUARDRAILS</div>
                  </div>
                  <div className="bg-cyan-500/10 border border-cyan-500/20 rounded-lg p-3 text-center">
                    <div className="text-xs font-medium text-cyan-400 mb-1">FINOPS</div>
                  </div>
                </div>

                {/* Central Processing Unit */}
                <div className="bg-slate-700 rounded-lg p-4 text-center">
                  <div className="w-8 h-8 bg-[#ff6b35] rounded-full mx-auto mb-2 flex items-center justify-center">
                    <div className="w-3 h-3 bg-white rounded-full animate-pulse"></div>
                  </div>
                  <div className="text-xs text-slate-400">Processing Request</div>
                </div>

                {/* Connection dots */}
                <div className="absolute left-0 top-1/2 transform -translate-x-1/2 -translate-y-1/2">
                  <div className="flex space-x-1">
                    <div className="w-1 h-4 bg-slate-600 rounded-full"></div>
                    <div className="w-1 h-6 bg-slate-600 rounded-full"></div>
                    <div className="w-1 h-8 bg-slate-600 rounded-full"></div>
                  </div>
                </div>
                <div className="absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2">
                  <div className="flex space-x-1">
                    <div className="w-1 h-8 bg-slate-600 rounded-full"></div>
                    <div className="w-1 h-6 bg-slate-600 rounded-full"></div>
                    <div className="w-1 h-4 bg-slate-600 rounded-full"></div>
                  </div>
                </div>
              </div>

              {/* Connection lines */}
              <div className="absolute left-0 top-1/2 transform -translate-x-full -translate-y-1/2 hidden lg:block">
                <div className="w-12 h-px bg-slate-600"></div>
              </div>
              <div className="absolute right-0 top-1/2 transform translate-x-full -translate-y-1/2 hidden lg:block">
                <div className="w-12 h-px bg-slate-600"></div>
              </div>
            </motion.div>

            {/* Column 3: AI Providers */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.6 }}
              className="relative"
            >
              <div className="text-center mb-6">
                <h3 className="text-lg font-semibold text-white mb-2">AI Providers</h3>
              </div>

              <div className="bg-slate-800 border border-slate-700 rounded-xl p-6 relative">
                {/* Provider Icons Grid */}
                <div className="grid grid-cols-3 gap-3 mb-6">
                  {/* OpenAI */}
                  <div className="bg-slate-700 rounded-lg p-3 flex items-center justify-center group hover:bg-slate-600 transition-colors">
                    <div className="w-6 h-6 bg-green-500 rounded-full shadow-sm"></div>
                  </div>
                  {/* Anthropic */}
                  <div className="bg-slate-700 rounded-lg p-3 flex items-center justify-center group hover:bg-slate-600 transition-colors">
                    <div className="w-6 h-6 bg-blue-500 rounded-sm shadow-sm"></div>
                  </div>
                  {/* Mistral */}
                  <div className="bg-slate-700 rounded-lg p-3 flex items-center justify-center group hover:bg-slate-600 transition-colors">
                    <div className="w-6 h-6 bg-orange-500 rounded-lg shadow-sm"></div>
                  </div>
                  {/* Google */}
                  <div className="bg-slate-700 rounded-lg p-3 flex items-center justify-center group hover:bg-slate-600 transition-colors">
                    <div className="w-6 h-6 bg-purple-500 rounded-full shadow-sm"></div>
                  </div>
                  {/* Cohere */}
                  <div className="bg-slate-700 rounded-lg p-3 flex items-center justify-center group hover:bg-slate-600 transition-colors">
                    <div className="w-6 h-6 bg-pink-500 rounded-sm shadow-sm"></div>
                  </div>
                  {/* Perplexity */}
                  <div className="bg-slate-700 rounded-lg p-3 flex items-center justify-center group hover:bg-slate-600 transition-colors">
                    <div className="w-6 h-6 bg-cyan-500 rounded-lg shadow-sm"></div>
                  </div>
                  {/* Azure */}
                  <div className="bg-slate-700 rounded-lg p-3 flex items-center justify-center group hover:bg-slate-600 transition-colors">
                    <div className="w-6 h-6 bg-blue-600 rounded-full shadow-sm"></div>
                  </div>
                  {/* Vertex AI */}
                  <div className="bg-slate-700 rounded-lg p-3 flex items-center justify-center group hover:bg-slate-600 transition-colors">
                    <div className="w-6 h-6 bg-yellow-500 rounded-sm shadow-sm"></div>
                  </div>
                  {/* Cloudflare */}
                  <div className="bg-slate-700 rounded-lg p-3 flex items-center justify-center group hover:bg-slate-600 transition-colors">
                    <div className="w-6 h-6 bg-orange-600 rounded-lg shadow-sm"></div>
                  </div>
                </div>

                {/* Custom Model Section */}
                <div className="border-t border-slate-700 pt-4">
                  <div className="text-center">
                    <div className="text-xs text-slate-400 mb-2">Custom Model</div>
                    <div className="bg-slate-700 rounded-lg p-2">
                      <div className="text-xs text-slate-300">Fine-Tune</div>
                    </div>
                  </div>
                </div>

                {/* Connection dots */}
                <div className="absolute left-0 top-1/2 transform -translate-x-1/2 -translate-y-1/2">
                  <div className="flex space-x-1">
                    <div className="w-1 h-4 bg-slate-600 rounded-full"></div>
                    <div className="w-1 h-6 bg-slate-600 rounded-full"></div>
                    <div className="w-1 h-8 bg-slate-600 rounded-full"></div>
                  </div>
                </div>
              </div>

              {/* Connection line */}
              <div className="absolute left-0 top-1/2 transform -translate-x-full -translate-y-1/2 hidden lg:block">
                <div className="w-12 h-px bg-slate-600"></div>
              </div>
            </motion.div>
          </div>

          {/* Example Selector */}
          <div className="flex justify-center mt-12 space-x-2">
            {routingExamples.map((example, index) => (
              <button
                key={example.id}
                onClick={() => setActiveExample(index)}
                className={`w-3 h-3 rounded-full transition-all duration-200 ${
                  index === activeExample
                    ? 'bg-[#ff6b35] scale-125'
                    : 'bg-gray-500 hover:bg-gray-400'
                }`}
              />
            ))}
          </div>
        </div>

        {/* Benefits */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-4 gap-8 mt-20"
        >
          <div className="text-center">
            <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center mx-auto mb-4">
              <BoltIcon className="h-6 w-6 text-blue-400" />
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">Automatic Classification</h3>
            <p className="text-gray-400">AI analyzes your prompt and determines the best model type automatically</p>
          </div>

          <div className="text-center">
            <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center mx-auto mb-4">
              <ArrowRightIcon className="h-6 w-6 text-green-400" />
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">Instant Routing</h3>
            <p className="text-gray-400">Requests are routed to the optimal model in milliseconds</p>
          </div>

          <div className="text-center">
            <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center mx-auto mb-4">
              <ChartBarIcon className="h-6 w-6 text-purple-400" />
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">Better Results</h3>
            <p className="text-gray-400">Each model excels at specific tasks, ensuring optimal output quality</p>
          </div>

          <div className="text-center">
            <div className="w-12 h-12 bg-orange-500/20 rounded-lg flex items-center justify-center mx-auto mb-4">
              <ShieldCheckIcon className="h-6 w-6 text-orange-400" />
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">Enterprise Ready</h3>
            <p className="text-gray-400">Built-in security, compliance, and monitoring for production use</p>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
