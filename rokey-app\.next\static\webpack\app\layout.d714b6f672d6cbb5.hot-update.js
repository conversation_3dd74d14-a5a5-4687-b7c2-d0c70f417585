"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"0da5c7724900\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MTAxNiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjBkYTVjNzcyNDkwMFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/Sidebar.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Sidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/KeyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BeakerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/AcademicCapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/KeyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/BeakerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/MapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/AcademicCapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/ChartBarIcon.js\");\n/* harmony import */ var _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/SidebarContext */ \"(app-pages-browser)/./src/contexts/SidebarContext.tsx\");\n/* harmony import */ var _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/NavigationContext */ \"(app-pages-browser)/./src/contexts/NavigationContext.tsx\");\n/* harmony import */ var _hooks_useRoutePrefetch__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useRoutePrefetch */ \"(app-pages-browser)/./src/hooks/useRoutePrefetch.ts\");\n/* harmony import */ var _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useChatHistory */ \"(app-pages-browser)/./src/hooks/useChatHistory.ts\");\n/* harmony import */ var _hooks_usePredictiveNavigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/usePredictiveNavigation */ \"(app-pages-browser)/./src/hooks/usePredictiveNavigation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst navItems = [\n    {\n        href: \"/dashboard\",\n        label: \"Dashboard\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        description: \"Overview & analytics\"\n    },\n    {\n        href: \"/my-models\",\n        label: \"My Models\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        description: \"API key management\"\n    },\n    {\n        href: \"/playground\",\n        label: \"Playground\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        description: \"Test your models\"\n    },\n    {\n        href: \"/routing-setup\",\n        label: \"Routing Setup\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        description: \"Configure routing\"\n    },\n    {\n        href: \"/logs\",\n        label: \"Logs\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n        description: \"Request history\"\n    },\n    {\n        href: \"/training\",\n        label: \"Prompt Engineering\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n        description: \"Custom prompts\"\n    },\n    {\n        href: \"/analytics\",\n        label: \"Analytics\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n        description: \"Advanced insights\"\n    }\n];\nfunction Sidebar() {\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { isCollapsed, isHovered, isHoverDisabled, setHovered } = (0,_contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_4__.useSidebar)();\n    const { navigateOptimistically } = (0,_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_5__.useNavigation)();\n    const { prefetchOnHover } = (0,_hooks_useRoutePrefetch__WEBPACK_IMPORTED_MODULE_6__.useRoutePrefetch)();\n    const { prefetchWhenIdle } = (0,_hooks_useRoutePrefetch__WEBPACK_IMPORTED_MODULE_6__.useIntelligentPrefetch)();\n    const { prefetchChatHistory } = (0,_hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_7__.useChatHistoryPrefetch)();\n    const { predictions, isLearning } = (0,_hooks_usePredictiveNavigation__WEBPACK_IMPORTED_MODULE_8__.usePredictiveNavigation)();\n    const contextualSuggestions = (0,_hooks_usePredictiveNavigation__WEBPACK_IMPORTED_MODULE_8__.useContextualSuggestions)();\n    // Enhanced prefetching with predictive navigation\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        const allRoutes = navItems.map((item)=>item.href);\n        // Combine predictive routes with standard prefetching\n        const predictiveRoutes = predictions.slice(0, 2); // Top 2 predictions\n        const contextualRoutes = contextualSuggestions.filter((s)=>s.priority === \"high\").map((s)=>s.route).slice(0, 2);\n        const routesToPrefetch = [\n            ...predictiveRoutes,\n            ...contextualRoutes,\n            ...allRoutes.filter((route)=>route !== pathname && !predictiveRoutes.includes(route) && !contextualRoutes.includes(route)),\n            \"/playground\",\n            \"/logs\"\n        ].slice(0, 6); // Increased limit for better coverage\n        console.log(\"\\uD83E\\uDDE0 [PREDICTIVE] Prefetching routes:\", {\n            predictive: predictiveRoutes,\n            contextual: contextualRoutes,\n            total: routesToPrefetch,\n            isLearning\n        });\n        const cleanup = prefetchWhenIdle(routesToPrefetch);\n        return cleanup;\n    }, [\n        pathname,\n        prefetchWhenIdle,\n        predictions,\n        contextualSuggestions,\n        isLearning\n    ]);\n    // Determine if sidebar should be expanded (hover or not collapsed)\n    const isExpanded = !isCollapsed || isHovered;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"sidebar flex flex-col h-screen flex-shrink-0 transition-all duration-200 ease-out z-50 bg-gray-900 dark:bg-black border-r border-gray-800 dark:border-neon-blue/20 \".concat(isExpanded ? \"w-64\" : \"w-16\"),\n        onMouseEnter: ()=>!isHoverDisabled && setHovered(true),\n        onMouseLeave: ()=>!isHoverDisabled && setHovered(false),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 relative overflow-hidden\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 transition-all duration-200 ease-out \".concat(isExpanded ? \"px-6\" : \"px-3\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 pt-4 transition-all duration-200 ease-out \".concat(isExpanded ? \"\" : \"text-center\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"transition-all duration-200 ease-out \".concat(isExpanded ? \"opacity-0 scale-75 -translate-y-2\" : \"opacity-100 scale-100 translate-y-0\", \" \").concat(isExpanded ? \"absolute\" : \"relative\", \" w-8 h-8 bg-gradient-to-br from-orange-500 to-orange-600 dark:from-neon-blue dark:to-neon-purple rounded-lg flex items-center justify-center mx-auto shadow-lg dark:shadow-neon-blue\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-bold text-sm\",\n                                        children: \"R\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"transition-all duration-200 ease-out \".concat(isExpanded ? \"opacity-100 scale-100 translate-y-0\" : \"opacity-0 scale-75 translate-y-2\", \" \").concat(isExpanded ? \"relative\" : \"absolute top-0 left-0 w-full\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-bold text-white dark:neon-text-blue tracking-tight whitespace-nowrap\",\n                                            children: \"RoKey\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-400 dark:text-gray-300 mt-1 whitespace-nowrap\",\n                                            children: \"Smart LLM Router\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"space-y-2\",\n                        children: navItems.map((item)=>{\n                            const isActive = pathname === item.href || pathname.startsWith(item.href + \"/\");\n                            const Icon = isActive ? item.iconSolid : item.icon;\n                            const isPredicted = predictions.includes(item.href);\n                            const contextualSuggestion = contextualSuggestions.find((s)=>s.route === item.href);\n                            // Enhanced prefetch for playground to include chat history\n                            const handlePlaygroundHover = ()=>{\n                                if (item.href === \"/playground\") {\n                                    // Prefetch route\n                                    prefetchOnHover(item.href, 50).onMouseEnter();\n                                    // Also prefetch chat history for current config if available\n                                    const currentConfigId = new URLSearchParams(window.location.search).get(\"config\");\n                                    if (currentConfigId) {\n                                        prefetchChatHistory(currentConfigId);\n                                    }\n                                }\n                            };\n                            const hoverProps = item.href === \"/playground\" ? {\n                                onMouseEnter: handlePlaygroundHover\n                            } : prefetchOnHover(item.href, 50);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: item.href,\n                                onClick: (e)=>{\n                                    e.preventDefault();\n                                    navigateOptimistically(item.href);\n                                },\n                                className: \"sidebar-nav-item group flex items-center transition-all duration-200 ease-out w-full text-left \".concat(isActive ? \"active\" : \"\", \" \").concat(isExpanded ? \"\" : \"collapsed\"),\n                                title: isExpanded ? undefined : item.label,\n                                ...hoverProps,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex items-center w-full overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative flex items-center justify-center transition-all duration-200 ease-out \".concat(isExpanded ? \"w-5 h-5 mr-3\" : \"w-10 h-10 rounded-xl\", \" \").concat(!isExpanded && isActive ? \"bg-white dark:bg-neon-blue/20 shadow-sm dark:shadow-neon-blue\" : !isExpanded ? \"bg-transparent hover:bg-white/10 dark:hover:bg-neon-blue/10\" : \"\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"transition-all duration-200 ease-out \".concat(isExpanded ? \"h-5 w-5\" : \"h-5 w-5\", \" \").concat(isActive ? \"text-orange-500 dark:text-neon-blue\" : \"text-white dark:text-gray-300\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 23\n                                                }, this),\n                                                isPredicted && !isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute rounded-full bg-blue-400 dark:bg-neon-purple animate-pulse transition-all duration-200 ease-out \".concat(isExpanded ? \"-top-1 -right-1 w-2 h-2\" : \"-top-1 -right-1 w-3 h-3\"),\n                                                    title: \"Predicted next destination\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 transition-all duration-200 ease-out \".concat(isExpanded ? \"opacity-100 translate-x-0 max-w-full\" : \"opacity-0 translate-x-4 max-w-0\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between whitespace-nowrap\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium text-sm\",\n                                                            children: item.label\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 228,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        contextualSuggestion && !isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs px-1.5 py-0.5 rounded-full ml-2 \".concat(contextualSuggestion.priority === \"high\" ? \"bg-blue-500/20 text-blue-300\" : \"bg-gray-500/20 text-gray-300\"),\n                                                            children: contextualSuggestion.priority === \"high\" ? \"!\" : \"\\xb7\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs transition-colors duration-200 whitespace-nowrap \".concat(isActive ? \"text-orange-400 dark:text-neon-blue\" : \"text-gray-400 dark:text-gray-500\"),\n                                                    children: contextualSuggestion ? contextualSuggestion.reason : item.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 19\n                                }, this)\n                            }, item.href, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 139,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n            lineNumber: 137,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n        lineNumber: 130,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"eS+ydXAo96PN1Qszj9rKXwHTkQk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_4__.useSidebar,\n        _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_5__.useNavigation,\n        _hooks_useRoutePrefetch__WEBPACK_IMPORTED_MODULE_6__.useRoutePrefetch,\n        _hooks_useRoutePrefetch__WEBPACK_IMPORTED_MODULE_6__.useIntelligentPrefetch,\n        _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_7__.useChatHistoryPrefetch,\n        _hooks_usePredictiveNavigation__WEBPACK_IMPORTED_MODULE_8__.usePredictiveNavigation,\n        _hooks_usePredictiveNavigation__WEBPACK_IMPORTED_MODULE_8__.useContextualSuggestions\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sidebar.tsx\n"));

/***/ })

});