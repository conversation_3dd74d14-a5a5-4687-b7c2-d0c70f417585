'use client';

import { motion } from 'framer-motion';
import { CheckIcon, StarIcon, XMarkIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';
import LandingNavbar from '@/components/landing/LandingNavbar';
import Footer from '@/components/landing/Footer';

const pricingTiers = [
  {
    name: "Starter",
    price: 29,
    description: "Perfect for individual developers and small projects",
    features: [
      "50,000 API requests/month",
      "3 Custom Configurations",
      "10 API Keys per config",
      "All 300+ AI models",
      "Intelligent role routing",
      "Basic analytics (30-day history)",
      "Community support"
    ],
    notIncluded: [
      "Advanced routing strategies",
      "Performance monitoring",
      "Priority support"
    ],
    cta: "Get Started",
    popular: false
  },
  {
    name: "Professional",
    price: 199,
    description: "Advanced features for growing businesses and production workloads",
    features: [
      "Up to 1M API requests/month",
      "15 Custom Configurations",
      "50 API Keys per config",
      "All 300+ AI models",
      "Advanced routing strategies",
      "Advanced analytics (90-day history)",
      "Performance monitoring",
      "Cost optimization alerts",
      "Priority email support",
      "99.9% uptime SLA"
    ],
    notIncluded: [
      "Custom model integration",
      "Dedicated infrastructure",
      "White-label options"
    ],
    cta: "Get Started",
    popular: true
  },
  {
    name: "Enterprise",
    price: 999,
    description: "Full-scale solution for large organizations with custom requirements",
    features: [
      "Unlimited API requests",
      "Unlimited configurations",
      "Unlimited API keys",
      "All 300+ models + priority access",
      "Custom model integration",
      "Dedicated infrastructure",
      "Enterprise analytics (1-year history)",
      "Advanced SLA monitoring",
      "24/7 premium support",
      "Custom integrations",
      "White-label options",
      "Dedicated account manager"
    ],
    notIncluded: [],
    cta: "Get Started",
    popular: false
  }
];

const comparisonFeatures = [
  {
    category: "Usage Limits",
    features: [
      { name: "API Requests per month", starter: "50,000", pro: "500,000", enterprise: "2,000,000" },
      { name: "Custom Configurations", starter: "3", pro: "15", enterprise: "Unlimited" },
      { name: "API Keys per config", starter: "10", pro: "50", enterprise: "Unlimited" }
    ]
  },
  {
    category: "AI Models & Routing",
    features: [
      { name: "Supported AI Models", starter: "300+", pro: "300+", enterprise: "300+ + Priority" },
      { name: "Intelligent Role Routing", starter: "✓", pro: "✓", enterprise: "✓" },
      { name: "Advanced Routing Strategies", starter: "✗", pro: "✓", enterprise: "✓" },
      { name: "Custom Routing Rules", starter: "✗", pro: "✗", enterprise: "✓" }
    ]
  },
  {
    category: "Analytics & Monitoring",
    features: [
      { name: "Basic Analytics", starter: "30 days", pro: "90 days", enterprise: "1 year" },
      { name: "Performance Monitoring", starter: "✗", pro: "✓", enterprise: "✓" },
      { name: "Cost Optimization", starter: "Basic", pro: "Advanced", enterprise: "Enterprise" },
      { name: "SLA Monitoring", starter: "✗", pro: "✗", enterprise: "✓" }
    ]
  },
  {
    category: "Support & Management",
    features: [
      { name: "Support Level", starter: "Community", pro: "Priority Email", enterprise: "Dedicated + Phone" },
      { name: "Team Management", starter: "✗", pro: "✗", enterprise: "✓" },
      { name: "Custom Integrations", starter: "✗", pro: "✗", enterprise: "✓" }
    ]
  }
];

export default function PricingPage() {
  return (
    <div className="min-h-screen bg-white">
      <LandingNavbar />
      
      <main className="pt-20">
        {/* Hero Section */}
        <section className="py-16 bg-gradient-to-br from-slate-50 to-blue-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6"
            >
              Simple, Transparent Pricing
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="text-xl text-gray-600 max-w-3xl mx-auto mb-8"
            >
              Choose the perfect plan for your needs. All plans include access to 300+ AI models
              with intelligent routing. No hidden fees, no surprises.
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="inline-flex items-center px-4 py-2 rounded-full bg-[#ff6b35]/10 text-[#ff6b35] text-sm font-medium"
            >
              <CheckIcon className="h-4 w-4 mr-2" />
              Instant activation • Start building immediately
            </motion.div>
          </div>
        </section>

        {/* Pricing Cards */}
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {pricingTiers.map((tier, index) => (
                <motion.div
                  key={tier.name}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className={`relative bg-white rounded-2xl border-2 p-8 ${
                    tier.popular 
                      ? 'border-[#ff6b35] shadow-xl scale-105' 
                      : 'border-gray-200 shadow-sm'
                  }`}
                >
                  {tier.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <div className="bg-[#ff6b35] text-white px-4 py-1 rounded-full text-sm font-medium flex items-center">
                        <StarIcon className="h-4 w-4 mr-1" />
                        Most Popular
                      </div>
                    </div>
                  )}

                  <div className="text-center mb-8">
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">{tier.name}</h3>
                    <p className="text-gray-600 mb-4">{tier.description}</p>
                    <div className="flex items-baseline justify-center">
                      <span className="text-4xl font-bold text-gray-900">${tier.price}</span>
                      <span className="text-gray-600 ml-2">/month</span>
                    </div>
                  </div>

                  <div className="space-y-4 mb-8">
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-3">Included:</h4>
                      <ul className="space-y-2">
                        {tier.features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-start">
                            <CheckIcon className="h-5 w-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                            <span className="text-gray-700 text-sm">{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    {tier.notIncluded.length > 0 && (
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-3">Not included:</h4>
                        <ul className="space-y-2">
                          {tier.notIncluded.map((feature, featureIndex) => (
                            <li key={featureIndex} className="flex items-start">
                              <XMarkIcon className="h-5 w-5 text-gray-400 mr-3 mt-0.5 flex-shrink-0" />
                              <span className="text-gray-500 text-sm">{feature}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>

                  <Link
                    href="/auth/signup"
                    className={`block w-full text-center py-3 px-6 rounded-lg font-semibold transition-all duration-200 ${
                      tier.popular
                        ? 'bg-[#ff6b35] text-white hover:bg-[#e55a2b] shadow-lg hover:shadow-xl'
                        : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
                    }`}
                  >
                    {tier.cta}
                  </Link>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Feature Comparison Table */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Detailed Feature Comparison</h2>
              <p className="text-xl text-gray-600">Compare all features across our pricing tiers</p>
            </div>

            <div className="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">Features</th>
                      <th className="px-6 py-4 text-center text-sm font-semibold text-gray-900">Starter</th>
                      <th className="px-6 py-4 text-center text-sm font-semibold text-gray-900 bg-[#ff6b35]/5">Professional</th>
                      <th className="px-6 py-4 text-center text-sm font-semibold text-gray-900">Enterprise</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {comparisonFeatures.map((category, categoryIndex) => (
                      <>
                        <tr key={category.category} className="bg-gray-50">
                          <td colSpan={4} className="px-6 py-3 text-sm font-semibold text-gray-900">
                            {category.category}
                          </td>
                        </tr>
                        {category.features.map((feature, featureIndex) => (
                          <tr key={`${categoryIndex}-${featureIndex}`} className="hover:bg-gray-50">
                            <td className="px-6 py-4 text-sm text-gray-900">{feature.name}</td>
                            <td className="px-6 py-4 text-sm text-center text-gray-700">{feature.starter}</td>
                            <td className="px-6 py-4 text-sm text-center text-gray-700 bg-[#ff6b35]/5">{feature.pro}</td>
                            <td className="px-6 py-4 text-sm text-center text-gray-700">{feature.enterprise}</td>
                          </tr>
                        ))}
                      </>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
}
