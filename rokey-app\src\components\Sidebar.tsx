'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useEffect } from 'react';
import {
  HomeIcon,
  KeyIcon,
  MapIcon,
  DocumentTextIcon,
  AcademicCapIcon,
  ChartBarIcon,
  BeakerIcon
} from '@heroicons/react/24/outline';
import {
  HomeIcon as HomeIconSolid,
  KeyIcon as KeyIconSolid,
  MapIcon as MapIconSolid,
  DocumentTextIcon as DocumentTextIconSolid,
  AcademicCapIcon as AcademicCapIconSolid,
  ChartBarIcon as ChartBarIconSolid,
  BeakerIcon as BeakerIconSolid
} from '@heroicons/react/24/solid';
import { useSidebar } from '@/contexts/SidebarContext';
import { useNavigation } from '@/contexts/NavigationContext';
import { useRoutePrefetch, useIntelligentPrefetch } from '@/hooks/useRoutePrefetch';
import { useChatHistoryPrefetch } from '@/hooks/useChatHistory';
import { usePredictiveNavigation, useContextualSuggestions } from '@/hooks/usePredictiveNavigation';

// Portkey-style navigation structure
const navItems = [
  {
    href: "/dashboard",
    label: "Analytics",
    icon: ChartBarIcon,
  },
  {
    href: "/my-models",
    label: "API Keys",
    icon: KeyIcon,
  },
  {
    href: "/playground",
    label: "Playground",
    icon: BeakerIcon,
  },
  {
    href: "/logs",
    label: "Logs",
    icon: DocumentTextIcon,
  },
  {
    href: "/training",
    label: "Prompts",
    icon: AcademicCapIcon,
  },
  {
    href: "/routing-setup",
    label: "Configs",
    icon: Cog6ToothIcon,
  },
];

export default function Sidebar() {
  const pathname = usePathname();
  const { navigateOptimistically } = useNavigation();

  return (
    <aside className="w-64 h-screen bg-background-secondary border-r border-border-primary flex flex-col">
      {/* Logo Section */}
      <div className="p-6 border-b border-border-secondary">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-primary-500 rounded-lg flex items-center justify-center shadow-glow">
            <span className="text-dark-950 font-bold text-sm">R</span>
          </div>
          <div>
            <h1 className="text-lg font-bold text-gradient">RouKey</h1>
          </div>
        </div>
      </div>

      {/* Workspace Selector - Portkey Style */}
      <div className="p-4 border-b border-border-secondary">
        <div className="flex items-center justify-between p-3 bg-background-tertiary rounded-lg hover:bg-background-hover transition-colors cursor-pointer">
          <div className="flex items-center space-x-3">
            <div className="w-6 h-6 bg-accent-orange rounded flex items-center justify-center">
              <span className="text-xs font-bold text-white">MW</span>
            </div>
            <span className="text-sm font-medium text-text-primary">My Workspace</span>
          </div>
          <ChevronDownIcon className="w-4 h-4 text-text-muted" />
        </div>
      </div>

          {/* Navigation */}
          <nav className="space-y-2">
            {navItems.map((item) => {
              const isActive = pathname === item.href || pathname.startsWith(item.href + '/');
              const Icon = isActive ? item.iconSolid : item.icon;
              const isPredicted = predictions.includes(item.href);
              const contextualSuggestion = contextualSuggestions.find(s => s.route === item.href);

              // Enhanced prefetch for playground to include chat history
              const handlePlaygroundHover = () => {
                if (item.href === '/playground') {
                  // Prefetch route
                  prefetchOnHover(item.href, 50).onMouseEnter();

                  // Also prefetch chat history for current config if available
                  const currentConfigId = new URLSearchParams(window.location.search).get('config');
                  if (currentConfigId) {
                    prefetchChatHistory(currentConfigId);
                  }
                }
              };

              const hoverProps = item.href === '/playground'
                ? { onMouseEnter: handlePlaygroundHover }
                : prefetchOnHover(item.href, 50);

              return (
                <Link
                  key={item.href}
                  href={item.href}
                  onClick={(e) => {
                    e.preventDefault();
                    navigateOptimistically(item.href);
                  }}
                  className={`sidebar-nav-item group flex items-center transition-all duration-200 ease-out w-full text-left ${
                    isActive ? 'active' : ''
                  } ${isExpanded ? '' : 'collapsed'}`}
                  title={isExpanded ? undefined : item.label}
                  {...hoverProps}
                >
                  <div className="relative flex items-center w-full overflow-hidden">
                    {/* Icon - always visible */}
                    <div className={`relative flex items-center justify-center transition-all duration-200 ease-out ${
                      isExpanded ? 'w-5 h-5 mr-3' : 'w-10 h-10 rounded-xl'
                    } ${
                      !isExpanded && isActive
                        ? 'bg-background-hover shadow-sm'
                        : !isExpanded
                        ? 'bg-transparent hover:bg-background-hover'
                        : ''
                    }`}>
                      <Icon className={`transition-all duration-200 ease-out ${
                        isExpanded ? 'h-5 w-5' : 'h-5 w-5'
                      } ${isActive ? 'text-accent-cyan' : 'text-text-primary'}`} />
                      {isPredicted && !isActive && (
                        <div className={`absolute rounded-full bg-blue-400 animate-pulse transition-all duration-200 ease-out ${
                          isExpanded ? '-top-1 -right-1 w-2 h-2' : '-top-1 -right-1 w-3 h-3'
                        }`} title="Predicted next destination" />
                      )}
                    </div>

                    {/* Text content - slides in/out */}
                    <div className={`flex-1 transition-all duration-200 ease-out ${
                      isExpanded
                        ? 'opacity-100 translate-x-0 max-w-full'
                        : 'opacity-0 translate-x-4 max-w-0'
                    }`}>
                      <div className="flex items-center justify-between whitespace-nowrap">
                        <div className="font-medium text-sm">{item.label}</div>
                        {contextualSuggestion && !isActive && (
                          <span className={`text-xs px-1.5 py-0.5 rounded-full ml-2 ${
                            contextualSuggestion.priority === 'high'
                              ? 'bg-blue-500/20 text-blue-300'
                              : 'bg-gray-500/20 text-gray-300'
                          }`}>
                            {contextualSuggestion.priority === 'high' ? '!' : '·'}
                          </span>
                        )}
                      </div>
                      <div className={`text-xs transition-colors duration-200 whitespace-nowrap ${
                        isActive ? 'text-accent-cyan' : 'text-text-secondary'
                      }`}>
                        {contextualSuggestion ? contextualSuggestion.reason : item.description}
                      </div>
                    </div>
                  </div>
                </Link>
              );
            })}
          </nav>
        </div>
      </div>
    </aside>
  );
}

