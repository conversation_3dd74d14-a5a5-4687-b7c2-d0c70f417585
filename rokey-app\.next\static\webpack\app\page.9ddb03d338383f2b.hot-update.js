"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/RoutingVisualization.tsx":
/*!*********************************************************!*\
  !*** ./src/components/landing/RoutingVisualization.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RoutingVisualization)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,CpuChipIcon,PencilIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,CpuChipIcon,PencilIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,CpuChipIcon,PencilIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CodeBracketIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,CpuChipIcon,PencilIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,CpuChipIcon,PencilIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,CpuChipIcon,PencilIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,CpuChipIcon,PencilIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst routingExamples = [\n    {\n        id: 1,\n        prompt: \"Solve this complex math problem: 2x + 5 = 15\",\n        role: \"logic_reasoning\",\n        roleName: \"Logic & Reasoning\",\n        model: \"GPT-4o\",\n        provider: \"OpenAI\",\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        color: \"text-blue-400\",\n        bgColor: \"bg-blue-500/10\",\n        borderColor: \"border-blue-500/20\",\n        glowColor: \"shadow-blue-500/50\"\n    },\n    {\n        id: 2,\n        prompt: \"Write a blog post about AI trends\",\n        role: \"writing\",\n        roleName: \"Writing & Content Creation\",\n        model: \"GPT-4o\",\n        provider: \"OpenAI\",\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        color: \"text-purple-400\",\n        bgColor: \"bg-purple-500/10\",\n        borderColor: \"border-purple-500/20\",\n        glowColor: \"shadow-purple-500/50\"\n    },\n    {\n        id: 3,\n        prompt: \"Build a React component with TypeScript\",\n        role: \"coding_frontend\",\n        roleName: \"Frontend Development\",\n        model: \"Claude 4 Opus\",\n        provider: \"Anthropic\",\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        color: \"text-green-400\",\n        bgColor: \"bg-green-500/10\",\n        borderColor: \"border-green-500/20\",\n        glowColor: \"shadow-green-500/50\"\n    },\n    {\n        id: 4,\n        prompt: \"Summarize this research paper\",\n        role: \"research_synthesis\",\n        roleName: \"Research & Analysis\",\n        model: \"DeepSeek R1 0528\",\n        provider: \"DeepSeek\",\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        color: \"text-cyan-400\",\n        bgColor: \"bg-cyan-500/10\",\n        borderColor: \"border-cyan-500/20\",\n        glowColor: \"shadow-cyan-500/50\"\n    }\n];\nfunction RoutingVisualization() {\n    _s();\n    const [activeExample, setActiveExample] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [isAnimating, setIsAnimating] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"RoutingVisualization.useEffect\": ()=>{\n            const interval = setInterval({\n                \"RoutingVisualization.useEffect.interval\": ()=>{\n                    setIsAnimating(true);\n                    setTimeout({\n                        \"RoutingVisualization.useEffect.interval\": ()=>{\n                            setActiveExample({\n                                \"RoutingVisualization.useEffect.interval\": (prev)=>(prev + 1) % routingExamples.length\n                            }[\"RoutingVisualization.useEffect.interval\"]);\n                            setIsAnimating(false);\n                        }\n                    }[\"RoutingVisualization.useEffect.interval\"], 500);\n                }\n            }[\"RoutingVisualization.useEffect.interval\"], 5000); // Slower transition for better viewing\n            return ({\n                \"RoutingVisualization.useEffect\": ()=>clearInterval(interval)\n            })[\"RoutingVisualization.useEffect\"];\n        }\n    }[\"RoutingVisualization.useEffect\"], []);\n    const currentExample = routingExamples[activeExample];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"py-20 bg-gradient-to-br from-slate-900 via-gray-900 to-slate-800 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"2edd7b34c21e4641\",\n                children: \"@-webkit-keyframes flowCurrent{0%{-webkit-transform:translatex(-100%);transform:translatex(-100%);opacity:0}20%{opacity:1}80%{opacity:1}100%{-webkit-transform:translatex(200%);transform:translatex(200%);opacity:0}}@-moz-keyframes flowCurrent{0%{-moz-transform:translatex(-100%);transform:translatex(-100%);opacity:0}20%{opacity:1}80%{opacity:1}100%{-moz-transform:translatex(200%);transform:translatex(200%);opacity:0}}@-o-keyframes flowCurrent{0%{-o-transform:translatex(-100%);transform:translatex(-100%);opacity:0}20%{opacity:1}80%{opacity:1}100%{-o-transform:translatex(200%);transform:translatex(200%);opacity:0}}@keyframes flowCurrent{0%{-webkit-transform:translatex(-100%);-moz-transform:translatex(-100%);-o-transform:translatex(-100%);transform:translatex(-100%);opacity:0}20%{opacity:1}80%{opacity:1}100%{-webkit-transform:translatex(200%);-moz-transform:translatex(200%);-o-transform:translatex(200%);transform:translatex(200%);opacity:0}}.current-flow.jsx-2edd7b34c21e4641{-webkit-animation:flowCurrent 3s ease-in-out infinite;-moz-animation:flowCurrent 3s ease-in-out infinite;-o-animation:flowCurrent 3s ease-in-out infinite;animation:flowCurrent 3s ease-in-out infinite}.current-flow-delayed.jsx-2edd7b34c21e4641{-webkit-animation:flowCurrent 3s ease-in-out infinite;-moz-animation:flowCurrent 3s ease-in-out infinite;-o-animation:flowCurrent 3s ease-in-out infinite;animation:flowCurrent 3s ease-in-out infinite;-webkit-animation-delay:1.5s;-moz-animation-delay:1.5s;-o-animation-delay:1.5s;animation-delay:1.5s}\"\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 opacity-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 bg-grid-white/[0.02] bg-[size:60px_60px]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.h2, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"text-4xl sm:text-5xl font-bold text-white mb-6\",\n                                children: \"Introducing the AI Gateway Pattern\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.p, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    delay: 0.1\n                                },\n                                className: \"text-xl text-gray-300 max-w-3xl mx-auto\",\n                                children: \"Watch how RouKey intelligently routes your requests through our unified gateway to the perfect AI model\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"relative max-w-7xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 bg-gradient-to-r from-[#ff6b35]/5 via-transparent to-green-500/5 rounded-3xl blur-3xl\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"relative grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-16 items-start\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: -50\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        transition: {\n                                            duration: 0.6\n                                        },\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-xl font-semibold text-white mb-2\",\n                                                        children: \"User Prompt\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-sm text-gray-400\",\n                                                        children: \"Your request enters RouKey\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"bg-slate-800 border-2 \".concat(currentExample.borderColor, \" rounded-xl p-6 relative \").concat(currentExample.bgColor, \" shadow-lg \").concat(currentExample.glowColor),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 rounded-xl border-2 border-dashed border-gray-500 opacity-30 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"relative z-10\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-sm text-gray-400 mb-3\",\n                                                                children: \"Incoming Request\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 175,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-white font-medium mb-4 text-lg leading-relaxed\",\n                                                                children: [\n                                                                    '\"',\n                                                                    currentExample.prompt,\n                                                                    '\"'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 176,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"flex items-center text-sm text-gray-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-2 h-2 bg-[#ff6b35] rounded-full mr-2 animate-pulse\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 180,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    \"Analyzing prompt...\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 179,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"flex space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-1 h-8 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 188,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.2s'\n                                                                    },\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-1 h-6 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 189,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.4s'\n                                                                    },\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-1 h-4 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 190,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 187,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute right-0 top-1/2 transform translate-x-full -translate-y-1/2 hidden lg:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"relative w-16 h-px\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 bg-gradient-to-r from-[#ff6b35] to-transparent opacity-60\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute top-0 left-0 w-4 h-px bg-gradient-to-r from-white to-[#ff6b35] flow-right shadow-lg shadow-orange-500/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 199,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                animationDelay: '0.5s'\n                                                            },\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute top-0 left-0 w-2 h-px bg-white flow-right shadow-sm shadow-white/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.9\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            scale: 1\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 0.3\n                                        },\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-xl font-semibold text-white mb-2\",\n                                                        children: \"Intelligent Role Classification\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-sm text-gray-400\",\n                                                        children: \"AI analyzes & routes to optimal model\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"bg-slate-800 border-2 border-slate-700 rounded-xl p-6 relative shadow-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 rounded-xl border-2 border-dashed border-[#ff6b35] opacity-40 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"relative z-10 space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center mb-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-16 h-16 bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-lg shadow-orange-500/30\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                            className: \"w-8 h-8 text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                            lineNumber: 227,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 226,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-sm text-gray-400\",\n                                                                        children: \"Gemini 2.0 Flash Classifier\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 229,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 225,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"\".concat(currentExample.bgColor, \" \").concat(currentExample.borderColor, \" border-2 rounded-lg p-4 text-center shadow-lg \").concat(currentExample.glowColor),\n                                                                children: [\n                                                                    /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().createElement(currentExample.icon, {\n                                                                        className: \"h-6 w-6 \".concat(currentExample.color, \" mx-auto mb-2\")\n                                                                    }),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-xs text-gray-400 mb-1\",\n                                                                        children: \"Classified as\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 237,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"font-semibold text-sm \".concat(currentExample.color),\n                                                                        children: currentExample.roleName\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 238,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-xs text-gray-500 mt-1\",\n                                                                        children: [\n                                                                            \"Role ID: \",\n                                                                            currentExample.role\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 241,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"grid grid-cols-2 gap-2 text-xs\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"bg-slate-700 rounded p-2 text-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-green-400 mb-1\",\n                                                                                children: \"✓ Context Analysis\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 249,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-gray-500\",\n                                                                                children: \"Complete\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 250,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 248,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"bg-slate-700 rounded p-2 text-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-green-400 mb-1\",\n                                                                                children: \"✓ Role Matching\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 253,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-gray-500\",\n                                                                                children: \"Complete\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 254,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 252,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 247,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute left-0 top-1/2 transform -translate-x-1/2 -translate-y-1/2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"flex space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.6s'\n                                                                    },\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-1 h-4 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 262,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.4s'\n                                                                    },\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-1 h-6 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 263,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.2s'\n                                                                    },\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-1 h-8 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 264,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"flex space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-1 h-8 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 269,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.2s'\n                                                                    },\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-1 h-6 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 270,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.4s'\n                                                                    },\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-1 h-4 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 271,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute left-0 top-1/2 transform -translate-x-full -translate-y-1/2 hidden lg:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"relative w-16 h-px\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 bg-gradient-to-r from-transparent via-[#ff6b35] to-transparent opacity-60\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 279,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute top-0 right-0 w-4 h-px bg-gradient-to-l from-white to-[#ff6b35] flow-left shadow-lg shadow-orange-500/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                animationDelay: '0.3s'\n                                                            },\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute top-0 right-0 w-2 h-px bg-white flow-left shadow-sm shadow-white/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute right-0 top-1/2 transform translate-x-full -translate-y-1/2 hidden lg:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"relative w-16 h-px\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 bg-gradient-to-r from-[#ff6b35] to-transparent opacity-60\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute top-0 left-0 w-4 h-px bg-gradient-to-r from-white to-[#ff6b35] flow-right shadow-lg shadow-orange-500/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                animationDelay: '0.7s'\n                                                            },\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute top-0 left-0 w-2 h-px bg-white flow-right shadow-sm shadow-white/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, \"classification-\".concat(activeExample), true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: 50\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 0.6\n                                        },\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-xl font-semibold text-white mb-2\",\n                                                        children: \"Optimal Model Selection\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-sm text-gray-400\",\n                                                        children: \"Routed to the perfect model\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"bg-gradient-to-br from-slate-800 to-slate-900 border-2 border-green-500/30 rounded-xl p-6 relative shadow-xl shadow-green-500/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 rounded-xl border-2 border-dashed border-green-400 opacity-50 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 308,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"relative z-10\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center mb-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-lg shadow-green-500/30\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                            className: \"w-8 h-8 text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                            lineNumber: 314,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 313,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-lg font-bold text-white mb-1\",\n                                                                        children: currentExample.model\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 316,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-sm text-green-400 font-medium\",\n                                                                        children: currentExample.provider\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 317,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 312,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"\".concat(currentExample.bgColor, \" \").concat(currentExample.borderColor, \" border-2 rounded-lg p-4 text-center mb-4 shadow-lg \").concat(currentExample.glowColor),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-xs text-gray-400 mb-1\",\n                                                                        children: \"Assigned Role\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 322,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"font-semibold text-sm \".concat(currentExample.color),\n                                                                        children: currentExample.roleName\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 323,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 321,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"grid grid-cols-2 gap-2 text-xs\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"bg-slate-700 rounded p-2 text-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-green-400 mb-1\",\n                                                                                children: \"⚡ Speed\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 331,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-white font-medium\",\n                                                                                children: \"Optimal\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 332,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 330,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"bg-slate-700 rounded p-2 text-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-blue-400 mb-1\",\n                                                                                children: \"\\uD83C\\uDFAF Accuracy\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 335,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-white font-medium\",\n                                                                                children: \"High\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 336,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 334,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"mt-4 flex items-center justify-center text-green-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"w-4 h-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 342,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-sm font-medium\",\n                                                                        children: \"Route Established\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 343,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 341,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute left-0 top-1/2 transform -translate-x-1/2 -translate-y-1/2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"flex space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.8s'\n                                                                    },\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-1 h-4 bg-green-400 rounded-full animate-pulse shadow-lg shadow-green-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 350,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.6s'\n                                                                    },\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-1 h-6 bg-green-400 rounded-full animate-pulse shadow-lg shadow-green-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 351,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.4s'\n                                                                    },\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-1 h-8 bg-green-400 rounded-full animate-pulse shadow-lg shadow-green-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 352,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute left-0 top-1/2 transform -translate-x-full -translate-y-1/2 hidden lg:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"relative w-16 h-px\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 bg-gradient-to-r from-transparent via-green-400 to-green-400 opacity-60\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute top-0 right-0 w-4 h-px bg-gradient-to-l from-white to-green-400 flow-left shadow-lg shadow-green-500/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                animationDelay: '1s'\n                                                            },\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute top-0 right-0 w-2 h-px bg-white flow-left shadow-sm shadow-white/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, \"model-\".concat(activeExample), true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"flex justify-center mt-12 space-x-2\",\n                                children: routingExamples.map((example, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveExample(index),\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-3 h-3 rounded-full transition-all duration-200 \".concat(index === activeExample ? 'bg-[#ff6b35] scale-125' : 'bg-gray-500 hover:bg-gray-400')\n                                    }, example.id, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-8 mt-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center mx-auto mb-4 shadow-lg shadow-blue-500/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-6 w-6 text-blue-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-lg font-semibold text-white mb-2\",\n                                        children: \"Role-Based Classification\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-gray-400\",\n                                        children: \"Gemini 2.0 Flash analyzes context and classifies requests into 15+ specialized roles\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center mx-auto mb-4 shadow-lg shadow-green-500/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-6 w-6 text-green-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-lg font-semibold text-white mb-2\",\n                                        children: \"Smart Model Matching\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-gray-400\",\n                                        children: \"Each role routes to your pre-configured optimal model for maximum performance\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center mx-auto mb-4 shadow-lg shadow-purple-500/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-6 w-6 text-purple-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-lg font-semibold text-white mb-2\",\n                                        children: \"Contextual Continuity\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-gray-400\",\n                                        children: \"Maintains conversation context and role consistency across multi-turn interactions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-12 h-12 bg-orange-500/20 rounded-lg flex items-center justify-center mx-auto mb-4 shadow-lg shadow-orange-500/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-6 w-6 text-orange-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-lg font-semibold text-white mb-2\",\n                                        children: \"Fallback Protection\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-gray-400\",\n                                        children: \"Automatic fallback to default general chat model when no role match is found\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 415,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 385,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            delay: 0.2\n                        },\n                        className: \"mt-16 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-xl font-semibold text-white mb-8\",\n                                children: \"Supported Role Types\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 432,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 max-w-4xl mx-auto\",\n                                children: [\n                                    {\n                                        name: 'General Chat',\n                                        color: 'text-gray-400'\n                                    },\n                                    {\n                                        name: 'Logic & Reasoning',\n                                        color: 'text-blue-400'\n                                    },\n                                    {\n                                        name: 'Writing & Content',\n                                        color: 'text-purple-400'\n                                    },\n                                    {\n                                        name: 'Frontend Coding',\n                                        color: 'text-green-400'\n                                    },\n                                    {\n                                        name: 'Backend Coding',\n                                        color: 'text-emerald-400'\n                                    },\n                                    {\n                                        name: 'Research & Analysis',\n                                        color: 'text-cyan-400'\n                                    },\n                                    {\n                                        name: 'Summarization',\n                                        color: 'text-yellow-400'\n                                    },\n                                    {\n                                        name: 'Translation',\n                                        color: 'text-pink-400'\n                                    },\n                                    {\n                                        name: 'Data Extraction',\n                                        color: 'text-indigo-400'\n                                    },\n                                    {\n                                        name: 'Brainstorming',\n                                        color: 'text-orange-400'\n                                    },\n                                    {\n                                        name: 'Education',\n                                        color: 'text-red-400'\n                                    },\n                                    {\n                                        name: 'Audio Transcription',\n                                        color: 'text-teal-400'\n                                    }\n                                ].map((role, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"bg-slate-800 rounded-lg p-3 border border-slate-700\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-sm font-medium \".concat(role.color),\n                                            children: role.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, role.name, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 433,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 425,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\n_s(RoutingVisualization, \"SlEVdab/r4hG20wt70ej7grkDC0=\");\n_c = RoutingVisualization;\nvar _c;\n$RefreshReg$(_c, \"RoutingVisualization\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/RoutingVisualization.tsx\n"));

/***/ })

});