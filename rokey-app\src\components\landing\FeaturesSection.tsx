'use client';

import { motion } from 'framer-motion';
import { 
  BoltIcon,
  ShieldCheckIcon,
  ChartBarIcon,
  CpuChipIcon,
  ArrowPathIcon,
  ClockIcon,
  CurrencyDollarIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline';

const features = [
  {
    icon: BoltIcon,
    title: "Intelligent Role Routing",
    description: "AI automatically classifies your prompts and routes them to the best model for each specific task - writing, coding, logic, or analysis.",
    color: "text-blue-600",
    bgColor: "bg-blue-50"
  },
  {
    icon: ArrowPathIcon,
    title: "Automatic Failover",
    description: "Never worry about API limits or downtime. RouKey automatically retries failed requests with alternative models in your configuration.",
    color: "text-green-600",
    bgColor: "bg-green-50"
  },
  {
    icon: ShieldCheckIcon,
    title: "Enterprise Security",
    description: "Military-grade AES-256-GCM encryption for all API keys. Your credentials are stored securely and never exposed.",
    color: "text-purple-600",
    bgColor: "bg-purple-50"
  },
  {
    icon: ChartBarIcon,
    title: "Comprehensive Analytics",
    description: "Track costs, performance, token usage, and success rates across all your models with real-time dashboards and insights.",
    color: "text-orange-600",
    bgColor: "bg-orange-50"
  },
  {
    icon: ClockIcon,
    title: "Performance Optimization",
    description: "First-token tracking, latency monitoring, and intelligent caching ensure blazing-fast response times under 500ms.",
    color: "text-indigo-600",
    bgColor: "bg-indigo-50"
  },
  {
    icon: CurrencyDollarIcon,
    title: "Cost Optimization",
    description: "Automatic free-tier detection, budget alerts, and cost tracking help you optimize spending across all AI providers.",
    color: "text-emerald-600",
    bgColor: "bg-emerald-50"
  },
  {
    icon: CpuChipIcon,
    title: "300+ AI Models",
    description: "Access the latest models from OpenAI, Google, Anthropic, DeepSeek, xAI, Meta, and hundreds more through one unified API.",
    color: "text-red-600",
    bgColor: "bg-red-50"
  },
  {
    icon: Cog6ToothIcon,
    title: "Advanced Routing Strategies",
    description: "Load balancing, complexity-based routing, strict fallback sequences, and custom rules for enterprise-grade control.",
    color: "text-cyan-600",
    bgColor: "bg-cyan-50"
  }
];

export default function FeaturesSection() {
  return (
    <section id="features" className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4"
          >
            Everything You Need for AI Model Management
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.1 }}
            className="text-xl text-gray-600 max-w-3xl mx-auto"
          >
            RouKey provides a complete infrastructure for managing, routing, and optimizing your AI model usage
          </motion.p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.1 }}
              className="bg-white rounded-xl p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200"
            >
              <div className={`w-12 h-12 ${feature.bgColor} rounded-lg flex items-center justify-center mb-4`}>
                <feature.icon className={`h-6 w-6 ${feature.color}`} />
              </div>
              
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                {feature.title}
              </h3>
              
              <p className="text-gray-600 text-sm leading-relaxed">
                {feature.description}
              </p>
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">
              Ready to Optimize Your AI Workflow?
            </h3>
            <p className="text-lg opacity-90 mb-6 max-w-2xl mx-auto">
              Join thousands of developers who trust RouKey to manage their AI infrastructure
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-white text-[#ff6b35] px-8 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors"
            >
              Start Your Free Trial
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
