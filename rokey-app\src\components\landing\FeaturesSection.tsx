'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  BoltIcon,
  ShieldCheckIcon,
  ChartBarIcon,
  CpuChipIcon,
  ArrowPathIcon,
  ClockIcon,
  CurrencyDollarIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline';

const features = [
  {
    icon: BoltIcon,
    title: "Intelligent Role Routing",
    description: "AI automatically classifies your prompts and routes them to the best model for each specific task - writing, coding, logic, or analysis.",
    color: "text-blue-600",
    bgColor: "bg-blue-50"
  },
  {
    icon: ArrowPathIcon,
    title: "Automatic Failover",
    description: "Never worry about API limits or downtime. RouKey automatically retries failed requests with alternative models in your configuration.",
    color: "text-green-600",
    bgColor: "bg-green-50"
  },
  {
    icon: ShieldCheckIcon,
    title: "Enterprise Security",
    description: "Military-grade AES-256-GCM encryption for all API keys. Your credentials are stored securely and never exposed.",
    color: "text-purple-600",
    bgColor: "bg-purple-50"
  },
  {
    icon: ChartBarIcon,
    title: "Comprehensive Analytics",
    description: "Track costs, performance, token usage, and success rates across all your models with real-time dashboards and insights.",
    color: "text-orange-600",
    bgColor: "bg-orange-50"
  },
  {
    icon: ClockIcon,
    title: "Performance Optimization",
    description: "First-token tracking, latency monitoring, and intelligent caching ensure blazing-fast response times under 500ms.",
    color: "text-indigo-600",
    bgColor: "bg-indigo-50"
  },
  {
    icon: CurrencyDollarIcon,
    title: "Cost Optimization",
    description: "Automatic free-tier detection, budget alerts, and cost tracking help you optimize spending across all AI providers.",
    color: "text-emerald-600",
    bgColor: "bg-emerald-50"
  },
  {
    icon: CpuChipIcon,
    title: "300+ AI Models",
    description: "Access the latest models from OpenAI, Google, Anthropic, DeepSeek, xAI, Meta, and hundreds more through one unified API.",
    color: "text-red-600",
    bgColor: "bg-red-50"
  },
  {
    icon: Cog6ToothIcon,
    title: "Advanced Routing Strategies",
    description: "Load balancing, complexity-based routing, strict fallback sequences, and custom rules for enterprise-grade control.",
    color: "text-cyan-600",
    bgColor: "bg-cyan-50"
  }
];

export default function FeaturesSection() {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <section id="features" className="py-24 bg-gradient-to-br from-gray-900 to-black relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div
          className="absolute inset-0 opacity-10"
          style={{
            backgroundImage: `
              linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: '80px 80px'
          }}
        ></div>
        <div className="absolute top-0 right-0 w-96 h-96 bg-[#ff6b35]/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-[#f7931e]/5 rounded-full blur-3xl"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Section Header - Left Aligned */}
        <div className="text-left mb-20 max-w-4xl">
          <motion.h2
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            className="text-5xl sm:text-6xl font-bold text-white mb-6 leading-tight"
          >
            Enterprise-Grade
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]">
              {' '}AI Infrastructure
            </span>
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2 }}
            className="text-xl text-gray-300 max-w-3xl leading-relaxed"
          >
            RouKey provides military-grade security, intelligent routing, and comprehensive analytics
            for the most demanding AI workloads. Built for scale, designed for performance.
          </motion.p>
        </div>

        {/* Interactive Card Stack Features */}
        <div className="relative max-w-6xl mx-auto">
          {/* Card Stack Container */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="relative h-[700px] flex items-center justify-center"
            style={{ perspective: '1000px' }}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          >
            {/* Background glow */}
            <div className={`absolute inset-0 bg-gradient-to-r from-[#ff6b35]/10 via-transparent to-[#f7931e]/10 rounded-3xl blur-3xl transition-all duration-700 ${isHovered ? 'from-[#ff6b35]/20 to-[#f7931e]/20' : ''}`}></div>

            {features.map((feature, index) => {
              // Calculate positions for stacked and spread states
              const getTransform = (spread: boolean) => {
                if (spread) {
                  const angle = (index - 3.5) * 18; // Spread in a fan
                  const radius = 320;
                  const x = Math.sin((angle * Math.PI) / 180) * radius;
                  const y = Math.cos((angle * Math.PI) / 180) * radius - radius + 120;
                  return {
                    x,
                    y,
                    rotateY: angle,
                    rotateX: Math.abs(angle) * 0.2,
                    scale: 1
                  };
                } else {
                  return {
                    x: index * 8,
                    y: index * 4,
                    rotateY: index * 2,
                    rotateX: 0,
                    scale: 1
                  };
                }
              };

              const transform = getTransform(isHovered);

              return (
                <motion.div
                  key={feature.title}
                  className="absolute w-80 h-96 cursor-pointer"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{
                    opacity: 1,
                    x: transform.x,
                    y: transform.y,
                    rotateY: transform.rotateY,
                    rotateX: transform.rotateX,
                    scale: transform.scale
                  }}
                  whileInView={{ opacity: 1 }}
                  viewport={{ once: true }}
                  transition={{
                    delay: isHovered ? 0 : index * 0.1,
                    duration: isHovered ? 0.8 : 0.6,
                    type: "spring",
                    stiffness: isHovered ? 80 : 100,
                    damping: isHovered ? 20 : 15
                  }}
                  style={{
                    zIndex: isHovered ? (index === Math.floor(features.length / 2) ? 50 : features.length - Math.abs(index - Math.floor(features.length / 2))) : features.length - index,
                    transformStyle: 'preserve-3d'
                  }}
                  whileHover={{
                    scale: 1.05,
                    zIndex: 100,
                    transition: { duration: 0.3 }
                  }}
                >
                  {/* Card */}
                  <div
                    className="w-full h-full bg-gradient-to-br from-gray-800/90 to-gray-900/90 backdrop-blur-xl rounded-3xl border border-gray-700 hover:border-[#ff6b35]/50 transition-all duration-500 shadow-2xl hover:shadow-orange-500/20 relative overflow-hidden group/card"
                    style={{
                      transform: 'translateZ(0)',
                      backfaceVisibility: 'hidden'
                    }}
                  >
                    {/* Card background pattern */}
                    <div
                      className="absolute inset-0 opacity-5"
                      style={{
                        backgroundImage: `
                          linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),
                          linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)
                        `,
                        backgroundSize: '20px 20px'
                      }}
                    ></div>

                    {/* Card content */}
                    <div className="relative z-10 p-8 h-full flex flex-col">
                      {/* Icon */}
                      <div className={`w-20 h-20 ${index % 3 === 0 ? 'bg-gradient-to-br from-[#ff6b35] to-[#f7931e]' : index % 3 === 1 ? 'bg-gradient-to-br from-gray-600 to-gray-700' : 'bg-gradient-to-br from-[#f7931e] to-[#ff6b35]'} rounded-2xl flex items-center justify-center mb-6 group-hover/card:scale-110 transition-transform duration-300 shadow-lg ${index % 3 === 0 || index % 3 === 2 ? 'shadow-orange-500/30' : 'shadow-gray-500/30'}`}>
                        <feature.icon className="h-10 w-10 text-white" />
                      </div>

                      {/* Title */}
                      <h3 className="text-2xl font-bold text-white mb-4 group-hover/card:text-[#ff6b35] transition-colors duration-300">
                        {feature.title}
                      </h3>

                      {/* Description */}
                      <p className="text-gray-300 leading-relaxed flex-1 text-lg">
                        {feature.description}
                      </p>

                      {/* Bottom accent */}
                      <div className="mt-6 h-1 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-full transform scale-x-0 group-hover/card:scale-x-100 transition-transform duration-500"></div>
                    </div>

                    {/* Hover glow effect */}
                    <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-[#ff6b35]/10 to-[#f7931e]/10 opacity-0 group-hover/card:opacity-100 transition-opacity duration-500 pointer-events-none"></div>

                    {/* Shine effect */}
                    <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-transparent via-white/5 to-transparent transform -skew-x-12 -translate-x-full group-hover/card:translate-x-full transition-transform duration-1000 pointer-events-none"></div>
                  </div>
                </motion.div>
              );
            })}

            {/* Interactive instruction */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.5 }}
              className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-20 text-center"
            >
              <div className="bg-gray-800/90 backdrop-blur-sm rounded-full px-8 py-4 border border-gray-700 shadow-xl">
                <motion.p
                  className="text-gray-300 text-sm font-medium"
                  animate={{
                    color: isHovered ? '#ff6b35' : '#d1d5db'
                  }}
                  transition={{ duration: 0.3 }}
                >
                  {isHovered ? (
                    <>
                      <span className="text-[#ff6b35]">✨ Exploring</span> our features
                    </>
                  ) : (
                    <>
                      <span className="text-[#ff6b35]">Hover</span> to spread the cards
                    </>
                  )}
                </motion.p>
              </div>
            </motion.div>


          </motion.div>
        </div>

        {/* Premium Bottom CTA - Right Aligned */}
        <motion.div
          initial={{ opacity: 0, x: 50 }}
          whileInView={{ opacity: 1, x: 0 }}
          viewport={{ once: true }}
          className="mt-24 flex justify-end"
        >
          <div className="max-w-2xl bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-3xl p-12 text-white relative overflow-hidden shadow-2xl shadow-orange-500/25">
            {/* Background pattern */}
            <div
              className="absolute inset-0 opacity-10"
              style={{
                backgroundImage: `
                  linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                  linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px)
                `,
                backgroundSize: '20px 20px'
              }}
            ></div>

            <div className="relative z-10 text-right">
              <h3 className="text-4xl font-bold mb-6 leading-tight">
                Ready to Transform
                <br />Your AI Infrastructure?
              </h3>
              <p className="text-xl opacity-90 mb-8 leading-relaxed">
                Join the next generation of developers building with RouKey's
                intelligent AI gateway. Start routing smarter today.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-end">
                <motion.button
                  whileHover={{ scale: 1.05, boxShadow: "0 20px 40px rgba(0,0,0,0.3)" }}
                  whileTap={{ scale: 0.95 }}
                  className="bg-white text-[#ff6b35] px-8 py-4 rounded-xl font-bold hover:bg-gray-50 transition-all duration-300 shadow-lg"
                >
                  Start Building Now
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="border-2 border-white text-white px-8 py-4 rounded-xl font-bold hover:bg-white hover:text-[#ff6b35] transition-all duration-300"
                >
                  View Documentation
                </motion.button>
              </div>
            </div>

            {/* Decorative elements */}
            <div className="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
            <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
