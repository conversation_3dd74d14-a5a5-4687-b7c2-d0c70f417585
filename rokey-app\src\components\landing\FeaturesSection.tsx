'use client';

import { motion } from 'framer-motion';
import { 
  BoltIcon,
  ShieldCheckIcon,
  ChartBarIcon,
  CpuChipIcon,
  ArrowPathIcon,
  ClockIcon,
  CurrencyDollarIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline';

const features = [
  {
    icon: BoltIcon,
    title: "Intelligent Role Routing",
    description: "AI automatically classifies your prompts and routes them to the best model for each specific task - writing, coding, logic, or analysis.",
    color: "text-blue-600",
    bgColor: "bg-blue-50"
  },
  {
    icon: ArrowPathIcon,
    title: "Automatic Failover",
    description: "Never worry about API limits or downtime. RouKey automatically retries failed requests with alternative models in your configuration.",
    color: "text-green-600",
    bgColor: "bg-green-50"
  },
  {
    icon: ShieldCheckIcon,
    title: "Enterprise Security",
    description: "Military-grade AES-256-GCM encryption for all API keys. Your credentials are stored securely and never exposed.",
    color: "text-purple-600",
    bgColor: "bg-purple-50"
  },
  {
    icon: ChartBarIcon,
    title: "Comprehensive Analytics",
    description: "Track costs, performance, token usage, and success rates across all your models with real-time dashboards and insights.",
    color: "text-orange-600",
    bgColor: "bg-orange-50"
  },
  {
    icon: ClockIcon,
    title: "Performance Optimization",
    description: "First-token tracking, latency monitoring, and intelligent caching ensure blazing-fast response times under 500ms.",
    color: "text-indigo-600",
    bgColor: "bg-indigo-50"
  },
  {
    icon: CurrencyDollarIcon,
    title: "Cost Optimization",
    description: "Automatic free-tier detection, budget alerts, and cost tracking help you optimize spending across all AI providers.",
    color: "text-emerald-600",
    bgColor: "bg-emerald-50"
  },
  {
    icon: CpuChipIcon,
    title: "300+ AI Models",
    description: "Access the latest models from OpenAI, Google, Anthropic, DeepSeek, xAI, Meta, and hundreds more through one unified API.",
    color: "text-red-600",
    bgColor: "bg-red-50"
  },
  {
    icon: Cog6ToothIcon,
    title: "Advanced Routing Strategies",
    description: "Load balancing, complexity-based routing, strict fallback sequences, and custom rules for enterprise-grade control.",
    color: "text-cyan-600",
    bgColor: "bg-cyan-50"
  }
];

export default function FeaturesSection() {
  return (
    <section id="features" className="py-24 bg-gradient-to-br from-gray-900 to-black relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div
          className="absolute inset-0 opacity-10"
          style={{
            backgroundImage: `
              linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: '80px 80px'
          }}
        ></div>
        <div className="absolute top-0 right-0 w-96 h-96 bg-[#ff6b35]/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-[#f7931e]/5 rounded-full blur-3xl"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Section Header - Left Aligned */}
        <div className="text-left mb-20 max-w-4xl">
          <motion.h2
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            className="text-5xl sm:text-6xl font-bold text-white mb-6 leading-tight"
          >
            Enterprise-Grade
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]">
              {' '}AI Infrastructure
            </span>
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2 }}
            className="text-xl text-gray-300 max-w-3xl leading-relaxed"
          >
            RouKey provides military-grade security, intelligent routing, and comprehensive analytics
            for the most demanding AI workloads. Built for scale, designed for performance.
          </motion.p>
        </div>

        {/* Premium Features Grid - Asymmetric Layout */}
        <div className="space-y-16">
          {/* Row 1 - 3 features */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {features.slice(0, 3).map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.2 }}
                className="group bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-2xl p-8 border border-gray-700 hover:border-[#ff6b35]/50 transition-all duration-300 hover:shadow-2xl hover:shadow-orange-500/10"
              >
                <div className="w-16 h-16 bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg shadow-orange-500/30">
                  <feature.icon className="h-8 w-8 text-white" />
                </div>

                <h3 className="text-xl font-bold text-white mb-4 group-hover:text-[#ff6b35] transition-colors duration-300">
                  {feature.title}
                </h3>

                <p className="text-gray-300 leading-relaxed">
                  {feature.description}
                </p>

                {/* Hover glow effect */}
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-[#ff6b35]/5 to-[#f7931e]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
              </motion.div>
            ))}
          </div>

          {/* Row 2 - 2 features (offset) */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 md:ml-32">
            {features.slice(3, 5).map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: (index + 3) * 0.2 }}
                className="group bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-2xl p-8 border border-gray-700 hover:border-[#ff6b35]/50 transition-all duration-300 hover:shadow-2xl hover:shadow-orange-500/10"
              >
                <div className="w-16 h-16 bg-gradient-to-br from-gray-700 to-gray-800 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                  <feature.icon className="h-8 w-8 text-white" />
                </div>

                <h3 className="text-xl font-bold text-white mb-4 group-hover:text-[#ff6b35] transition-colors duration-300">
                  {feature.title}
                </h3>

                <p className="text-gray-300 leading-relaxed">
                  {feature.description}
                </p>

                {/* Hover glow effect */}
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-[#ff6b35]/5 to-[#f7931e]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
              </motion.div>
            ))}
          </div>

          {/* Row 3 - 3 features */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {features.slice(5, 8).map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: (index + 5) * 0.2 }}
                className="group bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-2xl p-8 border border-gray-700 hover:border-[#ff6b35]/50 transition-all duration-300 hover:shadow-2xl hover:shadow-orange-500/10"
              >
                <div className="w-16 h-16 bg-gradient-to-br from-[#f7931e] to-[#ff6b35] rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg shadow-orange-400/30">
                  <feature.icon className="h-8 w-8 text-white" />
                </div>

                <h3 className="text-xl font-bold text-white mb-4 group-hover:text-[#ff6b35] transition-colors duration-300">
                  {feature.title}
                </h3>

                <p className="text-gray-300 leading-relaxed">
                  {feature.description}
                </p>

                {/* Hover glow effect */}
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-[#ff6b35]/5 to-[#f7931e]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Premium Bottom CTA - Right Aligned */}
        <motion.div
          initial={{ opacity: 0, x: 50 }}
          whileInView={{ opacity: 1, x: 0 }}
          viewport={{ once: true }}
          className="mt-24 flex justify-end"
        >
          <div className="max-w-2xl bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-3xl p-12 text-white relative overflow-hidden shadow-2xl shadow-orange-500/25">
            {/* Background pattern */}
            <div
              className="absolute inset-0 opacity-10"
              style={{
                backgroundImage: `
                  linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                  linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px)
                `,
                backgroundSize: '20px 20px'
              }}
            ></div>

            <div className="relative z-10 text-right">
              <h3 className="text-4xl font-bold mb-6 leading-tight">
                Ready to Transform
                <br />Your AI Infrastructure?
              </h3>
              <p className="text-xl opacity-90 mb-8 leading-relaxed">
                Join the next generation of developers building with RouKey's
                intelligent AI gateway. Start routing smarter today.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-end">
                <motion.button
                  whileHover={{ scale: 1.05, boxShadow: "0 20px 40px rgba(0,0,0,0.3)" }}
                  whileTap={{ scale: 0.95 }}
                  className="bg-white text-[#ff6b35] px-8 py-4 rounded-xl font-bold hover:bg-gray-50 transition-all duration-300 shadow-lg"
                >
                  Start Building Now
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="border-2 border-white text-white px-8 py-4 rounded-xl font-bold hover:bg-white hover:text-[#ff6b35] transition-all duration-300"
                >
                  View Documentation
                </motion.button>
              </div>
            </div>

            {/* Decorative elements */}
            <div className="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
            <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
