"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/FeaturesSection.tsx":
/*!****************************************************!*\
  !*** ./src/components/landing/FeaturesSection.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeaturesSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst features = [\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: \"Intelligent Role Routing\",\n        description: \"AI automatically classifies your prompts and routes them to the best model for each specific task - writing, coding, logic, or analysis.\",\n        color: \"text-blue-600\",\n        bgColor: \"bg-blue-50\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"Automatic Failover\",\n        description: \"Never worry about API limits or downtime. RouKey automatically retries failed requests with alternative models in your configuration.\",\n        color: \"text-green-600\",\n        bgColor: \"bg-green-50\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Enterprise Security\",\n        description: \"Military-grade AES-256-GCM encryption for all API keys. Your credentials are stored securely and never exposed.\",\n        color: \"text-purple-600\",\n        bgColor: \"bg-purple-50\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Comprehensive Analytics\",\n        description: \"Track costs, performance, token usage, and success rates across all your models with real-time dashboards and insights.\",\n        color: \"text-orange-600\",\n        bgColor: \"bg-orange-50\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"Performance Optimization\",\n        description: \"First-token tracking, latency monitoring, and intelligent caching ensure blazing-fast response times under 500ms.\",\n        color: \"text-indigo-600\",\n        bgColor: \"bg-indigo-50\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"Cost Optimization\",\n        description: \"Automatic free-tier detection, budget alerts, and cost tracking help you optimize spending across all AI providers.\",\n        color: \"text-emerald-600\",\n        bgColor: \"bg-emerald-50\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        title: \"300+ AI Models\",\n        description: \"Access the latest models from OpenAI, Google, Anthropic, DeepSeek, xAI, Meta, and hundreds more through one unified API.\",\n        color: \"text-red-600\",\n        bgColor: \"bg-red-50\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        title: \"Advanced Routing Strategies\",\n        description: \"Load balancing, complexity-based routing, strict fallback sequences, and custom rules for enterprise-grade control.\",\n        color: \"text-cyan-600\",\n        bgColor: \"bg-cyan-50\"\n    }\n];\nfunction FeaturesSection() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"features\",\n        className: \"py-24 bg-gradient-to-br from-gray-900 to-black relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-10\",\n                        style: {\n                            backgroundImage: \"\\n              linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),\\n              linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)\\n            \",\n                            backgroundSize: '80px 80px'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 right-0 w-96 h-96 bg-[#ff6b35]/5 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 left-0 w-96 h-96 bg-[#f7931e]/5 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-left mb-20 max-w-4xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.h2, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"text-5xl sm:text-6xl font-bold text-white mb-6 leading-tight\",\n                                children: [\n                                    \"Enterprise-Grade\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]\",\n                                        children: [\n                                            ' ',\n                                            \"AI Infrastructure\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.p, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    delay: 0.2\n                                },\n                                className: \"text-xl text-gray-300 max-w-3xl leading-relaxed\",\n                                children: \"RouKey provides military-grade security, intelligent routing, and comprehensive analytics for the most demanding AI workloads. Built for scale, designed for performance.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-6xl mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 50\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"group relative h-[600px] flex items-center justify-center\",\n                            style: {\n                                perspective: '1000px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                        [\n                                            \"85b09b7a514813c0\",\n                                            [\n                                                features.map((_, i)=>{\n                                                    const angle = (i - 3.5) * 15;\n                                                    const radius = 280;\n                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                })[0],\n                                                features.map((_, i)=>{\n                                                    const angle = (i - 3.5) * 15;\n                                                    const radius = 280;\n                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                })[1],\n                                                features.map((_, i)=>{\n                                                    const angle = (i - 3.5) * 15;\n                                                    const radius = 280;\n                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                })[2],\n                                                features.map((_, i)=>{\n                                                    const angle = (i - 3.5) * 15;\n                                                    const radius = 280;\n                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                })[3],\n                                                features.map((_, i)=>{\n                                                    const angle = (i - 3.5) * 15;\n                                                    const radius = 280;\n                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                })[4],\n                                                features.map((_, i)=>{\n                                                    const angle = (i - 3.5) * 15;\n                                                    const radius = 280;\n                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                })[5],\n                                                features.map((_, i)=>{\n                                                    const angle = (i - 3.5) * 15;\n                                                    const radius = 280;\n                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                })[6],\n                                                features.map((_, i)=>{\n                                                    const angle = (i - 3.5) * 15;\n                                                    const radius = 280;\n                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                })[7]\n                                            ]\n                                        ]\n                                    ]) + \" \" + \"absolute inset-0 bg-gradient-to-r from-[#ff6b35]/10 via-transparent to-[#f7931e]/10 rounded-3xl blur-3xl group-hover:from-[#ff6b35]/20 group-hover:to-[#f7931e]/20 transition-all duration-700\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, this),\n                                features.map((feature, index)=>{\n                                    // Calculate positions for stacked and spread states\n                                    const stackedTransform = \"translateX(\".concat(index * 8, \"px) translateY(\").concat(index * 4, \"px) rotateY(\").concat(index * 2, \"deg)\");\n                                    const spreadTransform = (()=>{\n                                        const angle = (index - 3.5) * 15; // Spread in a fan\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })();\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                        className: \"absolute w-80 h-96 cursor-pointer\",\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.8\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            scale: 1,\n                                            transform: stackedTransform\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            scale: 1\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        transition: {\n                                            delay: index * 0.1,\n                                            duration: 0.6,\n                                            type: \"spring\",\n                                            stiffness: 100\n                                        },\n                                        style: {\n                                            zIndex: features.length - index,\n                                            transformStyle: 'preserve-3d'\n                                        },\n                                        whileHover: {\n                                            scale: 1.05,\n                                            zIndex: 50,\n                                            transition: {\n                                                duration: 0.3\n                                            }\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                transform: 'translateZ(0)',\n                                                backfaceVisibility: 'hidden'\n                                            },\n                                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                                [\n                                                    \"85b09b7a514813c0\",\n                                                    [\n                                                        features.map((_, i)=>{\n                                                            const angle = (i - 3.5) * 15;\n                                                            const radius = 280;\n                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                        })[0],\n                                                        features.map((_, i)=>{\n                                                            const angle = (i - 3.5) * 15;\n                                                            const radius = 280;\n                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                        })[1],\n                                                        features.map((_, i)=>{\n                                                            const angle = (i - 3.5) * 15;\n                                                            const radius = 280;\n                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                        })[2],\n                                                        features.map((_, i)=>{\n                                                            const angle = (i - 3.5) * 15;\n                                                            const radius = 280;\n                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                        })[3],\n                                                        features.map((_, i)=>{\n                                                            const angle = (i - 3.5) * 15;\n                                                            const radius = 280;\n                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                        })[4],\n                                                        features.map((_, i)=>{\n                                                            const angle = (i - 3.5) * 15;\n                                                            const radius = 280;\n                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                        })[5],\n                                                        features.map((_, i)=>{\n                                                            const angle = (i - 3.5) * 15;\n                                                            const radius = 280;\n                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                        })[6],\n                                                        features.map((_, i)=>{\n                                                            const angle = (i - 3.5) * 15;\n                                                            const radius = 280;\n                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                        })[7]\n                                                    ]\n                                                ]\n                                            ]) + \" \" + \"w-full h-full bg-gradient-to-br from-gray-800/90 to-gray-900/90 backdrop-blur-xl rounded-3xl border border-gray-700 hover:border-[#ff6b35]/50 transition-all duration-500 shadow-2xl hover:shadow-orange-500/20 relative overflow-hidden group/card\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        backgroundImage: \"\\n                          linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),\\n                          linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)\\n                        \",\n                                                        backgroundSize: '20px 20px'\n                                                    },\n                                                    className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                                        [\n                                                            \"85b09b7a514813c0\",\n                                                            [\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[0],\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[1],\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[2],\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[3],\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[4],\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[5],\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[6],\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[7]\n                                                            ]\n                                                        ]\n                                                    ]) + \" \" + \"absolute inset-0 opacity-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                                        [\n                                                            \"85b09b7a514813c0\",\n                                                            [\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[0],\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[1],\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[2],\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[3],\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[4],\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[5],\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[6],\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[7]\n                                                            ]\n                                                        ]\n                                                    ]) + \" \" + \"relative z-10 p-8 h-full flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                                                [\n                                                                    \"85b09b7a514813c0\",\n                                                                    [\n                                                                        features.map((_, i)=>{\n                                                                            const angle = (i - 3.5) * 15;\n                                                                            const radius = 280;\n                                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                        })[0],\n                                                                        features.map((_, i)=>{\n                                                                            const angle = (i - 3.5) * 15;\n                                                                            const radius = 280;\n                                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                        })[1],\n                                                                        features.map((_, i)=>{\n                                                                            const angle = (i - 3.5) * 15;\n                                                                            const radius = 280;\n                                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                        })[2],\n                                                                        features.map((_, i)=>{\n                                                                            const angle = (i - 3.5) * 15;\n                                                                            const radius = 280;\n                                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                        })[3],\n                                                                        features.map((_, i)=>{\n                                                                            const angle = (i - 3.5) * 15;\n                                                                            const radius = 280;\n                                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                        })[4],\n                                                                        features.map((_, i)=>{\n                                                                            const angle = (i - 3.5) * 15;\n                                                                            const radius = 280;\n                                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                        })[5],\n                                                                        features.map((_, i)=>{\n                                                                            const angle = (i - 3.5) * 15;\n                                                                            const radius = 280;\n                                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                        })[6],\n                                                                        features.map((_, i)=>{\n                                                                            const angle = (i - 3.5) * 15;\n                                                                            const radius = 280;\n                                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                        })[7]\n                                                                    ]\n                                                                ]\n                                                            ]) + \" \" + \"w-20 h-20 \".concat(index % 3 === 0 ? 'bg-gradient-to-br from-[#ff6b35] to-[#f7931e]' : index % 3 === 1 ? 'bg-gradient-to-br from-gray-600 to-gray-700' : 'bg-gradient-to-br from-[#f7931e] to-[#ff6b35]', \" rounded-2xl flex items-center justify-center mb-6 group-hover/card:scale-110 transition-transform duration-300 shadow-lg \").concat(index % 3 === 0 || index % 3 === 2 ? 'shadow-orange-500/30' : 'shadow-gray-500/30'),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                                className: \"h-10 w-10 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                                                [\n                                                                    \"85b09b7a514813c0\",\n                                                                    [\n                                                                        features.map((_, i)=>{\n                                                                            const angle = (i - 3.5) * 15;\n                                                                            const radius = 280;\n                                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                        })[0],\n                                                                        features.map((_, i)=>{\n                                                                            const angle = (i - 3.5) * 15;\n                                                                            const radius = 280;\n                                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                        })[1],\n                                                                        features.map((_, i)=>{\n                                                                            const angle = (i - 3.5) * 15;\n                                                                            const radius = 280;\n                                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                        })[2],\n                                                                        features.map((_, i)=>{\n                                                                            const angle = (i - 3.5) * 15;\n                                                                            const radius = 280;\n                                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                        })[3],\n                                                                        features.map((_, i)=>{\n                                                                            const angle = (i - 3.5) * 15;\n                                                                            const radius = 280;\n                                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                        })[4],\n                                                                        features.map((_, i)=>{\n                                                                            const angle = (i - 3.5) * 15;\n                                                                            const radius = 280;\n                                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                        })[5],\n                                                                        features.map((_, i)=>{\n                                                                            const angle = (i - 3.5) * 15;\n                                                                            const radius = 280;\n                                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                        })[6],\n                                                                        features.map((_, i)=>{\n                                                                            const angle = (i - 3.5) * 15;\n                                                                            const radius = 280;\n                                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                        })[7]\n                                                                    ]\n                                                                ]\n                                                            ]) + \" \" + \"text-2xl font-bold text-white mb-4 group-hover/card:text-[#ff6b35] transition-colors duration-300\",\n                                                            children: feature.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                            lineNumber: 199,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                                                [\n                                                                    \"85b09b7a514813c0\",\n                                                                    [\n                                                                        features.map((_, i)=>{\n                                                                            const angle = (i - 3.5) * 15;\n                                                                            const radius = 280;\n                                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                        })[0],\n                                                                        features.map((_, i)=>{\n                                                                            const angle = (i - 3.5) * 15;\n                                                                            const radius = 280;\n                                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                        })[1],\n                                                                        features.map((_, i)=>{\n                                                                            const angle = (i - 3.5) * 15;\n                                                                            const radius = 280;\n                                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                        })[2],\n                                                                        features.map((_, i)=>{\n                                                                            const angle = (i - 3.5) * 15;\n                                                                            const radius = 280;\n                                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                        })[3],\n                                                                        features.map((_, i)=>{\n                                                                            const angle = (i - 3.5) * 15;\n                                                                            const radius = 280;\n                                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                        })[4],\n                                                                        features.map((_, i)=>{\n                                                                            const angle = (i - 3.5) * 15;\n                                                                            const radius = 280;\n                                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                        })[5],\n                                                                        features.map((_, i)=>{\n                                                                            const angle = (i - 3.5) * 15;\n                                                                            const radius = 280;\n                                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                        })[6],\n                                                                        features.map((_, i)=>{\n                                                                            const angle = (i - 3.5) * 15;\n                                                                            const radius = 280;\n                                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                        })[7]\n                                                                    ]\n                                                                ]\n                                                            ]) + \" \" + \"text-gray-300 leading-relaxed flex-1 text-lg\",\n                                                            children: feature.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                                                [\n                                                                    \"85b09b7a514813c0\",\n                                                                    [\n                                                                        features.map((_, i)=>{\n                                                                            const angle = (i - 3.5) * 15;\n                                                                            const radius = 280;\n                                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                        })[0],\n                                                                        features.map((_, i)=>{\n                                                                            const angle = (i - 3.5) * 15;\n                                                                            const radius = 280;\n                                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                        })[1],\n                                                                        features.map((_, i)=>{\n                                                                            const angle = (i - 3.5) * 15;\n                                                                            const radius = 280;\n                                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                        })[2],\n                                                                        features.map((_, i)=>{\n                                                                            const angle = (i - 3.5) * 15;\n                                                                            const radius = 280;\n                                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                        })[3],\n                                                                        features.map((_, i)=>{\n                                                                            const angle = (i - 3.5) * 15;\n                                                                            const radius = 280;\n                                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                        })[4],\n                                                                        features.map((_, i)=>{\n                                                                            const angle = (i - 3.5) * 15;\n                                                                            const radius = 280;\n                                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                        })[5],\n                                                                        features.map((_, i)=>{\n                                                                            const angle = (i - 3.5) * 15;\n                                                                            const radius = 280;\n                                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                        })[6],\n                                                                        features.map((_, i)=>{\n                                                                            const angle = (i - 3.5) * 15;\n                                                                            const radius = 280;\n                                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                        })[7]\n                                                                    ]\n                                                                ]\n                                                            ]) + \" \" + \"mt-6 h-1 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-full transform scale-x-0 group-hover/card:scale-x-100 transition-transform duration-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                                        [\n                                                            \"85b09b7a514813c0\",\n                                                            [\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[0],\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[1],\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[2],\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[3],\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[4],\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[5],\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[6],\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[7]\n                                                            ]\n                                                        ]\n                                                    ]) + \" \" + \"absolute inset-0 rounded-3xl bg-gradient-to-r from-[#ff6b35]/10 to-[#f7931e]/10 opacity-0 group-hover/card:opacity-100 transition-opacity duration-500 pointer-events-none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                                        [\n                                                            \"85b09b7a514813c0\",\n                                                            [\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[0],\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[1],\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[2],\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[3],\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[4],\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[5],\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[6],\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[7]\n                                                            ]\n                                                        ]\n                                                    ]) + \" \" + \"absolute inset-0 rounded-3xl bg-gradient-to-r from-transparent via-white/5 to-transparent transform -skew-x-12 -translate-x-full group-hover/card:translate-x-full transition-transform duration-1000 pointer-events-none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, feature.title, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 17\n                                    }, this);\n                                }),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        delay: 1\n                                    },\n                                    className: \"absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-16 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                            [\n                                                \"85b09b7a514813c0\",\n                                                [\n                                                    features.map((_, i)=>{\n                                                        const angle = (i - 3.5) * 15;\n                                                        const radius = 280;\n                                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                    })[0],\n                                                    features.map((_, i)=>{\n                                                        const angle = (i - 3.5) * 15;\n                                                        const radius = 280;\n                                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                    })[1],\n                                                    features.map((_, i)=>{\n                                                        const angle = (i - 3.5) * 15;\n                                                        const radius = 280;\n                                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                    })[2],\n                                                    features.map((_, i)=>{\n                                                        const angle = (i - 3.5) * 15;\n                                                        const radius = 280;\n                                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                    })[3],\n                                                    features.map((_, i)=>{\n                                                        const angle = (i - 3.5) * 15;\n                                                        const radius = 280;\n                                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                    })[4],\n                                                    features.map((_, i)=>{\n                                                        const angle = (i - 3.5) * 15;\n                                                        const radius = 280;\n                                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                    })[5],\n                                                    features.map((_, i)=>{\n                                                        const angle = (i - 3.5) * 15;\n                                                        const radius = 280;\n                                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                    })[6],\n                                                    features.map((_, i)=>{\n                                                        const angle = (i - 3.5) * 15;\n                                                        const radius = 280;\n                                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                    })[7]\n                                                ]\n                                            ]\n                                        ]) + \" \" + \"bg-gray-800/80 backdrop-blur-sm rounded-full px-6 py-3 border border-gray-700\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                                [\n                                                    \"85b09b7a514813c0\",\n                                                    [\n                                                        features.map((_, i)=>{\n                                                            const angle = (i - 3.5) * 15;\n                                                            const radius = 280;\n                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                        })[0],\n                                                        features.map((_, i)=>{\n                                                            const angle = (i - 3.5) * 15;\n                                                            const radius = 280;\n                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                        })[1],\n                                                        features.map((_, i)=>{\n                                                            const angle = (i - 3.5) * 15;\n                                                            const radius = 280;\n                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                        })[2],\n                                                        features.map((_, i)=>{\n                                                            const angle = (i - 3.5) * 15;\n                                                            const radius = 280;\n                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                        })[3],\n                                                        features.map((_, i)=>{\n                                                            const angle = (i - 3.5) * 15;\n                                                            const radius = 280;\n                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                        })[4],\n                                                        features.map((_, i)=>{\n                                                            const angle = (i - 3.5) * 15;\n                                                            const radius = 280;\n                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                        })[5],\n                                                        features.map((_, i)=>{\n                                                            const angle = (i - 3.5) * 15;\n                                                            const radius = 280;\n                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                        })[6],\n                                                        features.map((_, i)=>{\n                                                            const angle = (i - 3.5) * 15;\n                                                            const radius = 280;\n                                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                        })[7]\n                                                    ]\n                                                ]\n                                            ]) + \" \" + \"text-gray-300 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                                        [\n                                                            \"85b09b7a514813c0\",\n                                                            [\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[0],\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[1],\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[2],\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[3],\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[4],\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[5],\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[6],\n                                                                features.map((_, i)=>{\n                                                                    const angle = (i - 3.5) * 15;\n                                                                    const radius = 280;\n                                                                    const x = Math.sin(angle * Math.PI / 180) * radius;\n                                                                    const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                                                    return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                                                })[7]\n                                                            ]\n                                                        ]\n                                                    ]) + \" \" + \"text-[#ff6b35]\",\n                                                    children: \"Hover\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" to explore features\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    id: \"85b09b7a514813c0\",\n                                    dynamic: [\n                                        features.map((_, i)=>{\n                                            const angle = (i - 3.5) * 15;\n                                            const radius = 280;\n                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                        })[0],\n                                        features.map((_, i)=>{\n                                            const angle = (i - 3.5) * 15;\n                                            const radius = 280;\n                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                        })[1],\n                                        features.map((_, i)=>{\n                                            const angle = (i - 3.5) * 15;\n                                            const radius = 280;\n                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                        })[2],\n                                        features.map((_, i)=>{\n                                            const angle = (i - 3.5) * 15;\n                                            const radius = 280;\n                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                        })[3],\n                                        features.map((_, i)=>{\n                                            const angle = (i - 3.5) * 15;\n                                            const radius = 280;\n                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                        })[4],\n                                        features.map((_, i)=>{\n                                            const angle = (i - 3.5) * 15;\n                                            const radius = 280;\n                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                        })[5],\n                                        features.map((_, i)=>{\n                                            const angle = (i - 3.5) * 15;\n                                            const radius = 280;\n                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                        })[6],\n                                        features.map((_, i)=>{\n                                            const angle = (i - 3.5) * 15;\n                                            const radius = 280;\n                                            const x = Math.sin(angle * Math.PI / 180) * radius;\n                                            const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                            return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                        })[7]\n                                    ],\n                                    children: \".group.__jsx-style-dynamic-selector:hover .absolute.__jsx-style-dynamic-selector:nth-child(2){-webkit-transform:\".concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[0], \"!important;-moz-transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[0], \"!important;-ms-transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[0], \"!important;-o-transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[0], \"!important;transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[0], \"!important}.group.__jsx-style-dynamic-selector:hover .absolute.__jsx-style-dynamic-selector:nth-child(3){-webkit-transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[1], \"!important;-moz-transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[1], \"!important;-ms-transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[1], \"!important;-o-transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[1], \"!important;transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[1], \"!important}.group.__jsx-style-dynamic-selector:hover .absolute.__jsx-style-dynamic-selector:nth-child(4){-webkit-transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[2], \"!important;-moz-transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[2], \"!important;-ms-transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[2], \"!important;-o-transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[2], \"!important;transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[2], \"!important}.group.__jsx-style-dynamic-selector:hover .absolute.__jsx-style-dynamic-selector:nth-child(5){-webkit-transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[3], \"!important;-moz-transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[3], \"!important;-ms-transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[3], \"!important;-o-transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[3], \"!important;transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[3], \"!important}.group.__jsx-style-dynamic-selector:hover .absolute.__jsx-style-dynamic-selector:nth-child(6){-webkit-transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[4], \"!important;-moz-transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[4], \"!important;-ms-transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[4], \"!important;-o-transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[4], \"!important;transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[4], \"!important}.group.__jsx-style-dynamic-selector:hover .absolute.__jsx-style-dynamic-selector:nth-child(7){-webkit-transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[5], \"!important;-moz-transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[5], \"!important;-ms-transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[5], \"!important;-o-transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[5], \"!important;transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[5], \"!important}.group.__jsx-style-dynamic-selector:hover .absolute.__jsx-style-dynamic-selector:nth-child(8){-webkit-transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[6], \"!important;-moz-transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[6], \"!important;-ms-transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[6], \"!important;-o-transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[6], \"!important;transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[6], \"!important}.group.__jsx-style-dynamic-selector:hover .absolute.__jsx-style-dynamic-selector:nth-child(9){-webkit-transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[7], \"!important;-moz-transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[7], \"!important;-ms-transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[7], \"!important;-o-transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[7], \"!important;transform:\").concat(features.map((_, i)=>{\n                                        const angle = (i - 3.5) * 15;\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })[7], \"!important}\")\n                                }, void 0, false, void 0, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: 50\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        className: \"mt-24 flex justify-end\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-2xl bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-3xl p-12 text-white relative overflow-hidden shadow-2xl shadow-orange-500/25\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 opacity-10\",\n                                    style: {\n                                        backgroundImage: \"\\n                  linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),\\n                  linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px)\\n                \",\n                                        backgroundSize: '20px 20px'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative z-10 text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-4xl font-bold mb-6 leading-tight\",\n                                            children: [\n                                                \"Ready to Transform\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Your AI Infrastructure?\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl opacity-90 mb-8 leading-relaxed\",\n                                            children: \"Join the next generation of developers building with RouKey's intelligent AI gateway. Start routing smarter today.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row gap-4 justify-end\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.button, {\n                                                    whileHover: {\n                                                        scale: 1.05,\n                                                        boxShadow: \"0 20px 40px rgba(0,0,0,0.3)\"\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.95\n                                                    },\n                                                    className: \"bg-white text-[#ff6b35] px-8 py-4 rounded-xl font-bold hover:bg-gray-50 transition-all duration-300 shadow-lg\",\n                                                    children: \"Start Building Now\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.button, {\n                                                    whileHover: {\n                                                        scale: 1.05\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.95\n                                                    },\n                                                    className: \"border-2 border-white text-white px-8 py-4 rounded-xl font-bold hover:bg-white hover:text-[#ff6b35] transition-all duration-300\",\n                                                    children: \"View Documentation\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full blur-xl\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -bottom-4 -left-4 w-32 h-32 bg-white/5 rounded-full blur-2xl\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n_c = FeaturesSection;\nvar _c;\n$RefreshReg$(_c, \"FeaturesSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/FeaturesSection.tsx\n"));

/***/ })

});