import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import "../styles/design-system.css";
import { SidebarProvider } from "@/contexts/SidebarContext";
import { NavigationProvider } from "@/contexts/NavigationContext";
import LayoutContent from "@/components/LayoutContent";
import DocumentTitleUpdater from "@/components/DocumentTitleUpdater";
import Script from "next/script";

const inter = Inter({
  subsets: ["latin"],
  display: 'swap',
  variable: '--font-inter'
});

export const metadata: Metadata = {
  title: "RouKey - Advanced LLM API Infrastructure",
  description: "Professional LLM API key routing, analytics, and management platform",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={inter.variable}>
      <head>
        {/* Preload critical resources */}
        <link rel="preload" href="/api/custom-configs" as="fetch" crossOrigin="anonymous" />
        <link rel="preload" href="/api/system-status" as="fetch" crossOrigin="anonymous" />

        {/* DNS prefetch for external resources */}
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />

        {/* Preconnect to critical origins */}
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />

        {/* Resource hints for navigation */}
        <link rel="prefetch" href="/dashboard" />
        <link rel="prefetch" href="/playground" />
        <link rel="prefetch" href="/logs" />
        <link rel="prefetch" href="/my-models" />

        {/* PWA manifest */}
        <link rel="manifest" href="/manifest.json" />
      </head>
      <body className="flex h-screen overflow-hidden font-sans antialiased bg-background-primary text-text-primary">
        <SidebarProvider>
          <NavigationProvider>
            <DocumentTitleUpdater />
            <LayoutContent>{children}</LayoutContent>
          </NavigationProvider>
        </SidebarProvider>

        {/* Performance monitoring in development */}
        {process.env.NODE_ENV === 'development' && (
          <Script
            src="/performance-monitor.js"
            strategy="afterInteractive"
          />
        )}

        {/* Service Worker registration */}
        <Script id="sw-register" strategy="afterInteractive">
          {`
            if ('serviceWorker' in navigator) {
              window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                  .then(function(registration) {
                    console.log('SW registered: ', registration);
                  })
                  .catch(function(registrationError) {
                    console.log('SW registration failed: ', registrationError);
                  });
              });
            }
          `}
        </Script>
      </body>
    </html>
  );
}
