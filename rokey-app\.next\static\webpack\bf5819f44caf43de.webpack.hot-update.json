{"c": ["app/layout", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/AcademicCapIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MapIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/AcademicCapIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/BeakerIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/ChartBarIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/DocumentTextIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/HomeIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/KeyIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/MapIcon.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CRoKey%20App%5C%5Crokey-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CRoKey%20App%5C%5Crokey-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CRoKey%20App%5C%5Crokey-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CRoKey%20App%5C%5Crokey-app%5C%5Csrc%5C%5Ccomponents%5C%5CDocumentTitleUpdater.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CRoKey%20App%5C%5Crokey-app%5C%5Csrc%5C%5Ccomponents%5C%5CLayoutContent.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CRoKey%20App%5C%5Crokey-app%5C%5Csrc%5C%5Ccontexts%5C%5CNavigationContext.tsx%22%2C%22ids%22%3A%5B%22NavigationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CRoKey%20App%5C%5Crokey-app%5C%5Csrc%5C%5Ccontexts%5C%5CSidebarContext.tsx%22%2C%22ids%22%3A%5B%22SidebarProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CRoKey%20App%5C%5Crokey-app%5C%5Csrc%5C%5Cstyles%5C%5Cdesign-system.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./src/components/LayoutContent.tsx", "(app-pages-browser)/./src/components/Navbar.tsx", "(app-pages-browser)/./src/components/OptimisticLoadingScreen.tsx", "(app-pages-browser)/./src/components/Sidebar.tsx", "(app-pages-browser)/./src/contexts/NavigationContext.tsx", "(app-pages-browser)/./src/hooks/useAdvancedPreloading.ts", "(app-pages-browser)/./src/hooks/usePredictiveNavigation.ts", "(app-pages-browser)/./src/hooks/useRoutePrefetch.ts"]}