"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/RoutingVisualization.tsx":
/*!*********************************************************!*\
  !*** ./src/components/landing/RoutingVisualization.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RoutingVisualization)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,CpuChipIcon,PencilIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,CpuChipIcon,PencilIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,CpuChipIcon,PencilIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CodeBracketIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,CpuChipIcon,PencilIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,CpuChipIcon,PencilIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,CpuChipIcon,PencilIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,CpuChipIcon,PencilIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst routingExamples = [\n    {\n        id: 1,\n        prompt: \"Solve this complex math problem: 2x + 5 = 15\",\n        role: \"logic_reasoning\",\n        roleName: \"Logic & Reasoning\",\n        model: \"GPT-4o\",\n        provider: \"OpenAI\",\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        color: \"text-blue-400\",\n        bgColor: \"bg-blue-500/10\",\n        borderColor: \"border-blue-500/20\",\n        glowColor: \"shadow-blue-500/50\"\n    },\n    {\n        id: 2,\n        prompt: \"Write a blog post about AI trends\",\n        role: \"writing\",\n        roleName: \"Writing & Content Creation\",\n        model: \"GPT-4o\",\n        provider: \"OpenAI\",\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        color: \"text-purple-400\",\n        bgColor: \"bg-purple-500/10\",\n        borderColor: \"border-purple-500/20\",\n        glowColor: \"shadow-purple-500/50\"\n    },\n    {\n        id: 3,\n        prompt: \"Build a React component with TypeScript\",\n        role: \"coding_frontend\",\n        roleName: \"Frontend Development\",\n        model: \"Claude 4 Opus\",\n        provider: \"Anthropic\",\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        color: \"text-green-400\",\n        bgColor: \"bg-green-500/10\",\n        borderColor: \"border-green-500/20\",\n        glowColor: \"shadow-green-500/50\"\n    },\n    {\n        id: 4,\n        prompt: \"Summarize this research paper\",\n        role: \"research_synthesis\",\n        roleName: \"Research & Analysis\",\n        model: \"DeepSeek R1 0528\",\n        provider: \"DeepSeek\",\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        color: \"text-cyan-400\",\n        bgColor: \"bg-cyan-500/10\",\n        borderColor: \"border-cyan-500/20\",\n        glowColor: \"shadow-cyan-500/50\"\n    }\n];\nfunction RoutingVisualization() {\n    _s();\n    const [activeExample, setActiveExample] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [isAnimating, setIsAnimating] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"RoutingVisualization.useEffect\": ()=>{\n            const interval = setInterval({\n                \"RoutingVisualization.useEffect.interval\": ()=>{\n                    setIsAnimating(true);\n                    setTimeout({\n                        \"RoutingVisualization.useEffect.interval\": ()=>{\n                            setActiveExample({\n                                \"RoutingVisualization.useEffect.interval\": (prev)=>(prev + 1) % routingExamples.length\n                            }[\"RoutingVisualization.useEffect.interval\"]);\n                            setIsAnimating(false);\n                        }\n                    }[\"RoutingVisualization.useEffect.interval\"], 500);\n                }\n            }[\"RoutingVisualization.useEffect.interval\"], 5000); // Slower transition for better viewing\n            return ({\n                \"RoutingVisualization.useEffect\": ()=>clearInterval(interval)\n            })[\"RoutingVisualization.useEffect\"];\n        }\n    }[\"RoutingVisualization.useEffect\"], []);\n    const currentExample = routingExamples[activeExample];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"py-20 bg-black relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"2edd7b34c21e4641\",\n                children: \"@-webkit-keyframes flowCurrent{0%{-webkit-transform:translatex(-100%);transform:translatex(-100%);opacity:0}20%{opacity:1}80%{opacity:1}100%{-webkit-transform:translatex(200%);transform:translatex(200%);opacity:0}}@-moz-keyframes flowCurrent{0%{-moz-transform:translatex(-100%);transform:translatex(-100%);opacity:0}20%{opacity:1}80%{opacity:1}100%{-moz-transform:translatex(200%);transform:translatex(200%);opacity:0}}@-o-keyframes flowCurrent{0%{-o-transform:translatex(-100%);transform:translatex(-100%);opacity:0}20%{opacity:1}80%{opacity:1}100%{-o-transform:translatex(200%);transform:translatex(200%);opacity:0}}@keyframes flowCurrent{0%{-webkit-transform:translatex(-100%);-moz-transform:translatex(-100%);-o-transform:translatex(-100%);transform:translatex(-100%);opacity:0}20%{opacity:1}80%{opacity:1}100%{-webkit-transform:translatex(200%);-moz-transform:translatex(200%);-o-transform:translatex(200%);transform:translatex(200%);opacity:0}}.current-flow.jsx-2edd7b34c21e4641{-webkit-animation:flowCurrent 3s ease-in-out infinite;-moz-animation:flowCurrent 3s ease-in-out infinite;-o-animation:flowCurrent 3s ease-in-out infinite;animation:flowCurrent 3s ease-in-out infinite}.current-flow-delayed.jsx-2edd7b34c21e4641{-webkit-animation:flowCurrent 3s ease-in-out infinite;-moz-animation:flowCurrent 3s ease-in-out infinite;-o-animation:flowCurrent 3s ease-in-out infinite;animation:flowCurrent 3s ease-in-out infinite;-webkit-animation-delay:1.5s;-moz-animation-delay:1.5s;-o-animation-delay:1.5s;animation-delay:1.5s}\"\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 bg-gradient-to-br from-gray-900/30 to-black\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundImage: \"\\n              linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),\\n              linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)\\n            \",\n                            backgroundSize: '60px 60px'\n                        },\n                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 opacity-20\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute top-1/4 left-1/4 w-96 h-96 bg-[#ff6b35]/5 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute bottom-1/4 right-1/4 w-96 h-96 bg-[#f7931e]/5 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.h2, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"text-4xl sm:text-5xl font-bold text-white mb-6\",\n                                children: \"Introducing the AI Gateway Pattern\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.p, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    delay: 0.1\n                                },\n                                className: \"text-xl text-gray-300 max-w-3xl mx-auto\",\n                                children: \"Watch how RouKey intelligently routes your requests through our unified gateway to the perfect AI model\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"relative max-w-7xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 bg-gradient-to-r from-[#ff6b35]/5 via-transparent to-green-500/5 rounded-3xl blur-3xl\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"relative grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-16 items-start\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: -50\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        transition: {\n                                            duration: 0.6\n                                        },\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-xl font-semibold text-white mb-2\",\n                                                        children: \"User Prompt\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-sm text-gray-400\",\n                                                        children: \"Your request enters RouKey\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"bg-slate-800 border-2 \".concat(currentExample.borderColor, \" rounded-xl p-6 relative \").concat(currentExample.bgColor, \" shadow-lg \").concat(currentExample.glowColor),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 rounded-xl border-2 border-dashed border-gray-500 opacity-30 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"relative z-10\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-sm text-gray-400 mb-3\",\n                                                                children: \"Incoming Request\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 189,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-white font-medium mb-4 text-lg leading-relaxed transition-all duration-500\",\n                                                                children: [\n                                                                    '\"',\n                                                                    currentExample.prompt,\n                                                                    '\"'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 190,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"flex items-center text-sm text-gray-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-2 h-2 bg-[#ff6b35] rounded-full mr-2 animate-pulse\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 194,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    \"Analyzing prompt...\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 193,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"flex space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-1 h-8 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 202,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.2s'\n                                                                    },\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-1 h-6 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 203,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.4s'\n                                                                    },\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-1 h-4 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 204,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute right-0 top-1/2 transform translate-x-full -translate-y-1/2 hidden lg:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"relative w-16 h-px overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 bg-gradient-to-r from-[#ff6b35] to-transparent opacity-40\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute top-0 left-0 w-8 h-px bg-gradient-to-r from-transparent via-white to-[#ff6b35] current-flow shadow-lg shadow-orange-500/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 213,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute top-0 left-0 w-4 h-px bg-white current-flow-delayed shadow-sm shadow-white/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 0.3\n                                        },\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-xl font-semibold text-white mb-2\",\n                                                        children: \"Intelligent Role Classification\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-sm text-gray-400\",\n                                                        children: \"AI analyzes & routes to optimal model\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"bg-slate-800 border-2 border-slate-700 rounded-xl p-6 relative shadow-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 rounded-xl border-2 border-dashed border-[#ff6b35] opacity-40 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"relative z-10 space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center mb-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-16 h-16 bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-lg shadow-orange-500/30\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                            className: \"w-8 h-8 text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                            lineNumber: 241,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 240,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-sm text-gray-400\",\n                                                                        children: \"RouKey Smart Classifier\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 243,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 239,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"\".concat(currentExample.bgColor, \" \").concat(currentExample.borderColor, \" border-2 rounded-lg p-4 text-center shadow-lg \").concat(currentExample.glowColor),\n                                                                children: [\n                                                                    /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().createElement(currentExample.icon, {\n                                                                        className: \"h-6 w-6 \".concat(currentExample.color, \" mx-auto mb-2\")\n                                                                    }),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-xs text-gray-400 mb-1\",\n                                                                        children: \"Classified as\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 251,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"font-semibold text-sm \".concat(currentExample.color),\n                                                                        children: currentExample.roleName\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 252,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-xs text-gray-500 mt-1\",\n                                                                        children: [\n                                                                            \"Role ID: \",\n                                                                            currentExample.role\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 255,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 247,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"grid grid-cols-2 gap-2 text-xs\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"bg-slate-700 rounded p-2 text-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-green-400 mb-1\",\n                                                                                children: \"✓ Context Analysis\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 263,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-gray-500\",\n                                                                                children: \"Complete\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 264,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 262,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"bg-slate-700 rounded p-2 text-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-green-400 mb-1\",\n                                                                                children: \"✓ Role Matching\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 267,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-gray-500\",\n                                                                                children: \"Complete\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 268,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 266,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 261,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute left-0 top-1/2 transform -translate-x-1/2 -translate-y-1/2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"flex space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.6s'\n                                                                    },\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-1 h-4 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 276,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.4s'\n                                                                    },\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-1 h-6 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 277,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.2s'\n                                                                    },\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-1 h-8 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 278,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 275,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"flex space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-1 h-8 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 283,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.2s'\n                                                                    },\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-1 h-6 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 284,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.4s'\n                                                                    },\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-1 h-4 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 285,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute left-0 top-1/2 transform -translate-x-full -translate-y-1/2 hidden lg:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"relative w-16 h-px overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 bg-gradient-to-r from-transparent via-[#ff6b35] to-transparent opacity-40\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute top-0 left-0 w-8 h-px bg-gradient-to-r from-transparent via-white to-[#ff6b35] current-flow shadow-lg shadow-orange-500/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute top-0 left-0 w-4 h-px bg-white current-flow-delayed shadow-sm shadow-white/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute right-0 top-1/2 transform translate-x-full -translate-y-1/2 hidden lg:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"relative w-16 h-px overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 bg-gradient-to-r from-[#ff6b35] to-transparent opacity-40\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute top-0 left-0 w-8 h-px bg-gradient-to-r from-transparent via-white to-[#ff6b35] current-flow shadow-lg shadow-orange-500/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute top-0 left-0 w-4 h-px bg-white current-flow-delayed shadow-sm shadow-white/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: 50\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 0.6\n                                        },\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-xl font-semibold text-white mb-2\",\n                                                        children: \"Optimal Model Selection\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-sm text-gray-400\",\n                                                        children: \"Routed to the perfect model\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"bg-gradient-to-br from-slate-800 to-slate-900 border-2 border-green-500/30 rounded-xl p-6 relative shadow-xl shadow-green-500/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 rounded-xl border-2 border-dashed border-green-400 opacity-50 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"relative z-10\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center mb-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-lg shadow-green-500/30\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                            className: \"w-8 h-8 text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                            lineNumber: 328,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 327,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-lg font-bold text-white mb-1\",\n                                                                        children: currentExample.model\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 330,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-sm text-green-400 font-medium\",\n                                                                        children: currentExample.provider\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 331,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 326,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"\".concat(currentExample.bgColor, \" \").concat(currentExample.borderColor, \" border-2 rounded-lg p-4 text-center mb-4 shadow-lg \").concat(currentExample.glowColor),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-xs text-gray-400 mb-1\",\n                                                                        children: \"Assigned Role\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 336,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"font-semibold text-sm \".concat(currentExample.color),\n                                                                        children: currentExample.roleName\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 337,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"grid grid-cols-2 gap-2 text-xs\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"bg-slate-700 rounded p-2 text-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-green-400 mb-1\",\n                                                                                children: \"⚡ Speed\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 345,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-white font-medium\",\n                                                                                children: \"Optimal\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 346,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 344,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"bg-slate-700 rounded p-2 text-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-blue-400 mb-1\",\n                                                                                children: \"\\uD83C\\uDFAF Accuracy\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 349,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-white font-medium\",\n                                                                                children: \"High\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 350,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 348,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"mt-4 flex items-center justify-center text-green-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"w-4 h-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 356,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-sm font-medium\",\n                                                                        children: \"Route Established\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 357,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 355,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute left-0 top-1/2 transform -translate-x-1/2 -translate-y-1/2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"flex space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.8s'\n                                                                    },\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-1 h-4 bg-green-400 rounded-full animate-pulse shadow-lg shadow-green-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 364,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.6s'\n                                                                    },\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-1 h-6 bg-green-400 rounded-full animate-pulse shadow-lg shadow-green-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 365,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.4s'\n                                                                    },\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-1 h-8 bg-green-400 rounded-full animate-pulse shadow-lg shadow-green-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 366,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute left-0 top-1/2 transform -translate-x-full -translate-y-1/2 hidden lg:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"relative w-16 h-px overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 bg-gradient-to-r from-transparent via-green-400 to-green-400 opacity-40\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute top-0 left-0 w-8 h-px bg-gradient-to-r from-transparent via-white to-green-400 current-flow shadow-lg shadow-green-500/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute top-0 left-0 w-4 h-px bg-white current-flow-delayed shadow-sm shadow-white/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"flex justify-center mt-12 space-x-2\",\n                                children: routingExamples.map((example, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveExample(index),\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-3 h-3 rounded-full transition-all duration-200 \".concat(index === activeExample ? 'bg-[#ff6b35] scale-125' : 'bg-gray-500 hover:bg-gray-400')\n                                    }, example.id, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-8 mt-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-12 h-12 bg-[#ff6b35]/20 rounded-lg flex items-center justify-center mx-auto mb-4 shadow-lg shadow-orange-500/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-6 w-6 text-[#ff6b35]\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-lg font-semibold text-white mb-2\",\n                                        children: \"Role-Based Classification\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-gray-400\",\n                                        children: \"RouKey Smart Classifier analyzes context and classifies requests into 15+ specialized roles\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-12 h-12 bg-gray-700/50 rounded-lg flex items-center justify-center mx-auto mb-4 shadow-lg shadow-gray-500/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-6 w-6 text-gray-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-lg font-semibold text-white mb-2\",\n                                        children: \"Smart Model Matching\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-gray-400\",\n                                        children: \"Each role routes to your pre-configured optimal model for maximum performance\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-12 h-12 bg-[#f7931e]/20 rounded-lg flex items-center justify-center mx-auto mb-4 shadow-lg shadow-orange-400/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-6 w-6 text-[#f7931e]\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-lg font-semibold text-white mb-2\",\n                                        children: \"Contextual Continuity\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-gray-400\",\n                                        children: \"Maintains conversation context and role consistency across multi-turn interactions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-12 h-12 bg-white/10 rounded-lg flex items-center justify-center mx-auto mb-4 shadow-lg shadow-white/10\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-lg font-semibold text-white mb-2\",\n                                        children: \"Fallback Protection\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-gray-400\",\n                                        children: \"Automatic fallback to default general chat model when no role match is found\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 399,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            delay: 0.2\n                        },\n                        className: \"mt-16 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-xl font-semibold text-white mb-4\",\n                                children: \"15+ Built-in Role Types\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-gray-400 mb-8 max-w-2xl mx-auto\",\n                                children: \"Each role is optimized for specific use cases, with the ability to create custom roles for your unique workflows\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 447,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 max-w-5xl mx-auto\",\n                                children: [\n                                    [\n                                        {\n                                            name: 'General Chat',\n                                            color: 'text-gray-400',\n                                            icon: '💬'\n                                        },\n                                        {\n                                            name: 'Logic & Reasoning',\n                                            color: 'text-blue-400',\n                                            icon: '🧠'\n                                        },\n                                        {\n                                            name: 'Writing & Content',\n                                            color: 'text-purple-400',\n                                            icon: '✍️'\n                                        },\n                                        {\n                                            name: 'Frontend Coding',\n                                            color: 'text-green-400',\n                                            icon: '⚛️'\n                                        },\n                                        {\n                                            name: 'Backend Coding',\n                                            color: 'text-emerald-400',\n                                            icon: '⚙️'\n                                        },\n                                        {\n                                            name: 'Research & Analysis',\n                                            color: 'text-cyan-400',\n                                            icon: '🔬'\n                                        },\n                                        {\n                                            name: 'Summarization',\n                                            color: 'text-yellow-400',\n                                            icon: '📝'\n                                        },\n                                        {\n                                            name: 'Translation',\n                                            color: 'text-pink-400',\n                                            icon: '🌐'\n                                        },\n                                        {\n                                            name: 'Data Extraction',\n                                            color: 'text-indigo-400',\n                                            icon: '📊'\n                                        },\n                                        {\n                                            name: 'Brainstorming',\n                                            color: 'text-orange-400',\n                                            icon: '💡'\n                                        },\n                                        {\n                                            name: 'Education',\n                                            color: 'text-red-400',\n                                            icon: '🎓'\n                                        },\n                                        {\n                                            name: 'Audio Transcription',\n                                            color: 'text-teal-400',\n                                            icon: '🎵'\n                                        }\n                                    ].map((role, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"bg-slate-800 rounded-lg p-4 border border-slate-700 hover:border-slate-600 transition-all duration-200 group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-2xl mb-2 group-hover:scale-110 transition-transform duration-200\",\n                                                    children: role.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-sm font-medium \".concat(role.color),\n                                                    children: role.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, role.name, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                            lineNumber: 465,\n                                            columnNumber: 15\n                                        }, this)),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 border-2 border-dashed border-[#ff6b35]/30 rounded-lg p-4 hover:border-[#ff6b35]/50 transition-all duration-200 group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-2xl mb-2 group-hover:scale-110 transition-transform duration-200\",\n                                                children: \"\\uD83C\\uDFAF\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-sm font-medium text-[#ff6b35]\",\n                                                children: \"Custom Roles\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-xs text-gray-500 mt-1\",\n                                                children: \"Create your own\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 471,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 450,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 439,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            delay: 0.4\n                        },\n                        className: \"mt-20 bg-gradient-to-br from-gray-900/50 to-black/50 rounded-2xl p-8 border border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-2xl font-bold text-white mb-4\",\n                                        children: \"Create Custom Roles for Your Workflow\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 488,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-gray-400 max-w-3xl mx-auto\",\n                                        children: \"Beyond our 15+ built-in roles, RouKey empowers you to create custom roles tailored to your specific needs. Define role patterns, assign optimal models, and let our AI classifier automatically route requests.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 487,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-16 h-16 bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg shadow-orange-500/30\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-2xl text-white font-bold\",\n                                                    children: \"1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-lg font-semibold text-white mb-3\",\n                                                children: \"Define Role Pattern\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-gray-400 text-sm leading-relaxed\",\n                                                children: \"Describe the types of requests this role should handle. Our AI learns from your examples and keywords.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"mt-4 bg-gray-800 rounded-lg p-3 text-left border border-gray-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-xs text-gray-500 mb-1\",\n                                                        children: \"Example Pattern:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 506,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-sm text-[#ff6b35] font-mono\",\n                                                        children: '\"SQL queries, database optimization, schema design\"'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 505,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 497,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-16 h-16 bg-gradient-to-br from-gray-700 to-gray-800 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg shadow-gray-500/30\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-2xl text-white font-bold\",\n                                                    children: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-lg font-semibold text-white mb-3\",\n                                                children: \"Assign Optimal Model\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 518,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-gray-400 text-sm leading-relaxed\",\n                                                children: \"Choose which model performs best for this role from your available providers and configurations.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 519,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"mt-4 bg-gray-800 rounded-lg p-3 text-left border border-gray-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-xs text-gray-500 mb-1\",\n                                                        children: \"Model Selection:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 523,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-sm text-white font-medium\",\n                                                        children: \"Claude 3.5 Sonnet → Database Expert\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 524,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-16 h-16 bg-gradient-to-br from-[#f7931e] to-[#ff6b35] rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg shadow-orange-400/30\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-2xl text-white font-bold\",\n                                                    children: \"3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 532,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-lg font-semibold text-white mb-3\",\n                                                children: \"Automatic Routing\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 535,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-gray-400 text-sm leading-relaxed\",\n                                                children: \"Our RouKey Smart Classifier automatically detects and routes matching requests to your custom role.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 536,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"mt-4 bg-gray-800 rounded-lg p-3 text-left border border-gray-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-xs text-gray-500 mb-1\",\n                                                        children: \"Auto-Detection:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 540,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-sm text-[#f7931e]\",\n                                                        children: \"✓ Pattern Recognition Active\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 541,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 539,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 531,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 495,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center mt-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"inline-flex items-center bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-lg px-6 py-3 text-white font-medium shadow-lg shadow-orange-500/30\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-2edd7b34c21e4641\",\n                                        children: \"\\uD83D\\uDE80 Start Creating Custom Roles\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 550,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                    lineNumber: 549,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 548,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 480,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\n_s(RoutingVisualization, \"SlEVdab/r4hG20wt70ej7grkDC0=\");\n_c = RoutingVisualization;\nvar _c;\n$RefreshReg$(_c, \"RoutingVisualization\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/RoutingVisualization.tsx\n"));

/***/ })

});