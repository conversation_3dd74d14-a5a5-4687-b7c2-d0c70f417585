'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { ArrowRightIcon, PlayIcon, SparklesIcon, BoltIcon, ShieldCheckIcon } from '@heroicons/react/24/outline';
import { useState, useEffect } from 'react';

export default function HeroSection() {
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const [typewriterText, setTypewriterText] = useState('');
  const [currentWordIndex, setCurrentWordIndex] = useState(0);

  const words = ['Intelligent', 'Powerful', 'Seamless', 'Revolutionary'];
  const fullText = 'RouKey: The Ultimate AI Gateway';

  useEffect(() => {
    // Typewriter effect for the main title
    let i = 0;
    const typeInterval = setInterval(() => {
      if (i < fullText.length) {
        setTypewriterText(fullText.slice(0, i + 1));
        i++;
      } else {
        clearInterval(typeInterval);
      }
    }, 80);

    // Rotating words effect
    const wordInterval = setInterval(() => {
      setCurrentWordIndex((prev) => (prev + 1) % words.length);
    }, 2000);

    return () => {
      clearInterval(typeInterval);
      clearInterval(wordInterval);
    };
  }, []);

  return (
    <section className="relative min-h-screen bg-black overflow-hidden">
      {/* Animated Grid Background */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black"></div>
        <div
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: `
              linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px',
            animation: 'grid-move 20s linear infinite'
          }}
        ></div>

        {/* Smoky overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-black opacity-60"></div>

        {/* Floating orbs */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-orange-500/20 to-red-500/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-orange-600/10 to-yellow-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-32 pb-20">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center min-h-[80vh]">
          {/* Left Column - Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 1 }}
            className="text-left"
          >
            {/* Typewriter Title */}
            <div className="mb-8">
              <h1 className="text-6xl md:text-7xl font-bold text-white mb-4 leading-tight">
                {typewriterText}
                <span className="animate-pulse text-[#ff6b35]">|</span>
              </h1>

              <div className="text-4xl md:text-5xl font-bold mb-6">
                <span className="text-gray-300">Make AI </span>
                <span
                  className="text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e] transition-all duration-500"
                  key={currentWordIndex}
                >
                  {words[currentWordIndex]}
                </span>
              </div>
            </div>

            <p className="text-xl md:text-2xl text-gray-300 mb-12 leading-relaxed max-w-2xl">
              Route requests to the perfect AI model from{' '}
              <span className="text-[#ff6b35] font-semibold">300+ models</span> across{' '}
              <span className="text-[#ff6b35] font-semibold">15+ providers</span>.
              Zero configuration, maximum performance.
            </p>

            {/* Feature highlights */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-[#ff6b35]/20 rounded-lg">
                  <BoltIcon className="h-6 w-6 text-[#ff6b35]" />
                </div>
                <span className="text-gray-300 font-medium">Lightning Fast</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-[#ff6b35]/20 rounded-lg">
                  <ShieldCheckIcon className="h-6 w-6 text-[#ff6b35]" />
                </div>
                <span className="text-gray-300 font-medium">Enterprise Ready</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-[#ff6b35]/20 rounded-lg">
                  <SparklesIcon className="h-6 w-6 text-[#ff6b35]" />
                </div>
                <span className="text-gray-300 font-medium">AI-Powered</span>
              </div>
            </div>

            {/* CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
              className="flex flex-col sm:flex-row gap-6"
            >
              <Link
                href="/auth/signup"
                className="group inline-flex items-center px-8 py-4 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white font-bold rounded-xl hover:shadow-2xl hover:shadow-orange-500/25 hover:scale-105 transition-all duration-300 text-lg relative overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-[#f7931e] to-[#ff6b35] opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <span className="relative z-10">Start Building Now</span>
                <ArrowRightIcon className="ml-3 h-5 w-5 relative z-10 group-hover:translate-x-1 transition-transform duration-300" />
              </Link>

              <button
                onClick={() => setIsVideoPlaying(true)}
                className="inline-flex items-center px-8 py-4 border-2 border-gray-600 text-gray-300 font-bold rounded-xl hover:border-[#ff6b35] hover:text-white hover:bg-[#ff6b35]/10 transition-all duration-300 text-lg group"
              >
                <PlayIcon className="mr-3 h-5 w-5 group-hover:text-[#ff6b35] transition-colors duration-300" />
                Watch Demo
              </button>
            </motion.div>

            {/* Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7 }}
              className="flex flex-col sm:flex-row gap-8 mt-12"
            >
              <div className="text-left">
                <div className="text-3xl font-bold text-white">300+</div>
                <div className="text-gray-400">AI Models</div>
              </div>
              <div className="text-left">
                <div className="text-3xl font-bold text-white">99.9%</div>
                <div className="text-gray-400">Uptime</div>
              </div>
              <div className="text-left">
                <div className="text-3xl font-bold text-white">&lt;500ms</div>
                <div className="text-gray-400">Response Time</div>
              </div>
            </motion.div>
          </motion.div>

          {/* Right Column - Premium Visual */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 1, delay: 0.3 }}
            className="relative"
          >
            {/* Main Dashboard Mockup - Dark Theme */}
            <div className="relative bg-gray-900 rounded-2xl shadow-2xl border border-gray-700 overflow-hidden backdrop-blur-sm">
              <div className="bg-gray-800 px-4 py-3 border-b border-gray-700 flex items-center space-x-2">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <div className="ml-4 text-sm text-gray-300">RouKey Dashboard</div>
              </div>

              <div className="p-6">
                {/* Premium routing visualization */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-gradient-to-r from-[#ff6b35]/20 to-[#f7931e]/20 rounded-lg border border-[#ff6b35]/30">
                    <span className="text-sm font-medium text-white">Intelligent Routing Active</span>
                    <div className="w-3 h-3 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50"></div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="p-4 bg-gray-800 rounded-lg border border-gray-700">
                      <div className="text-xs text-gray-400 mb-1">Active Models</div>
                      <div className="text-2xl font-bold text-white">12</div>
                    </div>
                    <div className="p-4 bg-gray-800 rounded-lg border border-gray-700">
                      <div className="text-xs text-gray-400 mb-1">Requests Today</div>
                      <div className="text-2xl font-bold text-white">2,847</div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-gray-800 rounded-lg border border-gray-700">
                      <span className="text-gray-300">GPT-4 (Logic)</span>
                      <span className="text-[#ff6b35] font-medium">Active</span>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-gray-800 rounded-lg border border-gray-700">
                      <span className="text-gray-300">Claude 4 (Writing)</span>
                      <span className="text-[#ff6b35] font-medium">Active</span>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-gray-800 rounded-lg border border-gray-700">
                      <span className="text-gray-300">Gemini 2.5 (General)</span>
                      <span className="text-[#ff6b35] font-medium">Active</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Premium Floating elements */}
            <motion.div
              animate={{ y: [0, -10, 0] }}
              transition={{ duration: 3, repeat: Infinity }}
              className="absolute -top-4 -right-4 bg-gray-900 border border-gray-700 rounded-lg shadow-2xl p-4 backdrop-blur-sm"
            >
              <div className="text-xs text-gray-400 mb-1">Cost Saved</div>
              <div className="text-xl font-bold text-[#ff6b35]">$1,247</div>
            </motion.div>

            <motion.div
              animate={{ y: [0, 10, 0] }}
              transition={{ duration: 4, repeat: Infinity, delay: 1 }}
              className="absolute -bottom-4 -left-4 bg-gray-900 border border-gray-700 rounded-lg shadow-2xl p-4 backdrop-blur-sm"
            >
              <div className="text-xs text-gray-400 mb-1">Response Time</div>
              <div className="text-xl font-bold text-[#ff6b35]">342ms</div>
            </motion.div>

            {/* Glowing connection lines */}
            <div className="absolute inset-0 pointer-events-none">
              <svg className="w-full h-full" viewBox="0 0 400 300">
                <defs>
                  <linearGradient id="glowGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#ff6b35" stopOpacity="0" />
                    <stop offset="50%" stopColor="#ff6b35" stopOpacity="1" />
                    <stop offset="100%" stopColor="#f7931e" stopOpacity="0" />
                  </linearGradient>
                </defs>
                <motion.path
                  d="M50,150 Q200,50 350,150"
                  stroke="url(#glowGradient)"
                  strokeWidth="2"
                  fill="none"
                  initial={{ pathLength: 0 }}
                  animate={{ pathLength: 1 }}
                  transition={{ duration: 2, repeat: Infinity, repeatType: "loop" }}
                />
              </svg>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Add CSS for grid animation */}
      <style jsx>{`
        @keyframes grid-move {
          0% { transform: translate(0, 0); }
          100% { transform: translate(50px, 50px); }
        }
      `}</style>
    </section>
  );
}
