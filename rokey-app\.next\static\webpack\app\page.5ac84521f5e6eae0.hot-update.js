"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/FeaturesSection.tsx":
/*!****************************************************!*\
  !*** ./src/components/landing/FeaturesSection.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeaturesSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst features = [\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        title: \"Intelligent Role Routing\",\n        description: \"AI automatically classifies your prompts and routes them to the best model for each specific task - writing, coding, logic, or analysis.\",\n        color: \"text-blue-600\",\n        bgColor: \"bg-blue-50\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: \"Automatic Failover\",\n        description: \"Never worry about API limits or downtime. RouKey automatically retries failed requests with alternative models in your configuration.\",\n        color: \"text-green-600\",\n        bgColor: \"bg-green-50\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"Enterprise Security\",\n        description: \"Military-grade AES-256-GCM encryption for all API keys. Your credentials are stored securely and never exposed.\",\n        color: \"text-purple-600\",\n        bgColor: \"bg-purple-50\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Comprehensive Analytics\",\n        description: \"Track costs, performance, token usage, and success rates across all your models with real-time dashboards and insights.\",\n        color: \"text-orange-600\",\n        bgColor: \"bg-orange-50\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Performance Optimization\",\n        description: \"First-token tracking, latency monitoring, and intelligent caching ensure blazing-fast response times under 500ms.\",\n        color: \"text-indigo-600\",\n        bgColor: \"bg-indigo-50\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"Cost Optimization\",\n        description: \"Automatic free-tier detection, budget alerts, and cost tracking help you optimize spending across all AI providers.\",\n        color: \"text-emerald-600\",\n        bgColor: \"bg-emerald-50\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"300+ AI Models\",\n        description: \"Access the latest models from OpenAI, Google, Anthropic, DeepSeek, xAI, Meta, and hundreds more through one unified API.\",\n        color: \"text-red-600\",\n        bgColor: \"bg-red-50\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        title: \"Advanced Routing Strategies\",\n        description: \"Load balancing, complexity-based routing, strict fallback sequences, and custom rules for enterprise-grade control.\",\n        color: \"text-cyan-600\",\n        bgColor: \"bg-cyan-50\"\n    }\n];\nfunction FeaturesSection() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"features\",\n        className: \"py-24 bg-gradient-to-br from-gray-900 to-black relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-10\",\n                        style: {\n                            backgroundImage: \"\\n              linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),\\n              linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)\\n            \",\n                            backgroundSize: '80px 80px'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 right-0 w-96 h-96 bg-[#ff6b35]/5 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 left-0 w-96 h-96 bg-[#f7931e]/5 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-left mb-20 max-w-4xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.h2, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"text-5xl sm:text-6xl font-bold text-white mb-6 leading-tight\",\n                                children: [\n                                    \"Enterprise-Grade\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]\",\n                                        children: [\n                                            ' ',\n                                            \"AI Infrastructure\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.p, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    delay: 0.2\n                                },\n                                className: \"text-xl text-gray-300 max-w-3xl leading-relaxed\",\n                                children: \"RouKey provides military-grade security, intelligent routing, and comprehensive analytics for the most demanding AI workloads. Built for scale, designed for performance.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-6xl mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 50\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"group relative h-[600px] flex items-center justify-center\",\n                            style: {\n                                perspective: '1000px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-gradient-to-r from-[#ff6b35]/10 via-transparent to-[#f7931e]/10 rounded-3xl blur-3xl group-hover:from-[#ff6b35]/20 group-hover:to-[#f7931e]/20 transition-all duration-700\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this),\n                                features.map((feature, index)=>{\n                                    // Calculate positions for stacked and spread states\n                                    const stackedTransform = \"translateX(\".concat(index * 8, \"px) translateY(\").concat(index * 4, \"px) rotateY(\").concat(index * 2, \"deg)\");\n                                    const spreadTransform = (()=>{\n                                        const angle = (index - 3.5) * 15; // Spread in a fan\n                                        const radius = 280;\n                                        const x = Math.sin(angle * Math.PI / 180) * radius;\n                                        const y = Math.cos(angle * Math.PI / 180) * radius - radius + 100;\n                                        return \"translateX(\".concat(x, \"px) translateY(\").concat(y, \"px) rotateY(\").concat(angle, \"deg) rotateX(\").concat(Math.abs(angle) * 0.3, \"deg)\");\n                                    })();\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                        className: \"absolute w-80 h-96 cursor-pointer\",\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.8\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            scale: 1,\n                                            transform: stackedTransform\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            scale: 1\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        transition: {\n                                            delay: index * 0.1,\n                                            duration: 0.6,\n                                            type: \"spring\",\n                                            stiffness: 100\n                                        },\n                                        style: {\n                                            zIndex: features.length - index,\n                                            transformStyle: 'preserve-3d'\n                                        },\n                                        whileHover: {\n                                            scale: 1.05,\n                                            zIndex: 50,\n                                            transition: {\n                                                duration: 0.3\n                                            }\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-full bg-gradient-to-br from-gray-800/90 to-gray-900/90 backdrop-blur-xl rounded-3xl border border-gray-700 hover:border-[#ff6b35]/50 transition-all duration-500 shadow-2xl hover:shadow-orange-500/20 relative overflow-hidden group/card\",\n                                            style: {\n                                                transform: 'translateZ(0)',\n                                                backfaceVisibility: 'hidden'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 opacity-5\",\n                                                    style: {\n                                                        backgroundImage: \"\\n                          linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),\\n                          linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)\\n                        \",\n                                                        backgroundSize: '20px 20px'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10 p-8 h-full flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-20 h-20 \".concat(index % 3 === 0 ? 'bg-gradient-to-br from-[#ff6b35] to-[#f7931e]' : index % 3 === 1 ? 'bg-gradient-to-br from-gray-600 to-gray-700' : 'bg-gradient-to-br from-[#f7931e] to-[#ff6b35]', \" rounded-2xl flex items-center justify-center mb-6 group-hover/card:scale-110 transition-transform duration-300 shadow-lg \").concat(index % 3 === 0 || index % 3 === 2 ? 'shadow-orange-500/30' : 'shadow-gray-500/30'),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                                className: \"h-10 w-10 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                lineNumber: 196,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-2xl font-bold text-white mb-4 group-hover/card:text-[#ff6b35] transition-colors duration-300\",\n                                                            children: feature.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-300 leading-relaxed flex-1 text-lg\",\n                                                            children: feature.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-6 h-1 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-full transform scale-x-0 group-hover/card:scale-x-100 transition-transform duration-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 rounded-3xl bg-gradient-to-r from-[#ff6b35]/10 to-[#f7931e]/10 opacity-0 group-hover/card:opacity-100 transition-opacity duration-500 pointer-events-none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 rounded-3xl bg-gradient-to-r from-transparent via-white/5 to-transparent transform -skew-x-12 -translate-x-full group-hover/card:translate-x-full transition-transform duration-1000 pointer-events-none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, feature.title, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 17\n                                    }, this);\n                                }),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        delay: 1\n                                    },\n                                    className: \"absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-16 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-800/80 backdrop-blur-sm rounded-full px-6 py-3 border border-gray-700\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-[#ff6b35]\",\n                                                    children: \"Hover\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" to explore features\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: 50\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        className: \"mt-24 flex justify-end\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-2xl bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-3xl p-12 text-white relative overflow-hidden shadow-2xl shadow-orange-500/25\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 opacity-10\",\n                                    style: {\n                                        backgroundImage: \"\\n                  linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),\\n                  linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px)\\n                \",\n                                        backgroundSize: '20px 20px'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative z-10 text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-4xl font-bold mb-6 leading-tight\",\n                                            children: [\n                                                \"Ready to Transform\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Your AI Infrastructure?\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl opacity-90 mb-8 leading-relaxed\",\n                                            children: \"Join the next generation of developers building with RouKey's intelligent AI gateway. Start routing smarter today.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row gap-4 justify-end\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                                    whileHover: {\n                                                        scale: 1.05,\n                                                        boxShadow: \"0 20px 40px rgba(0,0,0,0.3)\"\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.95\n                                                    },\n                                                    className: \"bg-white text-[#ff6b35] px-8 py-4 rounded-xl font-bold hover:bg-gray-50 transition-all duration-300 shadow-lg\",\n                                                    children: \"Start Building Now\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                                    whileHover: {\n                                                        scale: 1.05\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.95\n                                                    },\n                                                    className: \"border-2 border-white text-white px-8 py-4 rounded-xl font-bold hover:bg-white hover:text-[#ff6b35] transition-all duration-300\",\n                                                    children: \"View Documentation\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full blur-xl\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -bottom-4 -left-4 w-32 h-32 bg-white/5 rounded-full blur-2xl\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n_c = FeaturesSection;\nvar _c;\n$RefreshReg$(_c, \"FeaturesSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/FeaturesSection.tsx\n"));

/***/ })

});