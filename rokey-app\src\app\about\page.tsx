'use client';

import { motion } from 'framer-motion';
import {
  BoltIcon,
  ShieldCheckIcon,
  ChartBarIcon,
  CpuChipIcon,
  UserGroupIcon,
  GlobeAltIcon,
  LightBulbIcon,
  RocketLaunchIcon
} from '@heroicons/react/24/outline';
import LandingNavbar from '@/components/landing/LandingNavbar';
import Footer from '@/components/landing/Footer';

const stats = [
  { label: "AI Models Supported", value: "300+" },
  { label: "API Requests Processed", value: "10M+" },
  { label: "Developers Trust Us", value: "5,000+" },
  { label: "Uptime Guarantee", value: "99.9%" }
];

const values = [
  {
    icon: BoltIcon,
    title: "Performance First",
    description: "We obsess over speed, reliability, and efficiency in everything we build."
  },
  {
    icon: ShieldCheckIcon,
    title: "Security by Design",
    description: "Enterprise-grade security isn't an afterthought—it's built into our foundation."
  },
  {
    icon: LightBulbIcon,
    title: "Innovation Driven",
    description: "We're constantly pushing the boundaries of what's possible with AI routing."
  },
  {
    icon: UserGroupIcon,
    title: "Developer Focused",
    description: "Built by developers, for developers. We understand your challenges."
  }
];

const team = [
  {
    name: "<PERSON> <PERSON>",
    role: "CEO & Co-Founder",
    bio: "Former AI researcher at Google. Led the team that built the first intelligent routing algorithms.",
    image: "/api/placeholder/150/150"
  },
  {
    name: "Sarah Johnson",
    role: "CTO & Co-Founder",
    bio: "Ex-OpenAI engineer with 10+ years in distributed systems and AI infrastructure.",
    image: "/api/placeholder/150/150"
  },
  {
    name: "Marcus Rodriguez",
    role: "Head of Engineering",
    bio: "Previously at Stripe, where he scaled payment infrastructure to handle billions of requests.",
    image: "/api/placeholder/150/150"
  },
  {
    name: "Emily Zhang",
    role: "Head of Product",
    bio: "Former product lead at Anthropic, passionate about making AI accessible to all developers.",
    image: "/api/placeholder/150/150"
  }
];

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-white">
      <LandingNavbar />

      <main className="pt-20">
        {/* Hero Section */}
        <section className="py-20 bg-gradient-to-br from-slate-50 to-blue-50">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
                Building the Future of
                <span className="text-[#ff6b35] block">AI Infrastructure</span>
              </h1>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
                RouKey was born from a simple belief: AI should be accessible, reliable, and intelligent. 
                We're on a mission to democratize AI by making it easier for developers to build amazing applications.
              </p>
            </motion.div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="text-center"
                >
                  <div className="text-4xl md:text-5xl font-bold text-[#ff6b35] mb-2">
                    {stat.value}
                  </div>
                  <div className="text-gray-600 font-medium">
                    {stat.label}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Story Section */}
        <section className="py-20 bg-gray-50">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl font-bold text-gray-900 mb-6">Our Story</h2>
              <p className="text-xl text-gray-600">
                How we went from frustrated developers to building the world's most intelligent AI routing platform.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="prose prose-lg mx-auto text-gray-700"
            >
              <p>
                In 2023, our founders were building AI applications and constantly frustrated by the complexity 
                of managing multiple AI providers, dealing with rate limits, and optimizing for cost and performance. 
                Every developer was solving the same problems over and over again.
              </p>
              <p>
                We realized there had to be a better way. What if there was a single API that could intelligently 
                route requests to the best AI model for each specific task? What if developers could focus on 
                building amazing products instead of managing infrastructure?
              </p>
              <p>
                That's when RouKey was born. We started with a simple prototype and quickly realized we were 
                onto something big. Today, we're proud to serve thousands of developers and process millions 
                of AI requests every month.
              </p>
            </motion.div>
          </div>
        </section>

        {/* Values Section */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl font-bold text-gray-900 mb-6">Our Values</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                These principles guide everything we do, from product decisions to how we treat our customers.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
              {values.map((value, index) => (
                <motion.div
                  key={value.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="flex items-start space-x-4"
                >
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-[#ff6b35] rounded-xl flex items-center justify-center">
                      <value.icon className="w-6 h-6 text-white" />
                    </div>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-3">
                      {value.title}
                    </h3>
                    <p className="text-gray-600">
                      {value.description}
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Team Section */}
        <section className="py-20 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl font-bold text-gray-900 mb-6">Meet Our Team</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                We're a team of passionate engineers, researchers, and product builders from leading AI companies.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {team.map((member, index) => (
                <motion.div
                  key={member.name}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white rounded-2xl p-6 shadow-sm text-center"
                >
                  <div className="w-24 h-24 bg-gray-200 rounded-full mx-auto mb-4"></div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">
                    {member.name}
                  </h3>
                  <p className="text-[#ff6b35] font-medium mb-3">
                    {member.role}
                  </p>
                  <p className="text-sm text-gray-600">
                    {member.bio}
                  </p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gray-900">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h2 className="text-4xl font-bold text-white mb-6">
                Join Us on This Journey
              </h2>
              <p className="text-xl text-gray-300 mb-8">
                We're just getting started. Help us build the future of AI infrastructure.
              </p>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-[#ff6b35] text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-[#e55a2b] transition-colors duration-200 shadow-lg"
              >
                Get Started Today
              </motion.button>
            </motion.div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
